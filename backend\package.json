{"name": "restaurant-playlist-backend", "version": "1.0.0", "description": "Backend API para Sistema de Playlist Interativa para Restaurantes", "main": "dist/server.js", "scripts": {"start": "ts-node --transpile-only src/server.ts", "dev": "nodemon --exec \"ts-node --transpile-only src/server.ts\"", "build": "tsc", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "typeorm": "typeorm-ts-node-commonjs", "migration:generate": "npm run typeorm -- migration:generate", "migration:run": "npm run typeorm -- migration:run", "migration:revert": "npm run typeorm -- migration:revert", "lint": "eslint src/**/*.ts", "lint:fix": "eslint src/**/*.ts --fix"}, "dependencies": {"axios": "^1.4.0", "bcryptjs": "^2.4.3", "compression": "^1.7.4", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "express-rate-limit": "^6.8.1", "express-validator": "^7.0.1", "googleapis": "^154.1.0", "helmet": "^7.0.0", "joi": "^17.9.2", "jsonwebtoken": "^9.0.1", "morgan": "^1.10.0", "multer": "^1.4.5-lts.1", "pg": "^8.11.1", "qrcode": "^1.5.4", "redis": "^4.6.7", "socket.io": "^4.7.2", "swagger-jsdoc": "^6.2.8", "swagger-ui-express": "^5.0.0", "typeorm": "^0.3.17", "uuid": "^9.0.0", "winston": "^3.10.0"}, "devDependencies": {"@types/bcryptjs": "^2.4.2", "@types/compression": "^1.7.2", "@types/cors": "^2.8.13", "@types/express": "^4.17.17", "@types/jest": "^29.5.3", "@types/jsonwebtoken": "^9.0.2", "@types/morgan": "^1.9.4", "@types/multer": "^1.4.7", "@types/node": "^20.4.2", "@types/pg": "^8.10.2", "@types/qrcode": "^1.5.5", "@types/supertest": "^2.0.12", "@types/swagger-jsdoc": "^6.0.1", "@types/swagger-ui-express": "^4.1.3", "@types/uuid": "^9.0.2", "@types/ws": "^8.18.1", "@typescript-eslint/eslint-plugin": "^6.2.0", "@typescript-eslint/parser": "^6.2.0", "eslint": "^8.45.0", "jest": "^29.6.1", "nodemon": "^3.0.1", "supertest": "^6.3.3", "ts-jest": "^29.1.1", "ts-node": "^10.9.1", "typescript": "^5.1.6"}, "keywords": ["restaurant", "playlist", "music", "youtube", "api", "nodejs", "typescript"], "author": "Restaurant Playlist System", "license": "MIT"}