import React, { useState, useEffect } from "react";
import { motion, AnimatePresence } from "framer-motion";
import {
  X,
  CreditCard,
  QrCode,
  Copy,
  CheckCircle,
  Clock,
  AlertCircle,
  ExternalLink,
  Smartphone,
} from "lucide-react";
import { toast } from "react-hot-toast";
import { useParams, useSearchParams } from "react-router-dom";

interface PaymentModalProps {
  isOpen: boolean;
  onClose: () => void;
  suggestion: {
    id: string;
    title: string;
    artist: string;
    thumbnailUrl?: string;
    duration?: number;
  };
  sessionId: string;
  onPaymentSuccess?: (paymentId: string) => void;
}

interface PaymentData {
  paymentId: string;
  qrCode: string;
  qrCodeBase64: string;
  ticketUrl: string;
  amount: number;
}

const PaymentModal: React.FC<PaymentModalProps> = ({
  isOpen,
  onClose,
  suggestion,
  sessionId,
  onPaymentSuccess,
}) => {
  const { restaurantId } = useParams<{ restaurantId: string }>();
  const [searchParams] = useSearchParams();
  const tableNumber = searchParams.get("table");
  const [clientName, setClientName] = useState("Cliente"); // TODO: Obter do contexto de sessão
  const [step, setStep] = useState<"confirm" | "payment" | "success" | "error">(
    "confirm"
  );
  const [paymentData, setPaymentData] = useState<PaymentData | null>(null);
  const [loading, setLoading] = useState(false);
  const [timeLeft, setTimeLeft] = useState(1800); // 30 minutos em segundos
  const [checkingPayment, setCheckingPayment] = useState(false);
  const [qrCodeError, setQrCodeError] = useState(false);

  // Validação do restaurantId
  if (!restaurantId) {
    console.error("❌ RestaurantId não encontrado na URL");
    return null;
  }

  // Timer para expiração do pagamento
  useEffect(() => {
    if (step === "payment" && timeLeft > 0) {
      const timer = setInterval(() => {
        setTimeLeft((prev) => {
          if (prev <= 1) {
            setStep("error");
            toast.error("O tempo para pagamento expirou");
            return 0;
          }
          return prev - 1;
        });
      }, 1000);

      return () => clearInterval(timer);
    }
  }, [step, timeLeft]);

  // Verificar status do pagamento
  useEffect(() => {
    if (step === "payment" && paymentData && !checkingPayment) {
      const checkInterval = setInterval(async () => {
        await checkPaymentStatus();
      }, 5000);
      return () => clearInterval(checkInterval);
    }
  }, [step, paymentData, checkingPayment]);

  const formatTime = (seconds: number): string => {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes.toString().padStart(2, "0")}:${remainingSeconds
      .toString()
      .padStart(2, "0")}`;
  };

  const createPayment = async () => {
    try {
      setLoading(true);
      const response = await fetch(
        "http://localhost:8001/api/v1/payments/pix/suggestion",
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            restaurantId: restaurantId!,
            youtubeId: suggestion.id,
            title: suggestion.title,
            artist: suggestion.artist,
            clientName,
            tableNumber: tableNumber ? parseInt(tableNumber) : 1,
            sessionId,
          }),
        }
      );

      if (response.ok) {
        const data = await response.json();
        console.log("💳 DEBUG: Dados do pagamento recebidos:", data);
        if (!data.payment.qrCodeData.startsWith("data:image/")) {
          console.error("❌ qrCodeData inválido:", data.payment.qrCodeData);
          setQrCodeError(true);
          setStep("error");
          toast.error("QR Code inválido retornado pelo servidor");
          return;
        }
        setPaymentData({
          paymentId: data.payment.id,
          qrCode: data.payment.pixCode,
          qrCodeBase64: data.payment.qrCodeData,
          ticketUrl: data.payment.ticketUrl || "",
          amount: data.payment.amount,
        });
        setStep("payment");
        toast.success("Pagamento Pix gerado com sucesso!");
      } else {
        const errorData = await response.json();
        console.error("❌ Erro na resposta do servidor:", errorData);
        toast.error(errorData.error || "Erro ao gerar pagamento");
        setStep("error");
      }
    } catch (error) {
      console.error("Erro ao criar pagamento:", error);
      toast.error("Erro ao conectar com o servidor de pagamento");
      setStep("error");
    } finally {
      setLoading(false);
    }
  };

  const checkPaymentStatus = async () => {
    if (!paymentData || checkingPayment) return;

    try {
      setCheckingPayment(true);
      const response = await fetch(
        `http://localhost:8001/api/v1/payments/${paymentData.paymentId}/status`
      );

      if (response.ok) {
        const data = await response.json();
        if (data.status.status === "approved") {
          setStep("success");
          toast.success(
            "Pagamento aprovado! Sua música foi adicionada à fila."
          );
          onPaymentSuccess?.(paymentData.paymentId);
        } else if (
          data.status.status === "rejected" ||
          data.status.status === "cancelled"
        ) {
          setStep("error");
          toast.error("Pagamento rejeitado ou cancelado");
        }
      } else {
        console.error(
          "❌ Erro ao verificar status do pagamento:",
          response.status
        );
      }
    } catch (error) {
      console.error("Erro ao verificar pagamento:", error);
    } finally {
      setCheckingPayment(false);
    }
  };

  const copyPixCode = () => {
    if (paymentData?.qrCode) {
      navigator.clipboard.writeText(paymentData.qrCode);
      toast.success("Código Pix copiado!");
    }
  };

  const openTicketUrl = () => {
    if (paymentData?.ticketUrl) {
      window.open(paymentData.ticketUrl, "_blank");
    }
  };

  const handleClose = () => {
    setStep("confirm");
    setPaymentData(null);
    setTimeLeft(1800);
    setQrCodeError(false);
    onClose();
  };

  if (!isOpen) {
    return null;
  }

  return (
    <AnimatePresence>
      <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-[9999] p-4 overflow-y-auto">
        <motion.div
          initial={{ opacity: 0, scale: 0.9 }}
          animate={{ opacity: 1, scale: 1 }}
          exit={{ opacity: 0, scale: 0.9 }}
          className="bg-white dark:bg-gray-800 rounded-lg max-w-md w-full my-4"
        >
          {/* Header */}
          <div className="flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700">
            <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
              {step === "confirm" && "Confirmar Pagamento"}
              {step === "payment" && "Pagamento Pix"}
              {step === "success" && "Pagamento Aprovado!"}
              {step === "error" && "Erro no Pagamento"}
            </h2>
            <button
              onClick={handleClose}
              className="p-2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 transition-colors"
              aria-label="Fechar modal"
            >
              <X className="w-5 h-5" />
            </button>
          </div>

          {/* Content */}
          <div className="p-6">
            {/* Informações da música */}
            <div className="flex items-center space-x-4 mb-6 p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
              {suggestion.thumbnailUrl && (
                <img
                  src={suggestion.thumbnailUrl}
                  alt={`Capa de ${suggestion.title} por ${suggestion.artist}`}
                  className="w-16 h-16 rounded-lg object-cover"
                />
              )}
              <div className="flex-1 min-w-0">
                <h3 className="font-medium text-gray-900 dark:text-white truncate">
                  {suggestion.title}
                </h3>
                <p className="text-sm text-gray-600 dark:text-gray-400 truncate">
                  {suggestion.artist}
                </p>
                {suggestion.duration && (
                  <p className="text-xs text-gray-500 dark:text-gray-500">
                    {Math.floor(suggestion.duration / 60)}:
                    {(suggestion.duration % 60).toString().padStart(2, "0")}
                  </p>
                )}
              </div>
            </div>

            {/* Step: Confirm */}
            {step === "confirm" && (
              <div className="space-y-6">
                <div className="text-center">
                  <div className="w-16 h-16 bg-green-100 dark:bg-green-900/20 rounded-full flex items-center justify-center mx-auto mb-4">
                    <CreditCard className="w-8 h-8 text-green-600 dark:text-green-400" />
                  </div>
                  <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
                    Escolher esta música por R$ 2,00
                  </h3>
                  <p className="text-gray-600 dark:text-gray-400 text-sm">
                    Sua música será adicionada à fila prioritária e tocará em
                    breve!
                  </p>
                </div>

                <div className="bg-blue-50 dark:bg-blue-900/20 p-4 rounded-lg">
                  <div className="flex items-start space-x-3">
                    <Smartphone className="w-5 h-5 text-blue-600 dark:text-blue-400 mt-0.5" />
                    <div className="text-sm">
                      <p className="font-medium text-blue-900 dark:text-blue-100 mb-1">
                        Como funciona:
                      </p>
                      <ul className="text-blue-800 dark:text-blue-200 space-y-1">
                        <li>• Pague R$ 2,00 via Pix</li>
                        <li>• Sua música vai para a fila prioritária</li>
                        <li>• Acompanhe as letras no "Cante Comigo"</li>
                        <li>
                          • Outros clientes podem votar na sua performance
                        </li>
                      </ul>
                    </div>
                  </div>
                </div>

                <button
                  onClick={createPayment}
                  disabled={loading}
                  className="w-full bg-green-600 text-white py-3 px-4 rounded-lg font-medium hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors flex items-center justify-center space-x-2"
                  aria-label="Gerar pagamento Pix de R$ 2,00"
                >
                  {loading ? (
                    <div className="w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin" />
                  ) : (
                    <>
                      <QrCode className="w-5 h-5" />
                      <span>Gerar Pix de R$ 2,00</span>
                    </>
                  )}
                </button>
              </div>
            )}

            {/* Step: Payment */}
            {step === "payment" && paymentData && (
              <div className="space-y-6">
                {/* Timer */}
                <div className="flex items-center justify-center space-x-2 text-orange-600 dark:text-orange-400">
                  <Clock className="w-5 h-5" />
                  <span className="font-mono text-lg">
                    {formatTime(timeLeft)}
                  </span>
                  <span className="text-sm">para pagar</span>
                </div>

                {/* QR Code */}
                <div className="text-center">
                  {qrCodeError ? (
                    <div className="bg-red-100 dark:bg-red-900/20 p-4 rounded-lg">
                      <p className="text-sm text-red-600 dark:text-red-400">
                        Não foi possível carregar o QR Code. Use o código Pix
                        abaixo.
                      </p>
                    </div>
                  ) : (
                    <div className="bg-white p-4 rounded-lg inline-block mb-4">
                      <img
                        src={paymentData.qrCodeBase64}
                        alt={`QR Code para pagamento de ${suggestion.title}`}
                        className="w-48 h-48 mx-auto"
                        onError={(e) => {
                          console.error(
                            "❌ Erro ao carregar QR Code:",
                            paymentData.qrCodeBase64
                          );
                          setQrCodeError(true);
                          e.currentTarget.style.display = "none";
                        }}
                        onLoad={() => {
                          console.log("✅ QR Code carregado com sucesso!");
                        }}
                      />
                    </div>
                  )}
                  <p className="text-sm text-gray-600 dark:text-gray-400 mb-4">
                    Escaneie o QR Code com seu app do banco
                  </p>
                </div>

                {/* Código Pix */}
                <div className="space-y-3">
                  <p className="text-sm font-medium text-gray-900 dark:text-white">
                    Ou copie o código Pix:
                  </p>
                  <div className="flex items-center space-x-2">
                    <input
                      type="text"
                      value={paymentData.qrCode}
                      readOnly
                      className="flex-1 px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-gray-50 dark:bg-gray-700 text-sm font-mono"
                      aria-label="Código Pix"
                    />
                    <button
                      onClick={copyPixCode}
                      className="px-3 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                      aria-label="Copiar código Pix"
                    >
                      <Copy className="w-4 h-4" />
                    </button>
                  </div>
                </div>

                {/* Link do ticket */}
                {paymentData.ticketUrl && (
                  <button
                    onClick={openTicketUrl}
                    className="w-full flex items-center justify-center space-x-2 py-2 px-4 border border-gray-300 dark:border-gray-600 rounded-lg text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
                    aria-label="Abrir pagamento no Mercado Pago"
                  >
                    <ExternalLink className="w-4 h-4" />
                    <span>Abrir no Mercado Pago</span>
                  </button>
                )}

                {/* Status */}
                <div className="text-center">
                  <div className="flex items-center justify-center space-x-2 text-blue-600 dark:text-blue-400">
                    <div className="w-4 h-4 border-2 border-blue-600 border-t-transparent rounded-full animate-spin" />
                    <span className="text-sm">Aguardando pagamento...</span>
                  </div>
                </div>
              </div>
            )}

            {/* Step: Success */}
            {step === "success" && (
              <div className="text-center space-y-6">
                <div className="w-16 h-16 bg-green-100 dark:bg-green-900/20 rounded-full flex items-center justify-center mx-auto">
                  <CheckCircle className="w-8 h-8 text-green-600 dark:text-green-400" />
                </div>
                <div>
                  <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
                    Pagamento Aprovado!
                  </h3>
                  <p className="text-gray-600 dark:text-gray-400">
                    Sua música foi adicionada à fila prioritária e tocará em
                    breve.
                  </p>
                </div>
                <button
                  onClick={handleClose}
                  className="w-full bg-green-600 text-white py-3 px-4 rounded-lg font-medium hover:bg-green-700 transition-colors"
                  aria-label="Continuar após pagamento aprovado"
                >
                  Continuar
                </button>
              </div>
            )}

            {/* Step: Error */}
            {step === "error" && (
              <div className="text-center space-y-6">
                <div className="w-16 h-16 bg-red-100 dark:bg-red-900/20 rounded-full flex items-center justify-center mx-auto">
                  <AlertCircle className="w-8 h-8 text-red-600 dark:text-red-400" />
                </div>
                <div>
                  <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
                    Erro no Pagamento
                  </h3>
                  <p className="text-gray-600 dark:text-gray-400">
                    Não foi possível processar o pagamento. Tente novamente.
                  </p>
                </div>
                <div className="space-y-3">
                  <button
                    onClick={() => {
                      setStep("confirm");
                      setPaymentData(null);
                      setTimeLeft(1800);
                      setQrCodeError(false);
                    }}
                    className="w-full bg-blue-600 text-white py-3 px-4 rounded-lg font-medium hover:bg-blue-700 transition-colors"
                    aria-label="Tentar pagamento novamente"
                  >
                    Tentar Novamente
                  </button>
                  <button
                    onClick={handleClose}
                    className="w-full py-3 px-4 border border-gray-300 dark:border-gray-600 rounded-lg text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
                    aria-label="Cancelar pagamento"
                  >
                    Cancelar
                  </button>
                </div>
              </div>
            )}
          </div>
        </motion.div>
      </div>
    </AnimatePresence>
  );
};

export default PaymentModal;
