import React, { useState, useEffect } from "react";
import { motion } from "framer-motion";
import { ThumbsUp, ThumbsDown, TrendingUp, TrendingDown } from "lucide-react";
import { clsx } from "clsx";

interface VoteButtonProps {
  type: "up" | "down";
  onClick: () => void;
  disabled?: boolean;
  count?: number;
  size?: "sm" | "md" | "lg";
  variant?: "default" | "minimal";
  active?: boolean;
}

const VoteButton: React.FC<VoteButtonProps> = ({
  type,
  onClick,
  disabled = false,
  count,
  size = "md",
  variant = "default",
  active = false,
}) => {
  const isUpvote = type === "up";
  const [animatedCount, setAnimatedCount] = useState(count || 0);
  const [isAnimating, setIsAnimating] = useState(false);

  // Animar mudanças no contador
  useEffect(() => {
    if (count !== undefined && count !== animatedCount) {
      setIsAnimating(true);
      setAnimatedCount(count);

      const timer = setTimeout(() => {
        setIsAnimating(false);
      }, 300);

      return () => clearTimeout(timer);
    }
  }, [count, animatedCount]);

  const baseClasses =
    "inline-flex items-center justify-center rounded-lg transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-1 disabled:opacity-50 disabled:cursor-not-allowed";

  const sizeClasses = {
    sm: "w-8 h-8 text-xs",
    md: "w-10 h-10 text-sm",
    lg: "w-12 h-12 text-base",
  };

  const iconSizes = {
    sm: "w-3 h-3",
    md: "w-4 h-4",
    lg: "w-5 h-5",
  };

  const getVariantClasses = () => {
    if (variant === "minimal") {
      return isUpvote
        ? `text-gray-500 hover:text-green-600 hover:bg-green-50 dark:text-gray-400 dark:hover:text-green-400 dark:hover:bg-green-900/20 ${
            active
              ? "text-green-600 bg-green-50 dark:text-green-400 dark:bg-green-900/20"
              : ""
          }`
        : `text-gray-500 hover:text-red-600 hover:bg-red-50 dark:text-gray-400 dark:hover:text-red-400 dark:hover:bg-red-900/20 ${
            active
              ? "text-red-600 bg-red-50 dark:text-red-400 dark:bg-red-900/20"
              : ""
          }`;
    }

    return isUpvote
      ? `bg-green-100 text-green-700 hover:bg-green-200 focus:ring-green-500 dark:bg-green-900/30 dark:text-green-300 dark:hover:bg-green-900/50 ${
          active ? "bg-green-200 dark:bg-green-900/50" : ""
        }`
      : `bg-red-100 text-red-700 hover:bg-red-200 focus:ring-red-500 dark:bg-red-900/30 dark:text-red-300 dark:hover:bg-red-900/50 ${
          active ? "bg-red-200 dark:bg-red-900/50" : ""
        }`;
  };

  const Icon = isUpvote ? ThumbsUp : ThumbsDown;
  const TrendIcon = isUpvote ? TrendingUp : TrendingDown;

  return (
    <motion.button
      whileHover={{ scale: disabled ? 1 : 1.05 }}
      whileTap={{ scale: disabled ? 1 : 0.95 }}
      onClick={onClick}
      disabled={disabled}
      className={clsx(baseClasses, sizeClasses[size], getVariantClasses())}
      title={`${isUpvote ? "Votar positivo" : "Votar negativo"}${
        count !== undefined ? ` (${count})` : ""
      }`}
    >
      <div className="flex flex-col items-center space-y-0.5">
        <Icon className={iconSizes[size]} />
        {count !== undefined && (
          <motion.span
            key={animatedCount}
            initial={{
              scale: isAnimating ? 1.2 : 1,
              opacity: isAnimating ? 0.7 : 1,
            }}
            animate={{ scale: 1, opacity: 1 }}
            transition={{ duration: 0.2 }}
            className="text-xs font-medium leading-none"
          >
            {animatedCount}
          </motion.span>
        )}
      </div>
    </motion.button>
  );
};

export default VoteButton;
