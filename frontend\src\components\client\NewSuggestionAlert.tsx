import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  Music, 
  ThumbsUp, 
  ThumbsDown, 
  Users, 
  Clock, 
  CreditCard,
  X,
  Play,
  Star
} from 'lucide-react';
import { toast } from 'react-hot-toast';

interface NewSuggestion {
  id: string;
  title: string;
  artist: string;
  thumbnailUrl: string;
  duration: number;
  formattedDuration: string;
  isPaid: boolean;
  clientName?: string;
  tableNumber?: string;
  upvotes: number;
  downvotes: number;
  score: number;
  createdAt: string;
  youtubeVideoId: string;
}

interface NewSuggestionAlertProps {
  restaurantId: string;
  sessionId: string;
  onVote?: (suggestionId: string, voteType: 'up' | 'down') => void;
}

export const NewSuggestionAlert: React.FC<NewSuggestionAlertProps> = ({
  restaurantId,
  sessionId,
  onVote
}) => {
  const [newSuggestions, setNewSuggestions] = useState<NewSuggestion[]>([]);
  const [votingStates, setVotingStates] = useState<Record<string, boolean>>({});
  const [userVotes, setUserVotes] = useState<Record<string, 'up' | 'down'>>({});

  // Conectar ao WebSocket para receber novas sugestões
  useEffect(() => {
    const ws = new WebSocket(`ws://localhost:8001`);
    
    ws.onopen = () => {
      console.log('🔗 Conectado ao WebSocket para alertas de sugestões');
      // Entrar na sala do restaurante
      ws.send(JSON.stringify({
        type: 'join-restaurant',
        restaurantId: restaurantId
      }));
    };

    ws.onmessage = (event) => {
      try {
        const data = JSON.parse(event.data);
        
        // Escutar por novas sugestões
        if (data.type === 'new-suggestion' && data.suggestion) {
          const suggestion = data.suggestion;
          
          // Não mostrar sugestões da própria mesa
          if (suggestion.clientSessionId !== sessionId) {
            handleNewSuggestion(suggestion);
          }
        }
        
        // Escutar por atualizações de votos
        if (data.type === 'vote-update' && data.suggestionId) {
          updateSuggestionVotes(data.suggestionId, data);
        }
      } catch (error) {
        console.error('Erro ao processar mensagem WebSocket:', error);
      }
    };

    ws.onclose = () => {
      console.log('🔌 WebSocket desconectado');
    };

    return () => {
      ws.close();
    };
  }, [restaurantId, sessionId]);

  const handleNewSuggestion = (suggestion: any) => {
    const newSuggestion: NewSuggestion = {
      id: suggestion.id,
      title: suggestion.title,
      artist: suggestion.artist,
      thumbnailUrl: suggestion.thumbnailUrl || `https://img.youtube.com/vi/${suggestion.youtubeVideoId}/mqdefault.jpg`,
      duration: suggestion.duration,
      formattedDuration: suggestion.formattedDuration || formatDuration(suggestion.duration),
      isPaid: suggestion.isPaid || false,
      clientName: suggestion.clientName,
      tableNumber: suggestion.tableNumber,
      upvotes: suggestion.upvotes || 0,
      downvotes: suggestion.downvotes || 0,
      score: suggestion.score || 0,
      createdAt: suggestion.createdAt,
      youtubeVideoId: suggestion.youtubeVideoId
    };

    setNewSuggestions(prev => {
      // Evitar duplicatas
      if (prev.find(s => s.id === newSuggestion.id)) {
        return prev;
      }
      return [newSuggestion, ...prev];
    });

    // Mostrar toast de notificação
    const tableInfo = suggestion.tableNumber ? `Mesa ${suggestion.tableNumber}` : 'Alguém';
    const paymentInfo = suggestion.isPaid ? ' (PAGO)' : '';
    
    toast.success(
      `🎵 ${tableInfo} sugeriu "${suggestion.title}"${paymentInfo}! Vote agora!`,
      {
        duration: 5000,
        icon: '🗳️'
      }
    );

    // Auto-remover após 2 minutos se não houver interação
    setTimeout(() => {
      setNewSuggestions(prev => prev.filter(s => s.id !== newSuggestion.id));
    }, 120000);
  };

  const updateSuggestionVotes = (suggestionId: string, voteData: any) => {
    setNewSuggestions(prev => 
      prev.map(suggestion => 
        suggestion.id === suggestionId 
          ? {
              ...suggestion,
              upvotes: voteData.upvotes || suggestion.upvotes,
              downvotes: voteData.downvotes || suggestion.downvotes,
              score: voteData.voteCount || suggestion.score
            }
          : suggestion
      )
    );
  };

  const formatDuration = (seconds: number): string => {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
  };

  const handleVote = async (suggestionId: string, voteType: 'up' | 'down') => {
    if (votingStates[suggestionId] || userVotes[suggestionId]) {
      return;
    }

    setVotingStates(prev => ({ ...prev, [suggestionId]: true }));

    try {
      const response = await fetch(`http://localhost:8001/api/v1/suggestions/${suggestionId}/vote`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'X-Session-ID': sessionId,
        },
        body: JSON.stringify({ voteType })
      });

      if (response.ok) {
        setUserVotes(prev => ({ ...prev, [suggestionId]: voteType }));
        
        toast.success(
          `Voto ${voteType === 'up' ? 'positivo' : 'negativo'} registrado! +10 pontos!`,
          { icon: voteType === 'up' ? '👍' : '👎' }
        );

        // Chamar callback se fornecido
        onVote?.(suggestionId, voteType);

        // Remover da lista após votar
        setTimeout(() => {
          setNewSuggestions(prev => prev.filter(s => s.id !== suggestionId));
        }, 2000);
      } else {
        const errorData = await response.json();
        toast.error(errorData.message || 'Erro ao votar');
      }
    } catch (error) {
      console.error('Erro ao votar:', error);
      toast.error('Erro ao votar');
    } finally {
      setVotingStates(prev => ({ ...prev, [suggestionId]: false }));
    }
  };

  const dismissSuggestion = (suggestionId: string) => {
    setNewSuggestions(prev => prev.filter(s => s.id !== suggestionId));
  };

  if (newSuggestions.length === 0) {
    return null;
  }

  return (
    <div className="fixed top-4 right-4 z-50 space-y-3 max-w-sm">
      <AnimatePresence>
        {newSuggestions.slice(0, 3).map((suggestion) => (
          <motion.div
            key={suggestion.id}
            initial={{ opacity: 0, x: 300, scale: 0.8 }}
            animate={{ opacity: 1, x: 0, scale: 1 }}
            exit={{ opacity: 0, x: 300, scale: 0.8 }}
            transition={{ duration: 0.4, type: "spring" }}
            className="bg-gradient-to-r from-purple-600 to-pink-600 rounded-lg shadow-2xl p-4 text-white border-2 border-white/20"
          >
            {/* Header */}
            <div className="flex items-center justify-between mb-3">
              <div className="flex items-center space-x-2">
                <Music className="w-5 h-5 text-yellow-300" />
                <span className="font-bold text-sm">Nova Sugestão!</span>
                {suggestion.isPaid && (
                  <div className="flex items-center space-x-1 bg-yellow-500 text-black px-2 py-1 rounded-full">
                    <CreditCard className="w-3 h-3" />
                    <span className="text-xs font-bold">PAGO</span>
                  </div>
                )}
              </div>
              <button
                onClick={() => dismissSuggestion(suggestion.id)}
                className="text-white/70 hover:text-white transition-colors"
              >
                <X className="w-4 h-4" />
              </button>
            </div>

            {/* Conteúdo */}
            <div className="flex space-x-3">
              <img
                src={suggestion.thumbnailUrl}
                alt={suggestion.title}
                className="w-16 h-12 rounded-lg object-cover"
              />
              <div className="flex-1 min-w-0">
                <h3 className="font-semibold text-sm truncate">{suggestion.title}</h3>
                <p className="text-white/80 text-xs truncate">{suggestion.artist}</p>
                <div className="flex items-center space-x-2 mt-1">
                  <div className="flex items-center space-x-1 text-xs">
                    <Users className="w-3 h-3" />
                    <span>{suggestion.tableNumber ? `Mesa ${suggestion.tableNumber}` : 'Anônimo'}</span>
                  </div>
                  <div className="flex items-center space-x-1 text-xs">
                    <Clock className="w-3 h-3" />
                    <span>{suggestion.formattedDuration}</span>
                  </div>
                </div>
              </div>
            </div>

            {/* Estatísticas de Votos */}
            <div className="flex items-center justify-between mt-3 text-xs">
              <div className="flex items-center space-x-3">
                <div className="flex items-center space-x-1 text-green-300">
                  <ThumbsUp className="w-3 h-3" />
                  <span>{suggestion.upvotes}</span>
                </div>
                <div className="flex items-center space-x-1 text-red-300">
                  <ThumbsDown className="w-3 h-3" />
                  <span>{suggestion.downvotes}</span>
                </div>
                <div className="flex items-center space-x-1 text-yellow-300">
                  <Star className="w-3 h-3" />
                  <span>Score: {suggestion.score}</span>
                </div>
              </div>
            </div>

            {/* Botões de Votação */}
            <div className="flex space-x-2 mt-4">
              <button
                onClick={() => handleVote(suggestion.id, 'up')}
                disabled={votingStates[suggestion.id] || userVotes[suggestion.id] === 'up'}
                className={`flex-1 flex items-center justify-center space-x-2 py-2 px-3 rounded-lg transition-colors ${
                  userVotes[suggestion.id] === 'up'
                    ? 'bg-green-600 text-white'
                    : 'bg-white/20 hover:bg-green-600 hover:text-white'
                } disabled:opacity-50 disabled:cursor-not-allowed`}
              >
                <ThumbsUp className="w-4 h-4" />
                <span className="text-sm font-medium">Curtir</span>
              </button>
              
              <button
                onClick={() => handleVote(suggestion.id, 'down')}
                disabled={votingStates[suggestion.id] || userVotes[suggestion.id] === 'down'}
                className={`flex-1 flex items-center justify-center space-x-2 py-2 px-3 rounded-lg transition-colors ${
                  userVotes[suggestion.id] === 'down'
                    ? 'bg-red-600 text-white'
                    : 'bg-white/20 hover:bg-red-600 hover:text-white'
                } disabled:opacity-50 disabled:cursor-not-allowed`}
              >
                <ThumbsDown className="w-4 h-4" />
                <span className="text-sm font-medium">Não Curtir</span>
              </button>
            </div>

            {/* Indicador de Loading */}
            {votingStates[suggestion.id] && (
              <div className="flex items-center justify-center mt-2">
                <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                <span className="ml-2 text-xs">Enviando voto...</span>
              </div>
            )}
          </motion.div>
        ))}
      </AnimatePresence>
      
      {/* Contador de sugestões pendentes */}
      {newSuggestions.length > 3 && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          className="bg-black/50 backdrop-blur-sm rounded-lg p-2 text-center text-white text-xs"
        >
          +{newSuggestions.length - 3} mais sugestões aguardando seu voto
        </motion.div>
      )}
    </div>
  );
};
