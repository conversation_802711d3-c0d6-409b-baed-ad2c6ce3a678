import React, { useState, useEffect, useCallback, memo } from "react";
import { useParams } from "react-router-dom";
import { motion, AnimatePresence } from "framer-motion";
import {
  QrCode,
  Plus,
  Download,
  Trash2,
  ExternalLink,
  Copy,
  RefreshCw,
  Users,
  Table,
  Building2,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>otate<PERSON>w,
} from "lucide-react";
import { toast } from "react-hot-toast";
import apiService from "@/services/api";

enum QRCodeType {
  Table = "table",
  Restaurant = "restaurant",
}

interface QRCodeData {
  id: string;
  type: QRCodeType;
  name: string;
  url: string;
  qrCodeDataURL: string;
  createdAt: string;
  isActive: boolean;
  tableNumber?: string;
  restaurantId: string;
  restaurant: {
    id: string;
    name: string;
  };
}

// This is the base64 of a 1x1 transparent PNG - used to detect placeholder images
const PLACEHOLDER_IMAGE =
  "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==";

const QRCodeManager: React.FC = memo(() => {
  const { restaurantId } = useParams<{ restaurantId: string }>();
  const [qrCodes, setQRCodes] = useState<QRCodeData[]>([]);
  const [loading, setLoading] = useState(false);
  const [regenerating, setRegenerating] = useState<Record<string, boolean>>({});
  const [showGenerator, setShowGenerator] = useState(false);
  const [tableNumber, setTableNumber] = useState("");
  const [tableName, setTableName] = useState("");
  const [bulkCount, setBulkCount] = useState(5);
  const [imageLoadErrors, setImageLoadErrors] = useState<
    Record<string, boolean>
  >({});

  const finalRestaurantId = restaurantId || "demo-restaurant";

  const loadQRCodes = useCallback(async () => {
    setLoading(true);
    try {
      console.log(
        "🔍 QR: Carregando QR codes para restaurante:",
        finalRestaurantId
      );

      // Use apiService to ensure proper headers and error handling
      const response = await apiService.client.get(
        `/qrcode/${finalRestaurantId}`
      );

      console.log("🔍 QR: Dados recebidos:", response.data);

      // Validate QR code data
      const validQRCodes = (response.data.qrCodes || []).filter(
        (qr: QRCodeData) => qr && qr.id && qr.qrCodeDataURL
      );

      if (validQRCodes.length < (response.data.qrCodes || []).length) {
        console.warn(
          "🔍 QR: Alguns QR codes foram filtrados por dados inválidos"
        );
      }

      // Reset image load errors
      setImageLoadErrors({});
      setQRCodes(validQRCodes);

      // Check for placeholder images
      const placeholderCount = validQRCodes.filter(
        (qr) => qr.qrCodeDataURL === PLACEHOLDER_IMAGE
      ).length;

      if (placeholderCount > 0) {
        console.warn(
          `🔍 QR: ${placeholderCount} QR codes são imagens placeholder`
        );
        if (toast?.warning) {
          toast.warning(
            `${placeholderCount} QR codes precisam ser regenerados`
          );
        }
      } else {
        if (toast?.success) {
          toast.success(`${validQRCodes.length} QR codes carregados`);
        }
      }
    } catch (error) {
      console.error("🔍 QR: Erro ao carregar QR codes:", error);
      toast.error("Erro ao carregar QR codes");
    } finally {
      setLoading(false);
    }
  }, [finalRestaurantId]);

  useEffect(() => {
    loadQRCodes();
  }, [loadQRCodes]);

  const handleImageError = useCallback((qrCodeId: string) => {
    console.error(
      `🔍 QR: Erro ao carregar imagem para QR code ID: ${qrCodeId}`
    );
    setImageLoadErrors((prev) => ({
      ...prev,
      [qrCodeId]: true,
    }));
  }, []);

  const generateSingleTableQRCode = useCallback(async () => {
    if (!tableNumber.trim()) {
      toast.error("Número da mesa é obrigatório");
      return;
    }

    setLoading(true);
    try {
      console.log("🔍 QR: Gerando QR code para mesa:", tableNumber);

      // Use apiService for consistency
      const response = await apiService.client.post("/qrcode/table", {
        restaurantId: finalRestaurantId,
        tableNumber: tableNumber.trim(),
        tableName: tableName.trim() || `Mesa ${tableNumber}`,
      });

      console.log("🔍 QR: Resposta da geração:", response.data);

      if (response.data.success) {
        // Verify the QR code is not a placeholder
        if (response.data.qrCode?.qrCodeDataURL === PLACEHOLDER_IMAGE) {
          toast.warning("QR Code gerado como placeholder, tente novamente");
        } else {
          toast.success("QR Code gerado com sucesso!");
          setTableNumber("");
          setTableName("");
          setShowGenerator(false);
          await loadQRCodes();
        }
      } else {
        toast.error(response.data.error || "Erro ao gerar QR Code");
      }
    } catch (error) {
      console.error("Erro ao gerar QR code:", error);
      toast.error("Erro ao gerar QR Code");
    } finally {
      setLoading(false);
    }
  }, [finalRestaurantId, tableNumber, tableName, loadQRCodes]);

  const generateBulkQRCodes = useCallback(async () => {
    if (bulkCount < 1 || bulkCount > 50) {
      toast.error("Número de mesas deve estar entre 1 e 50");
      return;
    }

    setLoading(true);
    try {
      const response = await apiService.client.post("/qrcode/bulk-tables", {
        restaurantId: finalRestaurantId,
        tableCount: bulkCount,
        tablePrefix: "Mesa",
      });

      console.log("🔍 QR: Resposta da geração em lote:", response.data);

      if (response.data.success) {
        toast.success(
          `${response.data.totalGenerated} QR Codes gerados com sucesso!`
        );
        setShowGenerator(false);
        await loadQRCodes();
      } else {
        toast.error(response.data.error || "Erro ao gerar QR Codes");
      }
    } catch (error) {
      console.error("Erro ao gerar QR codes em lote:", error);
      toast.error("Erro ao gerar QR Codes");
    } finally {
      setLoading(false);
    }
  }, [finalRestaurantId, bulkCount, loadQRCodes]);

  const generateRestaurantQRCode = useCallback(async () => {
    setLoading(true);
    try {
      const response = await apiService.client.post("/qrcode/restaurant", {
        restaurantId: finalRestaurantId,
        restaurantName: "Restaurante Demo",
      });

      console.log(
        "🔍 QR: Resposta da geração do QR do restaurante:",
        response.data
      );

      if (response.data.success) {
        // Verify the QR code is not a placeholder
        if (response.data.qrCode?.qrCodeDataURL === PLACEHOLDER_IMAGE) {
          toast.warning("QR Code gerado como placeholder, tente novamente");
        } else {
          toast.success("QR Code do restaurante gerado com sucesso!");
          setShowGenerator(false);
          await loadQRCodes();
        }
      } else {
        toast.error(response.data.error || "Erro ao gerar QR Code");
      }
    } catch (error) {
      console.error("Erro ao gerar QR code do restaurante:", error);
      toast.error("Erro ao gerar QR Code");
    } finally {
      setLoading(false);
    }
  }, [finalRestaurantId, loadQRCodes]);

  // New function to regenerate a QR code
  const regenerateQRCode = useCallback(
    async (qrCode: QRCodeData) => {
      setRegenerating((prev) => ({ ...prev, [qrCode.id]: true }));

      try {
        let response;

        if (qrCode.type === QRCodeType.Table) {
          response = await apiService.client.post("/qrcode/regenerate/table", {
            qrCodeId: qrCode.id,
            restaurantId: finalRestaurantId,
            tableNumber: qrCode.tableNumber || "1",
            tableName: qrCode.name,
          });
        } else {
          response = await apiService.client.post(
            "/qrcode/regenerate/restaurant",
            {
              qrCodeId: qrCode.id,
              restaurantId: finalRestaurantId,
              restaurantName: qrCode.name,
            }
          );
        }

        console.log("🔍 QR: Resposta da regeneração:", response.data);

        if (response.data.success) {
          toast.success("QR Code regenerado com sucesso!");
          await loadQRCodes();
        } else {
          toast.error(response.data.error || "Erro ao regenerar QR Code");
        }
      } catch (error) {
        console.error("Erro ao regenerar QR code:", error);
        toast.error("Erro ao regenerar QR Code");
      } finally {
        setRegenerating((prev) => ({ ...prev, [qrCode.id]: false }));
      }
    },
    [finalRestaurantId, loadQRCodes]
  );

  // Alternative way to generate QR using client-side QR code generation
  const generateClientSideQR = useCallback(
    async (qrCode: QRCodeData) => {
      // Using a dynamic import to load the QR code library only when needed
      try {
        setRegenerating((prev) => ({ ...prev, [qrCode.id]: true }));

        // Try to use a direct API call to the public QR code generation service
        const qrData = encodeURIComponent(qrCode.url);
        const size = 300;

        // Generate QR code using a public API service
        const qrServiceUrl = `https://api.qrserver.com/v1/create-qr-code/?data=${qrData}&size=${size}x${size}&margin=10`;

        // Convert to base64 to match your existing format
        const response = await fetch(qrServiceUrl);
        const blob = await response.blob();

        const reader = new FileReader();
        reader.readAsDataURL(blob);
        reader.onloadend = async () => {
          const base64data = reader.result as string;

          // Now update the QR code on the server with the new data URL
          try {
            const updateResponse = await apiService.client.post(
              "/qrcode/update-image",
              {
                qrCodeId: qrCode.id,
                qrCodeDataURL: base64data,
              }
            );

            if (updateResponse.data.success) {
              toast.success("QR Code regenerado com sucesso!");
              await loadQRCodes();
            } else {
              toast.error("Erro ao atualizar QR Code no servidor");
            }
          } catch (error) {
            console.error("Erro ao atualizar QR code no servidor:", error);
            toast.error("Erro ao atualizar QR Code");
          }

          setRegenerating((prev) => ({ ...prev, [qrCode.id]: false }));
        };
      } catch (error) {
        console.error("Erro ao gerar QR code localmente:", error);
        toast.error("Erro ao gerar QR Code localmente");
        setRegenerating((prev) => ({ ...prev, [qrCode.id]: false }));
      }
    },
    [loadQRCodes]
  );

  const copyToClipboard = useCallback((text: string) => {
    navigator.clipboard.writeText(text);
    toast.success("URL copiada para a área de transferência!");
  }, []);

  const deleteQRCode = useCallback(
    async (id: string) => {
      if (!window.confirm("Tem certeza que deseja deletar este QR Code?")) {
        return;
      }

      try {
        const response = await apiService.client.delete(`/qrcode/${id}`);
        if (response.data.success) {
          toast.success("QR Code deletado com sucesso!");
          await loadQRCodes();
        } else {
          toast.error(response.data.error || "Erro ao deletar QR Code");
        }
      } catch (error) {
        console.error("Erro ao deletar QR code:", error);
        toast.error("Erro ao deletar QR Code");
      }
    },
    [loadQRCodes]
  );

  const downloadQRCode = useCallback((qrCode: QRCodeData) => {
    // Check if QR code data URL is valid and not a placeholder
    if (!qrCode.qrCodeDataURL || qrCode.qrCodeDataURL === PLACEHOLDER_IMAGE) {
      toast.error("Este QR Code precisa ser regenerado antes de baixar");
      return;
    }

    try {
      const link = document.createElement("a");
      link.href = qrCode.qrCodeDataURL;
      link.download = `${qrCode.name}-${qrCode.id}.png`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      toast.success("QR Code baixado com sucesso!");
    } catch (error) {
      console.error("Erro ao fazer download do QR code:", error);
      toast.error("Erro ao baixar QR Code");
    }
  }, []);

  // Function to render QR code image or fallback
  const renderQRCodeImage = useCallback(
    (qrCode: QRCodeData) => {
      const hasError = imageLoadErrors[qrCode.id];
      const isPlaceholder = qrCode.qrCodeDataURL === PLACEHOLDER_IMAGE;
      const isRegenerating = regenerating[qrCode.id];

      if (hasError || !qrCode.qrCodeDataURL || isPlaceholder) {
        return (
          <div className="w-32 h-32 mx-auto flex flex-col items-center justify-center bg-gray-100 dark:bg-gray-700 rounded-lg mb-3">
            <div className="text-center">
              <AlertTriangle className="w-8 h-8 text-yellow-500 mx-auto mb-2" />
              <span className="text-xs text-gray-500 dark:text-gray-400">
                QR indisponível
              </span>
              <button
                onClick={() => generateClientSideQR(qrCode)}
                disabled={isRegenerating}
                className="mt-2 px-2 py-1 bg-blue-600 text-white text-xs rounded hover:bg-blue-700 disabled:opacity-50 flex items-center justify-center space-x-1 mx-auto"
              >
                {isRegenerating ? (
                  <RefreshCw className="w-3 h-3 animate-spin" />
                ) : (
                  <RotateCw className="w-3 h-3" />
                )}
                <span>Regenerar</span>
              </button>
            </div>
          </div>
        );
      }

      return (
        <img
          src={qrCode.qrCodeDataURL}
          alt={`QR Code para ${qrCode.name}`}
          className="w-32 h-32 mx-auto rounded-lg mb-3"
          loading="lazy"
          onError={() => handleImageError(qrCode.id)}
        />
      );
    },
    [imageLoadErrors, regenerating, handleImageError, generateClientSideQR]
  );

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-2xl font-bold text-gray-900 dark:text-white">
            QR Codes
          </h2>
          <p className="text-gray-600 dark:text-gray-400 mt-1">
            Gerencie os QR Codes para acesso dos clientes
          </p>
        </div>
        <button
          onClick={() => setShowGenerator(true)}
          onKeyDown={(e) => e.key === "Enter" && setShowGenerator(true)}
          className="flex items-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
          aria-label="Gerar novo QR Code"
        >
          <Plus className="w-4 h-4" />
          <span>Gerar QR Code</span>
        </button>
      </div>

      {/* Generator Modal */}
      <AnimatePresence>
        {showGenerator && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50"
          >
            <motion.div
              initial={{ scale: 0.9, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              exit={{ scale: 0.9, opacity: 0 }}
              transition={{ duration: 0.2 }}
              className="bg-white dark:bg-gray-800 rounded-lg p-6 w-full max-w-md mx-4"
            >
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
                Gerar QR Code
              </h3>
              <div className="space-y-4">
                {/* Single Table */}
                <div className="border border-gray-200 dark:border-gray-700 rounded-lg p-4">
                  <h4 className="font-medium text-gray-900 dark:text-white mb-3">
                    Mesa Individual
                  </h4>
                  <div className="space-y-3">
                    <input
                      type="text"
                      value={tableNumber}
                      onChange={(e) => setTableNumber(e.target.value)}
                      placeholder="Número da mesa (ex: 1, 2, 3...)"
                      className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                      aria-label="Número da mesa"
                    />
                    <input
                      type="text"
                      value={tableName}
                      onChange={(e) => setTableName(e.target.value)}
                      placeholder="Nome da mesa (opcional)"
                      className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                      aria-label="Nome da mesa"
                    />
                    <button
                      onClick={generateSingleTableQRCode}
                      disabled={loading || !tableNumber.trim()}
                      className="w-full px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 flex items-center justify-center space-x-2 transition-colors"
                      aria-label="Gerar QR Code para mesa individual"
                    >
                      {loading ? (
                        <RefreshCw className="w-4 h-4 animate-spin" />
                      ) : (
                        <Table className="w-4 h-4" />
                      )}
                      <span>Gerar Mesa</span>
                    </button>
                  </div>
                </div>

                {/* Bulk Tables */}
                <div className="border border-gray-200 dark:border-gray-700 rounded-lg p-4">
                  <h4 className="font-medium text-gray-900 dark:text-white mb-3">
                    Múltiplas Mesas
                  </h4>
                  <div className="space-y-3">
                    <input
                      type="number"
                      value={bulkCount}
                      onChange={(e) =>
                        setBulkCount(
                          Math.max(
                            1,
                            Math.min(50, parseInt(e.target.value) || 1)
                          )
                        )
                      }
                      min="1"
                      max="50"
                      placeholder="Quantidade de mesas"
                      className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                      aria-label="Quantidade de mesas"
                    />
                    <button
                      onClick={generateBulkQRCodes}
                      disabled={loading}
                      className="w-full px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 disabled:opacity-50 flex items-center justify-center space-x-2 transition-colors"
                      aria-label={`Gerar ${bulkCount} QR Codes para mesas`}
                    >
                      {loading ? (
                        <RefreshCw className="w-4 h-4 animate-spin" />
                      ) : (
                        <Users className="w-4 h-4" />
                      )}
                      <span>Gerar {bulkCount} Mesas</span>
                    </button>
                  </div>
                </div>

                {/* Restaurant QR Code */}
                <div className="border border-gray-200 dark:border-gray-700 rounded-lg p-4">
                  <h4 className="font-medium text-gray-900 dark:text-white mb-3">
                    QR Code Geral do Restaurante
                  </h4>
                  <button
                    onClick={generateRestaurantQRCode}
                    disabled={loading}
                    className="w-full px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 disabled:opacity-50 flex items-center justify-center space-x-2 transition-colors"
                    aria-label="Gerar QR Code geral do restaurante"
                  >
                    {loading ? (
                      <RefreshCw className="w-4 h-4 animate-spin" />
                    ) : (
                      <Building2 className="w-4 h-4" />
                    )}
                    <span>Gerar QR Geral</span>
                  </button>
                </div>
              </div>
              <div className="flex space-x-3 mt-6">
                <button
                  onClick={() => setShowGenerator(false)}
                  onKeyDown={(e) =>
                    e.key === "Enter" && setShowGenerator(false)
                  }
                  className="flex-1 px-4 py-2 bg-gray-200 dark:bg-gray-600 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-300 dark:hover:bg-gray-500 transition-colors"
                  aria-label="Cancelar geração de QR Code"
                >
                  Cancelar
                </button>
              </div>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* QR Codes List */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow">
        <div className="p-6 border-b border-gray-200 dark:border-gray-700">
          <div className="flex justify-between items-center">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
              QR Codes Criados
            </h3>
            <button
              onClick={loadQRCodes}
              onKeyDown={(e) => e.key === "Enter" && loadQRCodes()}
              className="text-blue-600 hover:text-blue-700 dark:text-blue-400 dark:hover:text-blue-300 flex items-center space-x-1 transition-colors"
              aria-label="Atualizar lista de QR Codes"
            >
              <RefreshCw
                className={`w-4 h-4 ${loading ? "animate-spin" : ""}`}
              />
              <span>{loading ? "Carregando..." : "Atualizar"}</span>
            </button>
          </div>
        </div>
        <div className="p-6">
          {loading && qrCodes.length === 0 ? (
            <div className="text-center py-12">
              <RefreshCw className="w-12 h-12 text-gray-400 mx-auto mb-4 animate-spin" />
              <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
                Carregando QR Codes...
              </h3>
            </div>
          ) : qrCodes.length === 0 ? (
            <div className="text-center py-12">
              <QrCode className="w-12 h-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
                Nenhum QR Code criado
              </h3>
              <p className="text-gray-600 dark:text-gray-400 mb-4">
                Crie QR Codes para que os clientes possam acessar o sistema
              </p>
              <button
                onClick={() => setShowGenerator(true)}
                onKeyDown={(e) => e.key === "Enter" && setShowGenerator(true)}
                className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                aria-label="Criar primeiro QR Code"
              >
                Criar Primeiro QR Code
              </button>
            </div>
          ) : (
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
              {qrCodes.map((qrCode) => (
                <motion.div
                  key={qrCode.id}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.3 }}
                  className="border border-gray-200 dark:border-gray-700 rounded-lg p-4"
                >
                  <div className="text-center mb-4">
                    {renderQRCodeImage(qrCode)}
                    <h4 className="font-medium text-gray-900 dark:text-white truncate">
                      {qrCode.name}
                    </h4>
                    <p className="text-sm text-gray-500 dark:text-gray-400">
                      {qrCode.type === QRCodeType.Table
                        ? "Mesa"
                        : "Restaurante"}
                    </p>
                  </div>
                  <div className="space-y-2">
                    <div className="text-xs text-gray-500 dark:text-gray-400 break-all">
                      {qrCode.url}
                    </div>
                    <div className="flex space-x-2">
                      <button
                        onClick={() => window.open(qrCode.url, "_blank")}
                        onKeyDown={(e) =>
                          e.key === "Enter" && window.open(qrCode.url, "_blank")
                        }
                        className="flex-1 px-2 py-1 bg-blue-100 text-blue-700 dark:bg-blue-900/20 dark:text-blue-400 rounded text-xs hover:bg-blue-200 dark:hover:bg-blue-900/30 flex items-center justify-center space-x-1 transition-colors"
                        aria-label={`Testar QR Code ${qrCode.name}`}
                      >
                        <ExternalLink className="w-3 h-3" />
                        <span>Testar</span>
                      </button>
                      <button
                        onClick={() => copyToClipboard(qrCode.url)}
                        onKeyDown={(e) =>
                          e.key === "Enter" && copyToClipboard(qrCode.url)
                        }
                        className="flex-1 px-2 py-1 bg-green-100 text-green-700 dark:bg-green-900/20 dark:text-green-400 rounded text-xs hover:bg-green-200 dark:hover:bg-green-900/30 flex items-center justify-center space-x-1 transition-colors"
                        aria-label={`Copiar URL do QR Code ${qrCode.name}`}
                      >
                        <Copy className="w-3 h-3" />
                        <span>Copiar</span>
                      </button>
                      <button
                        onClick={() => downloadQRCode(qrCode)}
                        onKeyDown={(e) =>
                          e.key === "Enter" && downloadQRCode(qrCode)
                        }
                        className="flex-1 px-2 py-1 bg-purple-100 text-purple-700 dark:bg-purple-900/20 dark:text-purple-400 rounded text-xs hover:bg-purple-200 dark:hover:bg-purple-900/30 flex items-center justify-center space-x-1 transition-colors"
                        aria-label={`Baixar QR Code ${qrCode.name}`}
                        disabled={qrCode.qrCodeDataURL === PLACEHOLDER_IMAGE}
                      >
                        <Download className="w-3 h-3" />
                        <span>Baixar</span>
                      </button>
                      <button
                        onClick={() => deleteQRCode(qrCode.id)}
                        onKeyDown={(e) =>
                          e.key === "Enter" && deleteQRCode(qrCode.id)
                        }
                        className="px-2 py-1 bg-red-100 text-red-700 dark:bg-red-900/20 dark:text-red-400 rounded text-xs hover:bg-red-200 dark:hover:bg-red-900/30 transition-colors"
                        aria-label={`Deletar QR Code ${qrCode.name}`}
                      >
                        <Trash2 className="w-3 h-3" />
                      </button>
                    </div>
                  </div>
                </motion.div>
              ))}
            </div>
          )}
        </div>
      </div>
    </div>
  );
});

export default QRCodeManager;
