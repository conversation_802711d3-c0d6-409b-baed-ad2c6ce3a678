import React, { useState, useEffect, useCallback, memo } from "react";
import { motion, AnimatePresence } from "framer-motion";
import {
  BarChart3,
  TrendingUp,
  Users,
  Music,
  Heart,
  Clock,
  Download,
  RefreshCw,
  Award,
  Activity,
} from "lucide-react";
import { toast } from "react-hot-toast";
import apiService from "@/services/api";

enum Period {
  Today = "1d",
  Week = "7d",
  Month = "30d",
  Quarter = "90d",
}

interface AnalyticsSummary {
  totalPlays: number;
  totalSuggestions: number;
  totalVotes: number;
  activeUsers: number;
  averageRating: number;
  growthRate: number;
  peakHour: string;
  topGenre: string;
}

interface AnalyticsMetrics {
  totalSuggestions: number;
  approvedSuggestions: number;
  rejectedSuggestions: number;
  totalVotes: number;
  upvotes: number;
  downvotes: number;
  uniqueSessions: number;
  averageSessionDuration: number;
  engagementRate: number;
  approvalRate: number;
  topGenres: Array<{ genre: string; count: number; percentage: number }>;
  topArtists: Array<{ artist: string; count: number; percentage: number }>;
  hourlyActivity: Array<{ hour: number; suggestions: number; votes: number; sessions: number }>;
  dailyActivity: Array<{ date: string; suggestions: number; votes: number; sessions: number }>;
}

interface PopularSong {
  id: string;
  title: string;
  artist: string;
  votes: number;
  plays: number;
  score: number;
}

interface AnalyticsDashboardProps {
  restaurantId?: string;
}

const AnalyticsDashboard: React.FC<AnalyticsDashboardProps> = memo(({ restaurantId = "demo-restaurant" }) => {
  const [summary, setSummary] = useState<AnalyticsSummary | null>(null);
  const [metrics, setMetrics] = useState<AnalyticsMetrics | null>(null);
  const [popularSongs, setPopularSongs] = useState<PopularSong[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedPeriod, setSelectedPeriod] = useState<Period>(Period.Week);
  const [activeTab, setActiveTab] = useState<"overview" | "engagement" | "music" | "users">("overview");

  const periods = [
    { value: Period.Today, label: "Hoje" },
    { value: Period.Week, label: "7 dias" },
    { value: Period.Month, label: "30 dias" },
    { value: Period.Quarter, label: "90 dias" },
  ];

  const tabs = [
    { id: "overview", label: "Visão Geral", icon: BarChart3 },
    { id: "engagement", label: "Engajamento", icon: Heart },
    { id: "music", label: "Músicas", icon: Music },
    { id: "users", label: "Usuários", icon: Users },
  ] as const;

  const loadAnalytics = useCallback(async () => {
    try {
      setLoading(true);
      const [summaryRes, metricsRes, songsRes] = await Promise.all([
        apiService.client.get(`/analytics/dashboard/${restaurantId}?period=${selectedPeriod}`),
        apiService.client.get(`/analytics/metrics/${restaurantId}?period=${selectedPeriod}`),
        apiService.client.get(`/analytics/stats/${restaurantId}`),
      ]);

      setSummary(summaryRes.data.summary || null);
      setMetrics(metricsRes.data.metrics || null);
      setPopularSongs(songsRes.data.stats?.topSongs || []);
    } catch (error) {
      console.error("Erro ao carregar analytics:", error);
      toast.error("Erro ao carregar dados de analytics");
    } finally {
      setLoading(false);
    }
  }, [restaurantId, selectedPeriod]);

  useEffect(() => {
    loadAnalytics();
  }, [loadAnalytics]);

  const exportData = useCallback(async (format: "json" | "csv" = "json") => {
    try {
      const response = await apiService.client.get(`/analytics/export/${restaurantId}?format=${format}&period=${selectedPeriod}`, {
        responseType: "blob",
      });

      const blob = response.data;
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement("a");
      a.href = url;
      a.download = `analytics-${restaurantId}-${selectedPeriod}.${format}`;
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);
      toast.success("Dados exportados com sucesso!");
    } catch (error) {
      console.error("Erro ao exportar:", error);
      toast.error("Erro ao exportar dados");
    }
  }, [restaurantId, selectedPeriod]);

  const StatCard: React.FC<{
    title: string;
    value: string | number;
    change?: number;
    icon: React.ComponentType<{ className?: string }>;
    color: string;
  }> = ({ title, value, change, icon: Icon, color }) => (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
      className="bg-white dark:bg-gray-800 rounded-lg p-6 border border-gray-200 dark:border-gray-700"
    >
      <div className="flex items-center justify-between">
        <div>
          <p className="text-sm font-medium text-gray-600 dark:text-gray-400">{title}</p>
          <p className="text-2xl font-bold text-gray-900 dark:text-white mt-1">{value}</p>
          {change !== undefined && (
            <div className={`flex items-center mt-2 text-sm ${change >= 0 ? "text-green-600" : "text-red-600"}`}>
              <TrendingUp className="w-4 h-4 mr-1" />
              <span>{change >= 0 ? "+" : ""}{change}%</span>
            </div>
          )}
        </div>
        <div className={`p-3 rounded-lg ${color}`}>
          <Icon className="w-6 h-6 text-white" />
        </div>
      </div>
    </motion.div>
  );

  const HourlyChart: React.FC<{ data: AnalyticsMetrics["hourlyActivity"] }> = ({ data }) => {
    const maxActivity = Math.max(...data.map((d) => d.suggestions + d.votes + d.sessions), 1);

    return (
      <div className="space-y-2">
        <h4 className="font-medium text-gray-900 dark:text-white">Atividade por Hora</h4>
        <div className="flex items-end space-x-1 h-32">
          {data.map((hour) => {
            const activity = hour.suggestions + hour.votes + hour.sessions;
            const height = (activity / maxActivity) * 100;
            return (
              <div key={hour.hour} className="flex-1 flex flex-col items-center">
                <div
                  className="w-full bg-blue-500 rounded-t transition-all duration-300 hover:bg-blue-600"
                  style={{ height: `${height}%` }}
                  title={`${hour.hour}:00 - ${activity} atividades`}
                  role="img"
                  aria-label={`Atividade às ${hour.hour}:00: ${activity} interações`}
                />
                <span className="text-xs text-gray-500 dark:text-gray-400 mt-1">{hour.hour.toString().padStart(2, "0")}</span>
              </div>
            );
          })}
        </div>
      </div>
    );
  };

  const TopGenresChart: React.FC<{ genres: AnalyticsMetrics["topGenres"] }> = ({ genres }) => (
    <div className="space-y-3">
      <h4 className="font-medium text-gray-900 dark:text-white">Gêneros Populares</h4>
      {genres.slice(0, 5).map((genre, index) => (
        <div key={genre.genre} className="flex items-center space-x-3">
          <div className="w-8 text-sm text-gray-500 dark:text-gray-400">#{index + 1}</div>
          <div className="flex-1">
            <div className="flex items-center justify-between mb-1">
              <span className="text-sm font-medium text-gray-900 dark:text-white truncate">{genre.genre}</span>
              <span className="text-sm text-gray-500 dark:text-gray-400">{genre.count}</span>
            </div>
            <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
              <div
                className="bg-purple-500 h-2 rounded-full transition-all duration-300"
                style={{ width: `${genre.percentage}%` }}
              />
            </div>
          </div>
        </div>
      ))}
    </div>
  );

  if (loading) {
    return (
      <div className="flex items-center justify-center p-8">
        <RefreshCw className="w-6 h-6 animate-spin text-blue-600 mr-2" />
        <span className="text-gray-600 dark:text-gray-400">Carregando analytics...</span>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-3">
          <div className="p-2 bg-blue-100 dark:bg-blue-900/20 rounded-lg">
            <BarChart3 className="w-6 h-6 text-blue-600 dark:text-blue-400" />
          </div>
          <div>
            <h2 className="text-xl font-semibold text-gray-900 dark:text-white">Analytics do Restaurante</h2>
            <p className="text-sm text-gray-600 dark:text-gray-400">Insights sobre o engajamento dos clientes</p>
          </div>
        </div>
        <div className="flex items-center space-x-3">
          <select
            value={selectedPeriod}
            onChange={(e) => setSelectedPeriod(e.target.value as Period)}
            className="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            aria-label="Selecionar período de análise"
          >
            {periods.map((period) => (
              <option key={period.value} value={period.value}>
                {period.label}
              </option>
            ))}
          </select>
          <button
            onClick={loadAnalytics}
            onKeyDown={(e) => e.key === "Enter" && loadAnalytics()}
            disabled={loading}
            className="flex items-center space-x-2 px-3 py-2 bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors disabled:opacity-50"
            aria-label="Atualizar dados"
          >
            <RefreshCw className={`w-4 h-4 ${loading ? "animate-spin" : ""}`} />
            <span>Atualizar</span>
          </button>
          <button
            onClick={() => exportData("json")}
            onKeyDown={(e) => e.key === "Enter" && exportData("json")}
            className="flex items-center space-x-2 px-3 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
            aria-label="Exportar dados"
          >
            <Download className="w-4 h-4" />
            <span>Exportar</span>
          </button>
        </div>
      </div>

      {/* Summary Cards */}
      {summary && (
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
          <StatCard title="Total de Sugestões" valuing={summary.totalSuggestions} change={summary.growthRate} icon={Music} color="bg-blue-500" />
          <StatCard title="Total de Votos" value={summary.totalVotes} icon={Heart} color="bg-pink-500" />
          <StatCard title="Usuários Ativos" value={summary.activeUsers} icon={Users} color="bg-green-500" />
          <StatCard title="Avaliação Média" value={summary.averageRating.toFixed(1)} icon={Award} color="bg-yellow-500" />
        </div>
      )}

      {/* Tabs */}
      <div className="border-b border-gray-200 dark:border-gray-700">
        <nav className="flex space-x-8">
          {tabs.map((tab) => (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id)}
              onKeyDown={(e) => e.key === "Enter" && setActiveTab(tab.id)}
              className={`flex items-center space-x-2 py-2 px-1 border-b-2 font-medium text-sm transition-colors ${
                activeTab === tab.id
                  ? "border-blue-500 text-blue-600 dark:text-blue-400"
                  : "border-transparent text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300"
              }`}
              aria-label={`Exibir aba ${tab.label}`}
            >
              <tab.icon className="w-4 h-4" />
              <span>{tab.label}</span>
            </button>
          ))}
        </nav>
      </div>

      {/* Tab Content */}
      <AnimatePresence mode="wait">
        <motion.div
          key={activeTab}
          initial={{ opacity: 0, y: 10 }}
          animate={{ opacity: 1, y: 0 }}
          exit={{ opacity: 0, y: -10 }}
          transition={{ duration: 0.2 }}
          className="space-y-6"
        >
          {activeTab === "overview" && metrics && (
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <div className="bg-white dark:bg-gray-800 rounded-lg p-6 border border-gray-200 dark:border-gray-700">
                <HourlyChart data={metrics.hourlyActivity} />
              </div>
              <div className="bg-white dark:bg-gray-800 rounded-lg p-6 border border-gray-200 dark:border-gray-700">
                <TopGenresChart genres={metrics.topGenres} />
              </div>
              <div className="bg-white dark:bg-gray-800 rounded-lg p-6 border border-gray-200 dark:border-gray-700">
                <h4 className="font-medium text-gray-900 dark:text-white mb-4">Métricas Principais</h4>
                <div className="space-y-3">
                  <div className="flex justify-between">
                    <span className="text-gray-600 dark:text-gray-400">Taxa de Engajamento</span>
                    <span className="font-medium text-gray-900 dark:text-white">{metrics.engagementRate.toFixed(1)}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600 dark:text-gray-400">Taxa de Aprovação</span>
                    <span className="font-medium text-gray-900 dark:text-white">{metrics.approvalRate}%</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600 dark:text-gray-400">Duração Média da Sessão</span>
                    <span className="font-medium text-gray-900 dark:text-white">{metrics.averageSessionDuration} min</span>
                  </div>
                </div>
              </div>
              <div className="bg-white dark:bg-gray-800 rounded-lg p-6 border border-gray-200 dark:border-gray-700">
                <h4 className="font-medium text- gray-900 dark:text-white mb-4">Músicas Populares</h4>
                <div className="space-y-3">
                  {popularSongs.length === 0 ? (
                    <p className="text-sm text-gray-500 dark:text-gray-400">Nenhuma música popular encontrada</p>
                  ) : (
                    popularSongs.slice(0, 5).map((song, index) => (
                      <div key={song.id} className="flex items-center space-x-3">
                        <div className="w-6 text-sm text-gray-500 dark:text-gray-400">#{index + 1}</div>
                        <div className="flex-1 min-w-0">
                          <p className="text-sm font-medium text-gray-900 dark:text-white truncate">{song.title}</p>
                          <p className="text-xs text-gray-500 dark:text-gray-400 truncate">{song.artist}</p>
                        </div>
                        <div className="text-sm text-gray-600 dark:text-gray-400">{song.votes} votos</div>
                      </div>
                    ))
                  )}
                </div>
              </div>
            </div>
          )}
          {activeTab === "engagement" && metrics && (
            <div className="bg-white dark:bg-gray-800 rounded-lg p-6 border border-gray-200 dark:border-gray-700">
              <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">Dados de Engajamento</h3>
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <div className="flex justify-between">
                    <span className="text-gray-600 dark:text-gray-400">Sugestões Aprovadas</span>
                    <span className="font-medium text-gray-900 dark:text-white">{metrics.approvedSuggestions}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600 dark:text-gray-400">Sugestões Rejeitadas</span>
                    <span className="font-medium text-gray-900 dark:text-white">{metrics.rejectedSuggestions}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600 dark:text-gray-400">Votos Positivos</span>
                    <span className="font-medium text-gray-900 dark:text-white">{metrics.upvotes}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600 dark:text-gray-400">Votos Negativos</span>
                    <span className="font-medium text-gray-900 dark:text-white">{metrics.downvotes}</span>
                  </div>
                </div>
                <div className="space-y-2">
                  <div className="flex justify-between">
                    <span className="text-gray-600 dark:text-gray-400">Sessões Únicas</span>
                    <span className="font-medium text-gray-900 dark:text-white">{metrics.uniqueSessions}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600 dark:text-gray-400">Taxa de Engajamento</span>
                    <span className="font-medium text-gray-900 dark:text-white">{metrics.engagementRate.toFixed(1)}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600 dark:text-gray-400">Taxa de Aprovação</span>
                    <span className="font-medium text-gray-900 dark:text-white">{metrics.approvalRate}%</span>
                  </div>
                </div>
              </div>
            </div>
          )}
          {activeTab === "music" && (
            <div className="bg-white dark:bg-gray-800 rounded-lg p-6 border border-gray-200 dark:border-gray-700">
              <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">Análise Musical</h3>
              <div className="space-y-3">
                {popularSongs.length === 0 ? (
                  <p className="text-sm text-gray-500 dark:text-gray-400">Nenhuma música encontrada</p>
                ) : (
                  popularSongs.slice(0, 10).map((song, index) => (
                    <div key={song.id} className="flex items-center space-x-3">
                      <div className="w-6 text-sm text-gray-500 dark:text-gray-400">#{index + 1}</div>
                      <div className="flex-1 min-w-0">
                        <p className="text-sm font-medium text-gray-900 dark:text-white truncate">{song.title}</p>
                        <p className="text-xs text-gray-500 dark:text-gray-400 truncate">{song.artist}</p>
                      </div>
                      <div className="text-sm text-gray-600 dark:text-gray-400">{song.votes} votos</div>
                      <div className="text-sm text-gray-600 dark:text-gray-400">{song.plays} reproduções</div>
                    </div>
                  ))
                )}
              </div>
            </div>
          )}
          {activeTab === "users" && metrics && (
            <div className="bg-white dark:bg-gray-800 rounded-lg p-6 border border-gray-200 dark:border-gray-700">
              <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">Análise de Usuários</h3>
              <div className="space-y-2">
                <div className="flex justify-between">
                  <span className="text-gray-600 dark:text-gray-400">Usuários Ativos</span>
                  <span className="font-medium text-gray-900 dark:text-white">{summary?.activeUsers || 0}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600 dark:text-gray-400">Sessões Únicas</span>
                  <span className="font-medium text-gray-900 dark:text-white">{metrics.uniqueSessions}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600 dark:text-gray-400">Duração Média da Sessão</span>
                  <span className="font-medium text-gray-900 dark:text-white">{metrics.averageSessionDuration} min</span>
                </div>
              </div>
            </div>
          )}
        </motion.div>
      </AnimatePresence>
    </div>
  );
});

export default AnalyticsDashboard;
