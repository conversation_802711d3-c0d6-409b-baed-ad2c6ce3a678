import React, { useState, useEffect } from "react";
import { motion } from "framer-motion";
import {
  BarChart3,
  TrendingUp,
  Users,
  Music,
  Clock,
  ThumbsUp,
  ThumbsDown,
  Play,
  RefreshCw,
  Calendar,
  Target,
  Award,
} from "lucide-react";
import { toast } from "react-hot-toast";

interface AnalyticsData {
  totalSuggestions: number;
  totalVotes: number;
  totalPlays: number;
  activeUsers: number;
  averageRating: number;
  topSongs: Array<{
    title: string;
    artist: string;
    plays: number;
    votes: number;
    score: number;
  }>;
  hourlyActivity: Array<{
    hour: number;
    activity: number;
  }>;
  dailyStats: Array<{
    date: string;
    suggestions: number;
    votes: number;
    plays: number;
  }>;
}

const RestaurantAnalytics: React.FC = () => {
  const [analytics, setAnalytics] = useState<AnalyticsData | null>(null);
  const [loading, setLoading] = useState(true);
  const [period, setPeriod] = useState<'7d' | '30d' | '90d'>('7d');

  const restaurantId = "demo-restaurant";

  useEffect(() => {
    loadAnalytics();
  }, [period]);

  const loadAnalytics = async () => {
    try {
      setLoading(true);
      
      // Simular dados de analytics
      const mockAnalytics: AnalyticsData = {
        totalSuggestions: 156,
        totalVotes: 892,
        totalPlays: 1247,
        activeUsers: 34,
        averageRating: 4.2,
        topSongs: [
          {
            title: "Bohemian Rhapsody",
            artist: "Queen",
            plays: 45,
            votes: 89,
            score: 42
          },
          {
            title: "Hotel California",
            artist: "Eagles",
            plays: 38,
            votes: 76,
            score: 35
          },
          {
            title: "Imagine",
            artist: "John Lennon",
            plays: 32,
            votes: 64,
            score: 28
          },
          {
            title: "Sweet Child O' Mine",
            artist: "Guns N' Roses",
            plays: 29,
            votes: 58,
            score: 25
          },
          {
            title: "Stairway to Heaven",
            artist: "Led Zeppelin",
            plays: 27,
            votes: 54,
            score: 22
          }
        ],
        hourlyActivity: Array.from({ length: 24 }, (_, i) => ({
          hour: i,
          activity: Math.floor(Math.random() * 50) + 10
        })),
        dailyStats: Array.from({ length: 7 }, (_, i) => ({
          date: new Date(Date.now() - i * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
          suggestions: Math.floor(Math.random() * 20) + 5,
          votes: Math.floor(Math.random() * 100) + 20,
          plays: Math.floor(Math.random() * 150) + 30
        })).reverse()
      };

      setAnalytics(mockAnalytics);
      
    } catch (error) {
      console.error("Erro ao carregar analytics:", error);
      toast.error("Erro ao carregar analytics");
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <RefreshCw className="w-8 h-8 animate-spin text-blue-600" />
      </div>
    );
  }

  if (!analytics) {
    return (
      <div className="text-center py-12">
        <BarChart3 className="w-12 h-12 text-gray-400 mx-auto mb-4" />
        <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
          Dados não disponíveis
        </h3>
        <p className="text-gray-600 dark:text-gray-400">
          Não foi possível carregar os dados de analytics.
        </p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-2xl font-bold text-gray-900 dark:text-white">
            Analytics do Restaurante
          </h2>
          <p className="text-gray-600 dark:text-gray-400 mt-1">
            Acompanhe o desempenho da sua playlist interativa
          </p>
        </div>

        <div className="flex items-center space-x-3">
          <select
            value={period}
            onChange={(e) => setPeriod(e.target.value as any)}
            className="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
          >
            <option value="7d">Últimos 7 dias</option>
            <option value="30d">Últimos 30 dias</option>
            <option value="90d">Últimos 90 dias</option>
          </select>
          
          <button
            onClick={loadAnalytics}
            className="flex items-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
          >
            <RefreshCw className="w-4 h-4" />
            <span>Atualizar</span>
          </button>
        </div>
      </div>

      {/* Cards de Métricas */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm border border-gray-200 dark:border-gray-700"
        >
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                Total de Sugestões
              </p>
              <p className="text-2xl font-bold text-gray-900 dark:text-white">
                {analytics.totalSuggestions}
              </p>
            </div>
            <div className="w-12 h-12 bg-blue-100 dark:bg-blue-900 rounded-lg flex items-center justify-center">
              <Music className="w-6 h-6 text-blue-600 dark:text-blue-400" />
            </div>
          </div>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.1 }}
          className="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm border border-gray-200 dark:border-gray-700"
        >
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                Total de Votos
              </p>
              <p className="text-2xl font-bold text-gray-900 dark:text-white">
                {analytics.totalVotes}
              </p>
            </div>
            <div className="w-12 h-12 bg-green-100 dark:bg-green-900 rounded-lg flex items-center justify-center">
              <ThumbsUp className="w-6 h-6 text-green-600 dark:text-green-400" />
            </div>
          </div>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2 }}
          className="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm border border-gray-200 dark:border-gray-700"
        >
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                Reproduções
              </p>
              <p className="text-2xl font-bold text-gray-900 dark:text-white">
                {analytics.totalPlays}
              </p>
            </div>
            <div className="w-12 h-12 bg-purple-100 dark:bg-purple-900 rounded-lg flex items-center justify-center">
              <Play className="w-6 h-6 text-purple-600 dark:text-purple-400" />
            </div>
          </div>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.3 }}
          className="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm border border-gray-200 dark:border-gray-700"
        >
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                Usuários Ativos
              </p>
              <p className="text-2xl font-bold text-gray-900 dark:text-white">
                {analytics.activeUsers}
              </p>
            </div>
            <div className="w-12 h-12 bg-orange-100 dark:bg-orange-900 rounded-lg flex items-center justify-center">
              <Users className="w-6 h-6 text-orange-600 dark:text-orange-400" />
            </div>
          </div>
        </motion.div>
      </div>

      {/* Top Músicas */}
      <div className="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm border border-gray-200 dark:border-gray-700">
        <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4 flex items-center space-x-2">
          <Award className="w-5 h-5" />
          <span>Top 5 Músicas</span>
        </h3>
        
        <div className="space-y-4">
          {analytics.topSongs.map((song, index) => (
            <motion.div
              key={index}
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: index * 0.1 }}
              className="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700 rounded-lg"
            >
              <div className="flex items-center space-x-3">
                <div className="w-8 h-8 bg-blue-600 text-white rounded-full flex items-center justify-center text-sm font-bold">
                  {index + 1}
                </div>
                <div>
                  <h4 className="font-medium text-gray-900 dark:text-white">
                    {song.title}
                  </h4>
                  <p className="text-sm text-gray-600 dark:text-gray-400">
                    {song.artist}
                  </p>
                </div>
              </div>
              
              <div className="flex items-center space-x-4 text-sm">
                <div className="text-center">
                  <div className="font-semibold text-gray-900 dark:text-white">
                    {song.plays}
                  </div>
                  <div className="text-gray-500">plays</div>
                </div>
                <div className="text-center">
                  <div className="font-semibold text-gray-900 dark:text-white">
                    {song.votes}
                  </div>
                  <div className="text-gray-500">votos</div>
                </div>
                <div className="text-center">
                  <div className="font-semibold text-green-600">
                    +{song.score}
                  </div>
                  <div className="text-gray-500">score</div>
                </div>
              </div>
            </motion.div>
          ))}
        </div>
      </div>

      {/* Atividade por Hora */}
      <div className="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm border border-gray-200 dark:border-gray-700">
        <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4 flex items-center space-x-2">
          <Clock className="w-5 h-5" />
          <span>Atividade por Hora</span>
        </h3>
        
        <div className="grid grid-cols-12 gap-2">
          {analytics.hourlyActivity.map((data) => (
            <div key={data.hour} className="text-center">
              <div 
                className="bg-blue-200 dark:bg-blue-800 rounded mb-1"
                style={{ height: `${Math.max(data.activity / 2, 10)}px` }}
              />
              <div className="text-xs text-gray-500">
                {data.hour.toString().padStart(2, '0')}h
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Estatísticas Diárias */}
      <div className="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm border border-gray-200 dark:border-gray-700">
        <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4 flex items-center space-x-2">
          <Calendar className="w-5 h-5" />
          <span>Últimos 7 Dias</span>
        </h3>
        
        <div className="space-y-3">
          {analytics.dailyStats.map((day, index) => (
            <div key={index} className="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
              <div className="font-medium text-gray-900 dark:text-white">
                {new Date(day.date).toLocaleDateString('pt-BR', { 
                  weekday: 'short', 
                  day: '2-digit', 
                  month: '2-digit' 
                })}
              </div>
              <div className="flex items-center space-x-6 text-sm">
                <div className="text-center">
                  <div className="font-semibold text-blue-600">{day.suggestions}</div>
                  <div className="text-gray-500">sugestões</div>
                </div>
                <div className="text-center">
                  <div className="font-semibold text-green-600">{day.votes}</div>
                  <div className="text-gray-500">votos</div>
                </div>
                <div className="text-center">
                  <div className="font-semibold text-purple-600">{day.plays}</div>
                  <div className="text-gray-500">plays</div>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default RestaurantAnalytics;
