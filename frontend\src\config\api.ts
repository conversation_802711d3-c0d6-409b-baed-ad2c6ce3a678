// Configuração da API
export const API_CONFIG = {
  BASE_URL: "http://localhost:8001",
  API_VERSION: "v1",
  get API_BASE() {
    return `${this.BASE_URL}/api/${this.API_VERSION}`;
  },

  // Endpoints específicos
  ENDPOINTS: {
    RESTAURANTS: "/restaurants",
    SUGGESTIONS: "/suggestions",
    PLAYBACK: "/playback",
    ANALYTICS: "/analytics",
    NOTIFICATIONS: "/notifications",
  },
};

// Helper para construir URLs da API
export const buildApiUrl = (
  endpoint: string,
  params?: Record<string, string>
) => {
  const url = new URL(`${API_CONFIG.API_BASE}${endpoint}`);

  if (params) {
    Object.entries(params).forEach(([key, value]) => {
      if (value) url.searchParams.append(key, value);
    });
  }

  return url.toString();
};
