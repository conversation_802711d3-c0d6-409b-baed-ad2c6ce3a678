import axios from "axios";
import { AppDataSource } from "../config/database";
import { Payment } from "../models/Payment";
import { Suggestion } from "../models/Suggestion";
import { ClientSession } from "../models/ClientSession";
import { notificationService } from "./NotificationService";
import QRCode from "qrcode";

export interface PixPaymentData {
  amount: number; // Valor em centavos (200 = R$ 2,00)
  description: string;
  external_reference: string; // ID da sugestão
  payer: {
    email?: string;
    first_name?: string;
    last_name?: string;
  };
}

export interface PixPaymentResponse {
  id: string;
  status: "pending" | "approved" | "rejected" | "cancelled";
  qr_code: string;
  qr_code_base64: string;
  ticket_url: string;
  point_of_interaction: {
    transaction_data: {
      qr_code: string;
      qr_code_base64: string;
      ticket_url: string;
    };
  };
}

export interface PaymentStatus {
  id: string;
  status: "pending" | "approved" | "rejected" | "cancelled";
  status_detail: string;
  external_reference: string;
  transaction_amount: number;
  date_created: string;
  date_approved?: string;
}

class PaymentService {
  private paymentRepository = AppDataSource.getRepository(Payment);
  private suggestionRepository = AppDataSource.getRepository(Suggestion);
  private sessionRepository = AppDataSource.getRepository(ClientSession);

  // Gerar QR Code real para PIX
  private async generatePixQRCode(pixCode: string): Promise<string> {
    try {
      const qrCodeDataURL = await QRCode.toDataURL(pixCode, {
        errorCorrectionLevel: "M",
        type: "image/png",
        quality: 0.92,
        margin: 2,
        color: {
          dark: "#000000",
          light: "#FFFFFF",
        },
        width: 256,
      });
      return qrCodeDataURL;
    } catch (error) {
      console.error("❌ Erro ao gerar QR Code PIX:", error);
      // Fallback para um QR code simples
      return "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==";
    }
  }

  private mercadoPagoToken =
    process.env.MERCADO_PAGO_ACCESS_TOKEN || "TEST-YOUR-ACCESS-TOKEN";
  private mercadoPagoPublicKey =
    process.env.MERCADO_PAGO_PUBLIC_KEY || "TEST-YOUR-PUBLIC-KEY";
  private webhookSecret =
    process.env.MERCADO_PAGO_WEBHOOK_SECRET || "webhook_secret";
  private environment = process.env.MERCADO_PAGO_ENVIRONMENT || "sandbox";
  private baseURL =
    this.environment === "production"
      ? "https://api.mercadopago.com/v1"
      : "https://api.mercadopago.com/v1";

  // Criar pagamento Pix
  async createPixPayment(
    suggestionId: string,
    sessionId: string
  ): Promise<{
    paymentId: string;
    qrCode: string;
    qrCodeBase64: string;
    ticketUrl: string;
    amount: number;
  }> {
    try {
      console.log(
        "🎵 PaymentService: Criando pagamento para sugestão:",
        suggestionId
      );

      // Buscar sugestão
      const suggestion = await this.suggestionRepository.findOne({
        where: { id: suggestionId },
        relations: ["restaurant"],
      });

      if (!suggestion) {
        console.error(
          "❌ PaymentService: Sugestão não encontrada:",
          suggestionId
        );
        throw new Error("Sugestão não encontrada");
      }

      console.log("✅ PaymentService: Sugestão encontrada:", suggestion.title);

      // Verificar se já não foi paga
      const existingPayment = await this.paymentRepository.findOne({
        where: {
          suggestionId,
          status: "approved",
        },
      });

      if (existingPayment) {
        throw new Error("Esta música já foi paga");
      }

      // Buscar sessão do cliente (opcional)
      const session = await this.sessionRepository.findOne({
        where: { id: sessionId },
      });

      const paymentData: PixPaymentData = {
        amount: 200, // R$ 2,00 em centavos
        description: `Música: ${suggestion.title} - ${suggestion.artist}`,
        external_reference: suggestionId,
        payer: {
          email: session?.email || "<EMAIL>",
          first_name: session?.clientName || "Cliente",
          last_name: "Jukebox",
        },
      };

      console.log("💳 PaymentService: Criando pagamento no Mercado Pago");
      console.log(
        "🔑 PaymentService: Token:",
        this.mercadoPagoToken?.substring(0, 20) + "..."
      );
      console.log("🌐 PaymentService: URL:", `${this.baseURL}/payments`);
      console.log("💰 PaymentService: Dados do pagamento:", {
        transaction_amount: paymentData.amount / 100,
        description: paymentData.description,
        payment_method_id: "pix",
        external_reference: paymentData.external_reference,
        payer: paymentData.payer,
      });

      // Criar pagamento no Mercado Pago
      const response = await axios.post(
        `${this.baseURL}/payments`,
        {
          transaction_amount: paymentData.amount / 100, // Converter para reais
          description: paymentData.description,
          payment_method_id: "pix",
          external_reference: paymentData.external_reference,
          payer: paymentData.payer,
          notification_url: `${
            process.env.BACKEND_URL || "http://localhost:8001"
          }/api/v1/payments/webhook`,
        },
        {
          headers: {
            Authorization: `Bearer ${this.mercadoPagoToken}`,
            "Content-Type": "application/json",
          },
        }
      );

      const mpPayment = response.data;
      console.log(
        "✅ PaymentService: Pagamento criado no Mercado Pago:",
        mpPayment.id
      );

      console.log(
        "💾 PaymentService: Salvando pagamento no banco:",
        mpPayment.id
      );

      // Salvar pagamento no banco
      const payment = this.paymentRepository.create({
        id: mpPayment.id,
        suggestionId,
        sessionId,
        amount: 200, // centavos
        status: "pending",
        statusDetail: mpPayment.status_detail,
        paymentMethod: "pix",
        externalReference: suggestionId,
        qrCode: mpPayment.point_of_interaction?.transaction_data?.qr_code || "",
        qrCodeBase64:
          mpPayment.point_of_interaction?.transaction_data?.qr_code_base64 ||
          "",
        ticketUrl:
          mpPayment.point_of_interaction?.transaction_data?.ticket_url || "",
        payerEmail: "<EMAIL>",
        payerName: "Cliente Jukebox",
        platformFee: 60, // 30% de R$ 2,00
        restaurantAmount: 140, // 70% de R$ 2,00
        metadata: {
          songTitle: suggestion.title,
          artist: suggestion.artist,
          restaurantName: suggestion.restaurant?.name || "Demo Restaurant",
          paymentSource: "web",
        },
      });

      await this.paymentRepository.save(payment);

      console.log("✅ PaymentService: Pagamento salvo com sucesso");

      // Gerar QR Code real se temos o código PIX
      const pixCode =
        mpPayment.point_of_interaction?.transaction_data?.qr_code || "";
      const qrCodeDataURL = pixCode
        ? await this.generatePixQRCode(pixCode)
        : "";

      return {
        paymentId: mpPayment.id,
        qrCode: pixCode,
        qrCodeBase64: qrCodeDataURL,
        ticketUrl:
          mpPayment.point_of_interaction?.transaction_data?.ticket_url || "",
        amount: 2.0,
      };
    } catch (error: any) {
      console.error("❌ Erro ao criar pagamento Pix:", error);
      console.error(
        "❌ Erro detalhado:",
        error.response?.data || error.message
      );
      console.error("❌ Status:", error.response?.status);
      console.error("❌ Headers:", error.response?.headers);

      // Em desenvolvimento, usar mock data se o Mercado Pago falhar
      console.log("🔍 NODE_ENV:", process.env.NODE_ENV);
      const isDevelopment = process.env.NODE_ENV !== "production";
      console.log("🔍 isDevelopment:", isDevelopment);
      if (isDevelopment) {
        console.log(
          "🧪 PaymentService: Fallback para mock devido ao erro do Mercado Pago"
        );

        const mockPaymentId = `mock_${Date.now()}`;
        const mockQrCode =
          "00020126580014br.gov.bcb.pix0136123e4567-e12b-12d1-a456-426614174000520400005303986540502.005802BR5913MOCK PAYMENT6009SAO PAULO62070503***63041D3D";

        // Buscar sugestão para dados do mock
        const suggestion = await this.suggestionRepository.findOne({
          where: { id: suggestionId },
          relations: ["restaurant"],
        });

        const payment = this.paymentRepository.create({
          id: mockPaymentId,
          suggestionId,
          sessionId,
          amount: 200,
          status: "pending",
          statusDetail: "pending",
          paymentMethod: "pix",
          externalReference: suggestionId,
          qrCode: mockQrCode,
          qrCodeBase64: Buffer.from(mockQrCode).toString("base64"),
          ticketUrl: `https://mock-payment.com/ticket/${mockPaymentId}`,
          payerEmail: "<EMAIL>",
          payerName: "Cliente Jukebox",
          platformFee: 60,
          restaurantAmount: 140,
          metadata: {
            mockPayment: true,
            fallback: true,
            originalError: error.message,
            songTitle: suggestion?.title || "Música Mock",
            artist: suggestion?.artist || "Artista Mock",
            restaurantName: suggestion?.restaurant?.name || "Demo Restaurant",
            paymentSource: "web",
          },
        });

        await this.paymentRepository.save(payment);

        console.log(
          "✅ PaymentService: Pagamento mock salvo com sucesso:",
          payment.id
        );

        // Gerar QR Code real para o código PIX mock
        const mockQrCodeDataURL = await this.generatePixQRCode(mockQrCode);

        return {
          paymentId: mockPaymentId,
          qrCode: mockQrCode,
          qrCodeBase64: mockQrCodeDataURL,
          ticketUrl: `https://mock-payment.com/ticket/${mockPaymentId}`,
          amount: 2.0,
        };
      }

      throw new Error(
        `Erro ao gerar pagamento Pix: ${
          error.response?.data?.message || error.message
        }`
      );
    }
  }

  // Verificar status do pagamento
  async checkPaymentStatus(paymentId: string): Promise<PaymentStatus> {
    try {
      const response = await axios.get(
        `${this.baseURL}/payments/${paymentId}`,
        {
          headers: {
            Authorization: `Bearer ${this.mercadoPagoToken}`,
          },
        }
      );

      return response.data;
    } catch (error) {
      console.error("Erro ao verificar status do pagamento:", error);
      throw new Error("Erro ao verificar pagamento");
    }
  }

  // Processar webhook do Mercado Pago
  async processWebhook(data: any): Promise<void> {
    try {
      if (data.type === "payment") {
        const paymentId = data.data.id;
        const paymentStatus = await this.checkPaymentStatus(paymentId);

        // Atualizar pagamento no banco
        const payment = await this.paymentRepository.findOne({
          where: { id: paymentId },
        });

        if (payment) {
          payment.status = paymentStatus.status;
          payment.statusDetail = paymentStatus.status_detail;

          if (paymentStatus.status === "approved") {
            payment.approvedAt = new Date(
              paymentStatus.date_approved || new Date()
            );
            await this.approvePayment(payment);
          }

          await this.paymentRepository.save(payment);
        }
      }
    } catch (error) {
      console.error("Erro ao processar webhook:", error);
    }
  }

  // Aprovar pagamento e processar sugestão
  private async approvePayment(payment: Payment): Promise<void> {
    try {
      // Buscar sugestão
      const suggestion = await this.suggestionRepository.findOne({
        where: { id: payment.suggestionId },
        relations: ["restaurant", "session"],
      });

      if (!suggestion) {
        console.error(
          "Sugestão não encontrada para pagamento aprovado:",
          payment.suggestionId
        );
        return;
      }

      // Marcar sugestão como paga e aprovada
      suggestion.isPaid = true;
      suggestion.status = "approved";
      suggestion.paidAt = new Date();

      // Adicionar à fila de reprodução com prioridade
      suggestion.queuePosition = await this.getNextQueuePosition(
        suggestion.restaurant.id,
        true
      );

      await this.suggestionRepository.save(suggestion);

      // Notificar aprovação do pagamento
      await notificationService.sendNotification({
        type: "success",
        title: "Pagamento Aprovado!",
        message: `Sua música "${suggestion.title}" foi adicionada à fila prioritária`,
        priority: "high",
        category: "payment",
        targetUsers: [payment.sessionId],
        data: {
          suggestionId: suggestion.id,
          paymentId: payment.id,
          queuePosition: suggestion.queuePosition,
        },
      });

      // Notificar restaurante
      await notificationService.sendToRestaurant(suggestion.restaurant.id, {
        type: "music",
        title: "Nova Música Paga",
        message: `"${suggestion.title}" foi paga e adicionada à fila`,
        priority: "normal",
        category: "payment",
        data: {
          suggestionId: suggestion.id,
          amount: payment.amount / 100,
          clientName: suggestion.session?.clientName || "Cliente",
        },
      });

      console.log(
        `Pagamento aprovado: ${payment.id} - Sugestão: ${suggestion.title}`
      );
    } catch (error) {
      console.error("Erro ao aprovar pagamento:", error);
    }
  }

  // Obter próxima posição na fila (prioridade para pagos)
  private async getNextQueuePosition(
    restaurantId: string,
    isPaid: boolean = false
  ): Promise<number> {
    const query = this.suggestionRepository
      .createQueryBuilder("suggestion")
      .where("suggestion.restaurantId = :restaurantId", { restaurantId })
      .andWhere("suggestion.status = :status", { status: "approved" })
      .andWhere("suggestion.queuePosition IS NOT NULL");

    if (isPaid) {
      // Músicas pagas têm prioridade - inserir no início das pagas
      query.andWhere("suggestion.isPaid = true");
    }

    const maxPosition = await query
      .select("MAX(suggestion.queuePosition)", "maxPosition")
      .getRawOne();

    return (maxPosition?.maxPosition || 0) + 1;
  }

  // Obter histórico de pagamentos
  async getPaymentHistory(
    restaurantId?: string,
    sessionId?: string
  ): Promise<Payment[]> {
    const query = this.paymentRepository
      .createQueryBuilder("payment")
      .leftJoinAndSelect("payment.suggestion", "suggestion")
      .leftJoinAndSelect("suggestion.restaurant", "restaurant")
      .orderBy("payment.createdAt", "DESC");

    if (restaurantId) {
      query.andWhere("restaurant.id = :restaurantId", { restaurantId });
    }

    if (sessionId) {
      query.andWhere("payment.sessionId = :sessionId", { sessionId });
    }

    return query.getMany();
  }

  // Obter estatísticas de pagamento
  async getPaymentStats(
    restaurantId: string,
    period: string = "7d"
  ): Promise<{
    totalRevenue: number;
    totalPayments: number;
    approvedPayments: number;
    pendingPayments: number;
    averageAmount: number;
    dailyRevenue: Array<{ date: string; revenue: number; count: number }>;
  }> {
    const endDate = new Date();
    const startDate = new Date();

    switch (period) {
      case "1d":
        startDate.setDate(endDate.getDate() - 1);
        break;
      case "7d":
        startDate.setDate(endDate.getDate() - 7);
        break;
      case "30d":
        startDate.setDate(endDate.getDate() - 30);
        break;
      default:
        startDate.setDate(endDate.getDate() - 7);
    }

    const payments = await this.paymentRepository
      .createQueryBuilder("payment")
      .leftJoinAndSelect("payment.suggestion", "suggestion")
      .leftJoinAndSelect("suggestion.restaurant", "restaurant")
      .where("restaurant.id = :restaurantId", { restaurantId })
      .andWhere("payment.createdAt BETWEEN :startDate AND :endDate", {
        startDate,
        endDate,
      })
      .getMany();

    const totalPayments = payments.length;
    const approvedPayments = payments.filter(
      (p) => p.status === "approved"
    ).length;
    const pendingPayments = payments.filter(
      (p) => p.status === "pending"
    ).length;
    const totalRevenue =
      payments
        .filter((p) => p.status === "approved")
        .reduce((sum, p) => sum + p.amount, 0) / 100; // Converter para reais

    const averageAmount =
      approvedPayments > 0 ? totalRevenue / approvedPayments : 0;

    // Receita diária
    const dailyRevenue = [];
    for (
      let d = new Date(startDate);
      d <= endDate;
      d.setDate(d.getDate() + 1)
    ) {
      const dateStr = d.toISOString().split("T")[0];
      const dayPayments = payments.filter(
        (p) =>
          p.createdAt.toISOString().split("T")[0] === dateStr &&
          p.status === "approved"
      );

      dailyRevenue.push({
        date: dateStr,
        revenue: dayPayments.reduce((sum, p) => sum + p.amount, 0) / 100,
        count: dayPayments.length,
      });
    }

    return {
      totalRevenue,
      totalPayments,
      approvedPayments,
      pendingPayments,
      averageAmount,
      dailyRevenue,
    };
  }

  // Cancelar pagamento
  async cancelPayment(paymentId: string): Promise<void> {
    try {
      // Cancelar no Mercado Pago
      await axios.put(
        `${this.baseURL}/payments/${paymentId}`,
        { status: "cancelled" },
        {
          headers: {
            Authorization: `Bearer ${this.mercadoPagoToken}`,
            "Content-Type": "application/json",
          },
        }
      );

      // Atualizar no banco
      const payment = await this.paymentRepository.findOne({
        where: { id: paymentId },
      });

      if (payment) {
        payment.status = "cancelled";
        await this.paymentRepository.save(payment);
      }
    } catch (error) {
      console.error("Erro ao cancelar pagamento:", error);
      throw new Error("Erro ao cancelar pagamento");
    }
  }
}

export const paymentService = new PaymentService();
export default PaymentService;
