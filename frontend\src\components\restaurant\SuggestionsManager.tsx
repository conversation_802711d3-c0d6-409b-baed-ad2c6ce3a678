import React, { useState, useEffect } from "react";
import { motion, AnimatePresence } from "framer-motion";
import {
  Music,
  ThumbsUp,
  ThumbsDown,
  Clock,
  CheckCircle,
  XCircle,
  Play,
  Pause,
  SkipForward,
  Users,
  TrendingUp,
  Filter,
  Search,
  RefreshCw,
} from "lucide-react";
import { toast } from "react-hot-toast";
import { apiService } from "../../services/api";
import { buildApiUrl, API_CONFIG } from "../../config/api";

interface Suggestion {
  id: string;
  title: string;
  artist: string;
  youtubeVideoId: string;
  thumbnailUrl: string;
  duration: number;
  upvotes: number;
  downvotes: number;
  status: "pending" | "approved" | "rejected" | "playing" | "played";
  createdAt: string;
  suggestedBy: string;
}

const SuggestionsManager: React.FC = () => {
  const [suggestions, setSuggestions] = useState<Suggestion[]>([]);
  const [loading, setLoading] = useState(true);
  const [filter, setFilter] = useState<
    "all" | "pending" | "approved" | "rejected"
  >("all");
  const [searchTerm, setSearchTerm] = useState("");

  const restaurantId = "demo-restaurant";

  useEffect(() => {
    loadSuggestions();
  }, [filter]);

  const loadSuggestions = async () => {
    try {
      setLoading(true);

      // Construir URL com parâmetros corretos
      const url = new URL(
        `http://localhost:8001/api/v1/suggestions/${restaurantId}`
      );

      if (filter !== "all") {
        url.searchParams.append("status", filter);
      }
      url.searchParams.append("limit", "50");
      url.searchParams.append("page", "1");

      console.log("🔄 Carregando sugestões:", url.toString());

      const response = await fetch(url.toString());

      if (!response.ok) {
        const errorText = await response.text().catch(() => "");
        console.error("🔄 Erro response:", response.status, errorText);
        throw new Error(
          `Erro ${response.status}: ${
            errorText || "Erro ao carregar sugestões"
          }`
        );
      }

      const data = await response.json();
      console.log("🔄 Sugestões carregadas:", data);
      setSuggestions(data.suggestions || []);
    } catch (error) {
      console.error("Erro ao carregar sugestões:", error);

      // Mock data como fallback
      const mockSuggestions = [
        {
          id: "1",
          youtubeVideoId: "example",
          title: "Música de Exemplo",
          artist: "Artista Demo",
          status: "pending",
          createdAt: new Date().toISOString(),
          thumbnailUrl: "https://img.youtube.com/vi/example/mqdefault.jpg",
        },
      ];
      setSuggestions(mockSuggestions);
      toast.info("Carregando dados de exemplo (servidor offline)");
    } finally {
      setLoading(false);
    }
  };

  const approveSuggestion = async (suggestionId: string) => {
    try {
      const response = await fetch(
        `http://localhost:8001/api/v1/suggestions/${suggestionId}/approve`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
        }
      );

      if (!response.ok) {
        throw new Error("Erro ao aprovar sugestão");
      }

      setSuggestions((prev) =>
        prev.map((s) =>
          s.id === suggestionId ? { ...s, status: "approved" } : s
        )
      );
      toast.success("Sugestão aprovada!");
    } catch (error) {
      console.error("Erro ao aprovar sugestão:", error);
      toast.error("Erro ao aprovar sugestão");
    }
  };

  const rejectSuggestion = async (suggestionId: string) => {
    try {
      const response = await fetch(
        `http://localhost:8001/api/v1/suggestions/${suggestionId}/reject`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
        }
      );

      if (!response.ok) {
        throw new Error("Erro ao rejeitar sugestão");
      }

      setSuggestions((prev) =>
        prev.map((s) =>
          s.id === suggestionId ? { ...s, status: "rejected" } : s
        )
      );
      toast.success("Sugestão rejeitada!");
    } catch (error) {
      console.error("Erro ao rejeitar sugestão:", error);
      toast.error("Erro ao rejeitar sugestão");
    }
  };

  const filteredSuggestions = suggestions.filter(
    (suggestion) =>
      suggestion.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
      suggestion.artist.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const getStatusColor = (status: string) => {
    switch (status) {
      case "pending":
        return "bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-400";
      case "approved":
        return "bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400";
      case "rejected":
        return "bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-400";
      case "playing":
        return "bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-400";
      case "played":
        return "bg-gray-100 text-gray-800 dark:bg-gray-900/30 dark:text-gray-400";
      default:
        return "bg-gray-100 text-gray-800 dark:bg-gray-900/30 dark:text-gray-400";
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "pending":
        return <Clock className="w-4 h-4" />;
      case "approved":
        return <CheckCircle className="w-4 h-4" />;
      case "rejected":
        return <XCircle className="w-4 h-4" />;
      case "playing":
        return <Play className="w-4 h-4" />;
      case "played":
        return <CheckCircle className="w-4 h-4" />;
      default:
        return <Clock className="w-4 h-4" />;
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-2xl font-bold text-gray-900 dark:text-white">
            Gerenciar Sugestões
          </h2>
          <p className="text-gray-600 dark:text-gray-400 mt-1">
            Modere e gerencie as sugestões dos clientes
          </p>
        </div>

        <button
          onClick={loadSuggestions}
          disabled={loading}
          className="flex items-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50"
        >
          <RefreshCw className={`w-4 h-4 ${loading ? "animate-spin" : ""}`} />
          <span>Atualizar</span>
        </button>
      </div>

      {/* Filtros e busca */}
      <div className="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm">
        <div className="flex flex-col sm:flex-row gap-4">
          <div className="flex-1">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
              <input
                type="text"
                placeholder="Buscar por título ou artista..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full pl-10 pr-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
              />
            </div>
          </div>

          <div className="flex space-x-2">
            {["all", "pending", "approved", "rejected"].map((status) => (
              <button
                key={status}
                onClick={() => setFilter(status as any)}
                className={`px-4 py-2 rounded-lg text-sm font-medium transition-colors ${
                  filter === status
                    ? "bg-blue-600 text-white"
                    : "bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-600"
                }`}
              >
                {status === "all"
                  ? "Todas"
                  : status.charAt(0).toUpperCase() + status.slice(1)}
              </button>
            ))}
          </div>
        </div>
      </div>

      {/* Lista de sugestões */}
      <div className="space-y-4">
        {loading ? (
          <div className="flex justify-center items-center h-64">
            <RefreshCw className="w-8 h-8 animate-spin text-blue-600" />
          </div>
        ) : filteredSuggestions.length > 0 ? (
          <AnimatePresence>
            {filteredSuggestions.map((suggestion) => (
              <motion.div
                key={suggestion.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -20 }}
                className="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm border border-gray-200 dark:border-gray-700"
              >
                <div className="flex items-start space-x-4">
                  {/* Thumbnail */}
                  <img
                    src={suggestion.thumbnailUrl}
                    alt={suggestion.title}
                    className="w-20 h-15 object-cover rounded-lg flex-shrink-0"
                  />

                  {/* Conteúdo */}
                  <div className="flex-1 min-w-0">
                    <div className="flex items-start justify-between">
                      <div className="flex-1">
                        <h3 className="text-lg font-semibold text-gray-900 dark:text-white truncate">
                          {suggestion.title}
                        </h3>
                        <p className="text-gray-600 dark:text-gray-400">
                          {suggestion.artist}
                        </p>
                        <p className="text-sm text-gray-500 dark:text-gray-500 mt-1">
                          Sugerido por: {suggestion.suggestedBy} •{" "}
                          {new Date(suggestion.createdAt).toLocaleString()}
                        </p>
                      </div>

                      <div className="flex items-center space-x-2 ml-4">
                        <span
                          className={`px-2 py-1 rounded-full text-xs font-medium flex items-center space-x-1 ${getStatusColor(
                            suggestion.status
                          )}`}
                        >
                          {getStatusIcon(suggestion.status)}
                          <span>{suggestion.status}</span>
                        </span>
                      </div>
                    </div>

                    {/* Votos e ações */}
                    <div className="flex items-center justify-between mt-4">
                      <div className="flex items-center space-x-4">
                        <div className="flex items-center space-x-1 text-green-600">
                          <ThumbsUp className="w-4 h-4" />
                          <span className="text-sm font-medium">
                            {suggestion.upvotes}
                          </span>
                        </div>
                        <div className="flex items-center space-x-1 text-red-600">
                          <ThumbsDown className="w-4 h-4" />
                          <span className="text-sm font-medium">
                            {suggestion.downvotes}
                          </span>
                        </div>
                        <div className="text-sm text-gray-500">
                          Score: {suggestion.upvotes - suggestion.downvotes}
                        </div>
                      </div>

                      {suggestion.status === "pending" && (
                        <div className="flex space-x-2">
                          <button
                            onClick={() => approveSuggestion(suggestion.id)}
                            className="flex items-center space-x-1 px-3 py-1 bg-green-600 text-white rounded-lg hover:bg-green-700 text-sm"
                          >
                            <CheckCircle className="w-4 h-4" />
                            <span>Aprovar</span>
                          </button>
                          <button
                            onClick={() => rejectSuggestion(suggestion.id)}
                            className="flex items-center space-x-1 px-3 py-1 bg-red-600 text-white rounded-lg hover:bg-red-700 text-sm"
                          >
                            <XCircle className="w-4 h-4" />
                            <span>Rejeitar</span>
                          </button>
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              </motion.div>
            ))}
          </AnimatePresence>
        ) : (
          <div className="text-center py-12">
            <Music className="w-12 h-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
              Nenhuma sugestão encontrada
            </h3>
            <p className="text-gray-600 dark:text-gray-400">
              {filter === "all"
                ? "Ainda não há sugestões dos clientes"
                : `Não há sugestões com status "${filter}"`}
            </p>
          </div>
        )}
      </div>
    </div>
  );
};

export default SuggestionsManager;
