import React, { useState } from "react";
import { motion } from "framer-motion";
import { useNavigate } from "react-router-dom";
import {
  Music,
  Search,
  QrCode,
  Users,
  Vote,
  ArrowRight,
  Play,
  BarChart3,
  CheckCircle,
  TrendingUp,
  Shield,
  Zap,
  Globe,
  Award,
  Clock,
  Settings,
  Target,
  Sparkles,
  Star,
  Heart,
  Eye,
  Volume2,
} from "lucide-react";
import Button from "@/components/ui/Button";
import LoginModal from "@/components/auth/LoginModal";

const NewHomePage: React.FC = () => {
  const navigate = useNavigate();
  const [restaurantCode, setRestaurantCode] = useState("");
  const [email, setEmail] = useState("");
  const [showLoginModal, setShowLoginModal] = useState(false);

  const handleJoinRestaurant = () => {
    if (restaurantCode.trim()) {
      navigate(`/restaurant/${restaurantCode.trim()}`);
    }
  };

  const handleDemoAccess = () => {
    navigate("/restaurant/demo-restaurant");
  };

  const handleAdminAccess = () => {
    navigate("/admin/login");
  };

  const handleRestaurantLogin = () => {
    navigate("/login");
  };

  const handleLogin = (credentials: {
    email: string;
    password: string;
    restaurantId?: string;
  }) => {
    // Em produção, fazer autenticação real aqui
    console.log("Login credentials:", credentials);

    // Por enquanto, redirecionar diretamente para o admin
    if (credentials.restaurantId) {
      navigate(`/restaurant/${credentials.restaurantId}/admin`);
    } else {
      navigate("/admin");
    }
  };

  const mainFeatures = [
    {
      icon: QrCode,
      title: "QR Code Inteligente",
      description:
        "Clientes escaneiam QR codes únicos por mesa e acessam instantaneamente o sistema de sugestões musicais",
      color: "from-blue-500 to-cyan-500",
    },
    {
      icon: Music,
      title: "Integração YouTube Premium",
      description:
        "Acesso completo ao catálogo do YouTube sem anúncios, com suas próprias playlists personalizadas",
      color: "from-red-500 to-pink-500",
    },
    {
      icon: Users,
      title: "Experiência Colaborativa",
      description:
        "Clientes sugerem e votam em músicas em tempo real, criando uma atmosfera única e envolvente",
      color: "from-green-500 to-emerald-500",
    },
    {
      icon: BarChart3,
      title: "Analytics Avançados",
      description:
        "Dashboard completo com métricas, relatórios e insights sobre preferências musicais dos clientes",
      color: "from-purple-500 to-violet-500",
    },
  ];

  const benefits = [
    {
      icon: TrendingUp,
      title: "Aumente o Engajamento",
      description:
        "Clientes ficam mais tempo no restaurante quando participam da experiência musical",
      stats: "+35% tempo de permanência",
    },
    {
      icon: Heart,
      title: "Melhore a Satisfação",
      description:
        "Música personalizada pelos próprios clientes resulta em experiências mais memoráveis",
      stats: "94% aprovação dos clientes",
    },
    {
      icon: Target,
      title: "Reduza Reclamações",
      description:
        "Elimine reclamações sobre música inadequada - os clientes escolhem o que querem ouvir",
      stats: "-80% reclamações sobre música",
    },
    {
      icon: Sparkles,
      title: "Diferencial Competitivo",
      description:
        "Seja o primeiro restaurante da região com tecnologia interativa de música",
      stats: "Inovação garantida",
    },
  ];

  const howItWorks = [
    {
      step: "1",
      title: "Cliente Escaneia QR Code",
      description:
        "Cada mesa tem um QR code único que direciona para a interface de sugestões",
      icon: QrCode,
    },
    {
      step: "2",
      title: "Sugere Músicas",
      description:
        "Busca no YouTube ou nas playlists do restaurante e sugere suas favoritas",
      icon: Search,
    },
    {
      step: "3",
      title: "Comunidade Vota",
      description:
        "Outros clientes votam nas sugestões, criando uma fila democrática",
      icon: Vote,
    },
    {
      step: "4",
      title: "Música Toca Automaticamente",
      description:
        "Sistema reproduz as músicas mais votadas sem intervenção manual",
      icon: Play,
    },
  ];

  return (
    <div className="min-h-screen bg-white dark:bg-gray-900">
      {/* Navigation */}
      <nav className="fixed top-0 w-full bg-white/80 dark:bg-gray-900/80 backdrop-blur-md z-40 border-b border-gray-200 dark:border-gray-800">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-4">
            <div className="flex items-center space-x-2">
              <div className="p-2 bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg">
                <Music className="w-6 h-6 text-white" />
              </div>
              <span className="text-xl font-bold text-gray-900 dark:text-white">
                PlaylistInterativa
              </span>
            </div>

            <div className="hidden md:flex items-center space-x-8">
              <a
                href="#features"
                className="text-gray-600 dark:text-gray-300 hover:text-blue-600 transition-colors"
              >
                Recursos
              </a>
              <a
                href="#how-it-works"
                className="text-gray-600 dark:text-gray-300 hover:text-blue-600 transition-colors"
              >
                Como Funciona
              </a>
              <a
                href="#benefits"
                className="text-gray-600 dark:text-gray-300 hover:text-blue-600 transition-colors"
              >
                Benefícios
              </a>
              <Button
                onClick={handleRestaurantLogin}
                variant="outline"
                size="sm"
              >
                Login
              </Button>
              <Button onClick={handleDemoAccess} size="sm">
                Demo Grátis
              </Button>
            </div>
          </div>
        </div>
      </nav>

      {/* Hero Section */}
      <section className="pt-24 pb-20 bg-gradient-to-br from-blue-50 via-white to-purple-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="text-center"
          >
            <div className="inline-flex items-center px-4 py-2 bg-blue-100 dark:bg-blue-900/30 rounded-full text-blue-800 dark:text-blue-200 text-sm font-medium mb-8">
              <Sparkles className="w-4 h-4 mr-2" />
              Revolucione a experiência musical do seu restaurante
            </div>

            <h1 className="text-5xl md:text-7xl font-bold text-gray-900 dark:text-white mb-6 leading-tight">
              Seus Clientes{" "}
              <span className="bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
                Escolhem
              </span>{" "}
              a Música
            </h1>

            <p className="text-xl text-gray-600 dark:text-gray-300 mb-12 max-w-3xl mx-auto leading-relaxed">
              Sistema interativo que permite aos clientes sugerir e votar em
              músicas através de QR codes. Transforme seu restaurante em uma
              experiência única e memorável.
            </p>

            <div className="flex flex-col sm:flex-row gap-6 justify-center items-center mb-16">
              <Button
                onClick={handleDemoAccess}
                size="lg"
                className="bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700 text-lg px-8 py-4"
              >
                <Play className="w-5 h-5 mr-2" />
                Experimentar Demo
              </Button>

              <Button
                onClick={handleAdminAccess}
                variant="outline"
                size="lg"
                className="text-lg px-8 py-4"
              >
                <Settings className="w-5 h-5 mr-2" />
                Área Administrativa
              </Button>
            </div>

            {/* Quick Access */}
            <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-xl p-6 max-w-md mx-auto">
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
                Acesso Rápido ao Restaurante
              </h3>
              <div className="flex items-center space-x-2">
                <input
                  type="text"
                  placeholder="Código do restaurante"
                  value={restaurantCode}
                  onChange={(e) => setRestaurantCode(e.target.value)}
                  className="flex-1 px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                  onKeyPress={(e) =>
                    e.key === "Enter" && handleJoinRestaurant()
                  }
                />
                <Button onClick={handleJoinRestaurant} className="px-6 py-3">
                  <ArrowRight className="w-4 h-4" />
                </Button>
              </div>
            </div>
          </motion.div>
        </div>
      </section>

      {/* Features Section */}
      <section id="features" className="py-20 bg-white dark:bg-gray-900">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold text-gray-900 dark:text-white mb-4">
              Recursos Principais
            </h2>
            <p className="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
              Tecnologia avançada para criar a experiência musical perfeita no
              seu restaurante
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {mainFeatures.map((feature, index) => (
              <motion.div
                key={feature.title}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: index * 0.1 }}
                className="bg-white dark:bg-gray-800 rounded-2xl p-6 shadow-lg hover:shadow-xl transition-shadow"
              >
                <div
                  className={`w-12 h-12 bg-gradient-to-r ${feature.color} rounded-lg flex items-center justify-center mb-4`}
                >
                  <feature.icon className="w-6 h-6 text-white" />
                </div>
                <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-2">
                  {feature.title}
                </h3>
                <p className="text-gray-600 dark:text-gray-300">
                  {feature.description}
                </p>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* How It Works Section */}
      <section id="how-it-works" className="py-20 bg-gray-50 dark:bg-gray-800">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold text-gray-900 dark:text-white mb-4">
              Como Funciona
            </h2>
            <p className="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
              Processo simples e intuitivo que seus clientes vão adorar
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {howItWorks.map((step, index) => (
              <motion.div
                key={step.step}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: index * 0.1 }}
                className="text-center"
              >
                <div className="relative mb-6">
                  <div className="w-16 h-16 bg-blue-600 rounded-full flex items-center justify-center mx-auto mb-4">
                    <step.icon className="w-8 h-8 text-white" />
                  </div>
                  <div className="absolute -top-2 -right-2 w-8 h-8 bg-purple-600 rounded-full flex items-center justify-center text-white font-bold text-sm">
                    {step.step}
                  </div>
                </div>
                <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-2">
                  {step.title}
                </h3>
                <p className="text-gray-600 dark:text-gray-300">
                  {step.description}
                </p>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Benefits Section */}
      <section id="benefits" className="py-20 bg-white dark:bg-gray-900">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold text-gray-900 dark:text-white mb-4">
              Por que Escolher Nossa Solução?
            </h2>
            <p className="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
              Resultados comprovados que transformam a experiência do seu
              restaurante
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            {benefits.map((benefit, index) => (
              <motion.div
                key={benefit.title}
                initial={{ opacity: 0, x: index % 2 === 0 ? -20 : 20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.5, delay: index * 0.1 }}
                className="bg-gradient-to-r from-blue-50 to-purple-50 dark:from-gray-800 dark:to-gray-700 rounded-2xl p-8"
              >
                <div className="flex items-start space-x-4">
                  <div className="w-12 h-12 bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg flex items-center justify-center flex-shrink-0">
                    <benefit.icon className="w-6 h-6 text-white" />
                  </div>
                  <div className="flex-1">
                    <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-2">
                      {benefit.title}
                    </h3>
                    <p className="text-gray-600 dark:text-gray-300 mb-3">
                      {benefit.description}
                    </p>
                    <div className="inline-flex items-center px-3 py-1 bg-green-100 dark:bg-green-900/30 rounded-full text-green-800 dark:text-green-200 text-sm font-medium">
                      <TrendingUp className="w-4 h-4 mr-1" />
                      {benefit.stats}
                    </div>
                  </div>
                </div>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-gradient-to-r from-blue-600 to-purple-600">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
          >
            <h2 className="text-4xl font-bold text-white mb-6">
              Pronto para Revolucionar seu Restaurante?
            </h2>
            <p className="text-xl text-blue-100 mb-8 max-w-2xl mx-auto">
              Junte-se aos restaurantes que já estão oferecendo uma experiência
              musical única aos seus clientes
            </p>

            <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
              <button
                onClick={handleDemoAccess}
                className="inline-flex items-center justify-center px-8 py-4 text-lg font-semibold text-blue-600 bg-white rounded-lg shadow-lg hover:bg-gray-100 transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-white focus:ring-offset-2"
              >
                <Play className="w-5 h-5 mr-2" />
                Testar Gratuitamente
              </button>

              <button
                onClick={handleAdminAccess}
                className="inline-flex items-center justify-center px-8 py-4 text-lg font-semibold text-white border-2 border-white rounded-lg shadow-lg hover:bg-white hover:text-blue-600 transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-white focus:ring-offset-2"
              >
                <Eye className="w-5 h-5 mr-2" />
                Ver Dashboard
              </button>
            </div>
          </motion.div>
        </div>
      </section>

      {/* Footer */}
      <footer className="bg-gray-900 text-white py-12">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
            <div className="col-span-1 md:col-span-2">
              <div className="flex items-center space-x-2 mb-4">
                <div className="p-2 bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg">
                  <Music className="w-6 h-6 text-white" />
                </div>
                <span className="text-xl font-bold">PlaylistInterativa</span>
              </div>
              <p className="text-gray-400 mb-4 max-w-md">
                Transformando a experiência musical em restaurantes através de
                tecnologia interativa e colaborativa.
              </p>
              <div className="flex space-x-4">
                <Button onClick={handleDemoAccess} size="sm">
                  Experimentar Demo
                </Button>
                <Button onClick={handleAdminAccess} variant="outline" size="sm">
                  Área Admin
                </Button>
              </div>
            </div>

            <div>
              <h3 className="text-lg font-semibold mb-4">Recursos</h3>
              <ul className="space-y-2 text-gray-400">
                <li>QR Codes Dinâmicos</li>
                <li>Integração YouTube</li>
                <li>Analytics Avançados</li>
                <li>Dashboard Admin</li>
              </ul>
            </div>

            <div>
              <h3 className="text-lg font-semibold mb-4">Suporte</h3>
              <ul className="space-y-2 text-gray-400">
                <li>Documentação</li>
                <li>Tutoriais</li>
                <li>Suporte Técnico</li>
                <li>FAQ</li>
              </ul>
            </div>
          </div>

          <div className="border-t border-gray-800 mt-8 pt-8 text-center text-gray-400">
            <p>&copy; 2024 PlaylistInterativa. Todos os direitos reservados.</p>
          </div>
        </div>
      </footer>

      {/* Login Modal */}
      <LoginModal
        isOpen={showLoginModal}
        onClose={() => setShowLoginModal(false)}
        onLogin={handleLogin}
      />
    </div>
  );
};

export default NewHomePage;
