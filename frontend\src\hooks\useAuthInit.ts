import { useEffect } from "react";
import { useAuth } from "@/store";
import { apiService } from "@/services/api";

/**
 * Hook para inicializar o estado de autenticação
 * Verifica se há um token válido e restaura o estado do usuário
 */
export const useAuthInit = () => {
  const { authToken, setUser, setAuthToken, isAuthenticated } = useAuth();

  useEffect(() => {
    const initializeAuth = () => {
      // Se já está autenticado, não precisa fazer nada
      if (isAuthenticated && authToken) {
        return;
      }

      // Verificar se há token no localStorage
      const storedToken = localStorage.getItem("authToken");
      if (!storedToken) {
        return;
      }

      // Definir o token no serviço da API
      apiService.setAuthToken(storedToken);

      // Restaurar estado de autenticação sem fazer chamada à API
      setAuthToken(storedToken);

      console.log("Token de autenticação restaurado");
    };

    initializeAuth();
  }, [authToken, setUser, setAuthToken, isAuthenticated]);
};

export default useAuthInit;
