version: "3.8"

services:
  # Banco de dados PostgreSQL
  postgres:
    image: postgres:15-alpine
    container_name: restaurant-playlist-db
    restart: unless-stopped
    environment:
      POSTGRES_DB: restaurant_playlist
      POSTGRES_USER: restaurant_user
      POSTGRES_PASSWORD: restaurant_pass
      POSTGRES_INITDB_ARGS: "--encoding=UTF-8 --lc-collate=C --lc-ctype=C"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./database/init.sql:/docker-entrypoint-initdb.d/init.sql:ro
    ports:
      - "8002:5432"
    networks:
      - restaurant-network
    healthcheck:
      test:
        ["CMD-SHELL", "pg_isready -U restaurant_user -d restaurant_playlist"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Cache Redis
  redis:
    image: redis:7-alpine
    container_name: restaurant-playlist-redis
    restart: unless-stopped
    command: redis-server --appendonly yes --requirepass redis_password
    volumes:
      - redis_data:/data
    ports:
      - "8003:6379"
    networks:
      - restaurant-network
    healthcheck:
      test: ["CMD", "redis-cli", "--raw", "incr", "ping"]
      interval: 10s
      timeout: 3s
      retries: 5

  # Backend API
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: restaurant-playlist-api
    restart: unless-stopped
    environment:
      NODE_ENV: ${NODE_ENV:-development}
      PORT: 5000
      DATABASE_URL: **********************************************************/restaurant_playlist
      REDIS_URL: redis://:redis_password@redis:6379
      JWT_SECRET: ${JWT_SECRET:-super-secret-jwt-key-change-in-production}
      YOUTUBE_API_KEY: ${YOUTUBE_API_KEY}
      YOUTUBE_API_QUOTA_LIMIT: ${YOUTUBE_API_QUOTA_LIMIT:-10000}
      FRONTEND_URL: ${FRONTEND_URL:-http://localhost:8000}
      API_URL: ${API_URL:-http://localhost:8001}
      RATE_LIMIT_REQUESTS_PER_MINUTE: ${RATE_LIMIT_REQUESTS_PER_MINUTE:-100}
      LOG_LEVEL: ${LOG_LEVEL:-info}
      MERCADO_PAGO_ACCESS_TOKEN: ${MERCADO_PAGO_ACCESS_TOKEN}
      MERCADO_PAGO_PUBLIC_KEY: ${MERCADO_PAGO_PUBLIC_KEY}
      MERCADO_PAGO_ENVIRONMENT: ${MERCADO_PAGO_ENVIRONMENT:-sandbox}
    volumes:
      - ./backend/logs:/app/logs
    ports:
      - "8001:5000"
    networks:
      - restaurant-network
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:5000/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Frontend React
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
      args:
        VITE_API_URL: ${VITE_API_URL:-http://localhost:8001}
        VITE_WS_URL: ${VITE_WS_URL:-ws://localhost:8001}
    container_name: restaurant-playlist-frontend
    restart: unless-stopped
    ports:
      - "8000:3000"
    networks:
      - restaurant-network
    depends_on:
      - backend
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Adminer para administração do banco (desenvolvimento)
  adminer:
    image: adminer:latest
    container_name: restaurant-playlist-adminer
    restart: unless-stopped
    environment:
      ADMINER_DEFAULT_SERVER: postgres
      ADMINER_DESIGN: pepa-linha
    ports:
      - "8004:8080"
    networks:
      - restaurant-network
    depends_on:
      - postgres
    profiles:
      - dev

volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local

networks:
  restaurant-network:
    driver: bridge
