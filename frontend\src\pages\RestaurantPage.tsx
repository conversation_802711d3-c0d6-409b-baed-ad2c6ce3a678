import React, { useEffect, useState } from "react";
import { useParams, Navigate, useSearchParams } from "react-router-dom";
import { useQuery } from "react-query";
import { motion } from "framer-motion";
import { Music, Search, Clock, Users, Wifi, WifiOff } from "lucide-react";

// Componentes
import SearchMusic from "@/components/music/SearchMusic";
import PlayQueue from "@/components/music/PlayQueue";
import SuggestionsList from "@/components/music/SuggestionsList";
import RestaurantHeader from "@/components/restaurant/RestaurantHeader";
import LoadingSpinner from "@/components/ui/LoadingSpinner";
import ErrorMessage from "@/components/ui/ErrorMessage";
import TabNavigation from "@/components/ui/TabNavigation";
import VoteNotifications from "@/components/realtime/VoteNotifications";
import UserProfileButton from "@/components/user/UserProfileButton";
import ConnectionStatus from "@/components/realtime/ConnectionStatus";

// Hooks e serviços
import { apiService, apiQueries } from "@/services/api";
import { wsService } from "@/services/websocket";
import { useAppStore } from "@/store";
import { useRealTimeQueue } from "@/hooks/useRealTimeQueue";

// Tipos
import { Restaurant } from "@/types";

const RestaurantPage: React.FC = () => {
  const { restaurantId } = useParams<{ restaurantId: string }>();
  const [searchParams] = useSearchParams();
  const [activeTab, setActiveTab] = useState<
    "search" | "queue" | "suggestions"
  >("search");

  // Detectar parâmetros de contexto do QR code
  const tableNumber = searchParams.get("table");
  const contextType = searchParams.get("type");
  const contextId = searchParams.get("id");

  const {
    setCurrentRestaurant,
    connectionStatus,
    isOnline,
    addSuggestion,
    updateSuggestion,
    setPlayQueue,
  } = useAppStore();

  // Hook para atualizações em tempo real da fila
  const {
    queue: realtimeQueue,
    isConnected: wsConnected,
    refreshQueue,
  } = useRealTimeQueue(restaurantId!);

  // Query para obter dados do restaurante
  const {
    data: restaurant,
    isLoading: restaurantLoading,
    error: restaurantError,
  } = useQuery({
    ...apiQueries.restaurant(restaurantId!),
    enabled: !!restaurantId,
    onSuccess: (data: Restaurant) => {
      setCurrentRestaurant(data);
      // Entrar na sala do WebSocket
      wsService.joinRestaurant(restaurantId!);
    },
  });

  // Query para obter fila de reprodução
  const {
    data: playQueue,
    isLoading: queueLoading,
    refetch: refetchQueue,
  } = useQuery({
    ...apiQueries.playQueue(restaurantId!),
    enabled: !!restaurantId,
    onSuccess: (data) => {
      setPlayQueue(data);
    },
  });

  // Query para obter sugestões
  const {
    data: suggestionsData,
    isLoading: suggestionsLoading,
    refetch: refetchSuggestions,
  } = useQuery({
    ...apiQueries.suggestions(restaurantId!, { status: "approved" }),
    enabled: !!restaurantId,
  });

  // Configurar listeners do WebSocket
  useEffect(() => {
    if (!restaurantId) return;

    // Listener para novas sugestões
    const handleNewSuggestion = (suggestion: any) => {
      addSuggestion(suggestion);
      refetchQueue();
    };

    // Listener para atualizações de votos
    const handleVoteUpdate = (data: any) => {
      updateSuggestion(data.suggestionId, {
        voteCount: data.voteCount,
        upvotes: data.upvotes,
        downvotes: data.downvotes,
      });
      refetchQueue();
    };

    // Listener para atualizações da fila
    const handleQueueUpdate = (data: any) => {
      setPlayQueue(data);
    };

    // Registrar listeners
    wsService.on("new-suggestion", handleNewSuggestion);
    wsService.on("vote-update", handleVoteUpdate);
    wsService.on("queue-update", handleQueueUpdate);

    // Cleanup
    return () => {
      wsService.off("new-suggestion", handleNewSuggestion);
      wsService.off("vote-update", handleVoteUpdate);
      wsService.off("queue-update", handleQueueUpdate);
      wsService.leaveRestaurant(restaurantId);
    };
  }, [
    restaurantId,
    addSuggestion,
    updateSuggestion,
    setPlayQueue,
    refetchQueue,
  ]);

  // Verificar se restaurantId é válido
  if (!restaurantId) {
    return <Navigate to="/" replace />;
  }

  // Estados de loading e erro
  if (restaurantLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <LoadingSpinner size="lg" />
      </div>
    );
  }

  if (restaurantError || !restaurant) {
    return (
      <div className="min-h-screen flex items-center justify-center p-4">
        <ErrorMessage
          title="Restaurante não encontrado"
          message="O restaurante que você está procurando não existe ou não está disponível."
          action={{
            label: "Voltar ao início",
            onClick: () => (window.location.href = "/"),
          }}
        />
      </div>
    );
  }

  // Comentado: Permitir acesso sempre, independente do horário
  // if (!restaurant.isOpen) {
  //   return (
  //     <div className="min-h-screen flex items-center justify-center p-4">
  //       <div className="text-center space-y-4">
  //         <div className="w-16 h-16 mx-auto bg-gray-200 dark:bg-gray-700 rounded-full flex items-center justify-center">
  //           <Clock className="w-8 h-8 text-gray-500" />
  //         </div>
  //         <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
  //           Restaurante Fechado
  //         </h1>
  //         <p className="text-gray-600 dark:text-gray-400 max-w-md">
  //           O {restaurant.name} está fechado no momento. Volte durante o horário
  //           de funcionamento para sugerir músicas.
  //         </p>
  //       </div>
  //     </div>
  //   );
  // }

  // Configuração das abas
  const tabs = [
    {
      id: "search" as const,
      label: "Buscar Música",
      icon: Search,
      count: undefined,
    },
    {
      id: "queue" as const,
      label: "Fila",
      icon: Music,
      count: playQueue?.totalItems || 0,
    },
    {
      id: "suggestions" as const,
      label: "Sugestões",
      icon: Users,
      count: suggestionsData?.pagination.total || 0,
    },
  ];

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      {/* Header do restaurante */}
      <RestaurantHeader restaurant={restaurant} />

      {/* Banner de mesa (quando acessado via QR code) */}
      {tableNumber && (
        <div className="bg-blue-50 dark:bg-blue-900/20 border-b border-blue-200 dark:border-blue-800">
          <div className="max-w-4xl mx-auto px-4 py-3">
            <div className="flex items-center space-x-3">
              <div className="w-8 h-8 bg-blue-600 rounded-full flex items-center justify-center flex-shrink-0">
                <span className="text-white text-sm font-bold leading-none">
                  {tableNumber}
                </span>
              </div>
              <div className="flex-1 min-w-0">
                <p className="text-blue-800 dark:text-blue-200 font-medium">
                  Mesa {tableNumber}
                </p>
                <p className="text-blue-600 dark:text-blue-300 text-sm">
                  Você está conectado à mesa {tableNumber}. Suas sugestões serão
                  identificadas por esta mesa.
                </p>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Banner de contexto (para outros tipos de QR code) */}
      {contextType && contextType !== "table" && (
        <div className="bg-green-50 dark:bg-green-900/20 border-b border-green-200 dark:border-green-800">
          <div className="max-w-4xl mx-auto px-4 py-3">
            <div className="flex items-center space-x-3">
              <div className="w-8 h-8 bg-green-600 rounded-full flex items-center justify-center flex-shrink-0">
                <span className="text-white text-xs font-bold leading-none">
                  {contextType.charAt(0).toUpperCase()}
                </span>
              </div>
              <div className="flex-1 min-w-0">
                <p className="text-green-800 dark:text-green-200 font-medium">
                  {contextType === "counter" && "Balcão"}
                  {contextType === "delivery" && "Delivery"}
                  {contextType === "takeout" && "Retirada"}
                  {contextId && ` ${contextId}`}
                </p>
                <p className="text-green-600 dark:text-green-300 text-sm">
                  Acesso via QR code específico para {contextType}.
                </p>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Navegação por abas */}
      <div className="sticky top-0 z-30 bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700">
        <div className="max-w-4xl mx-auto">
          <div className="flex items-center justify-between px-4 py-2">
            <div className="flex-1">
              <TabNavigation
                tabs={tabs}
                activeTab={activeTab}
                onTabChange={setActiveTab}
              />
            </div>
            <div className="flex items-center space-x-3 ml-4">
              <ConnectionStatus
                isConnected={wsConnected}
                status={wsConnected ? "connected" : "disconnected"}
              />
              <UserProfileButton restaurantId={restaurantId!} />
            </div>
          </div>
        </div>
      </div>

      {/* Conteúdo principal */}
      <main className="max-w-4xl mx-auto px-4 py-6">
        <motion.div
          key={activeTab}
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          exit={{ opacity: 0, y: -20 }}
          transition={{ duration: 0.2 }}
        >
          {activeTab === "search" && (
            <SearchMusic
              restaurantId={restaurantId}
              onSuggestionCreated={() => {
                refetchQueue();
                refetchSuggestions();
              }}
            />
          )}

          {activeTab === "queue" && (
            <PlayQueue
              queue={realtimeQueue || playQueue}
              isLoading={queueLoading}
              onVote={() => {
                refetchQueue();
                refreshQueue();
              }}
            />
          )}

          {activeTab === "suggestions" && (
            <SuggestionsList
              suggestions={suggestionsData?.suggestions || []}
              isLoading={suggestionsLoading}
              pagination={suggestionsData?.pagination}
              onVote={() => {
                refetchQueue();
                refetchSuggestions();
              }}
            />
          )}
        </motion.div>
      </main>

      {/* Notificações de voto em tempo real */}
      <VoteNotifications />

      {/* Footer com informações do restaurante */}
      <footer className="bg-white dark:bg-gray-800 border-t border-gray-200 dark:border-gray-700 mt-12">
        <div className="max-w-4xl mx-auto px-4 py-6">
          <div className="text-center space-y-2">
            <p className="text-sm text-gray-600 dark:text-gray-400">
              Sistema de Playlist Interativa
            </p>
            <p className="text-xs text-gray-500 dark:text-gray-500">
              Sugestões são moderadas e podem levar alguns minutos para aparecer
              na fila
            </p>
            {restaurant.settings?.ecad?.enabled && (
              <p className="text-xs text-gray-500 dark:text-gray-500">
                ⚠️ Licenciamento musical: ECAD
              </p>
            )}
          </div>
        </div>
      </footer>
    </div>
  );
};

export default RestaurantPage;
