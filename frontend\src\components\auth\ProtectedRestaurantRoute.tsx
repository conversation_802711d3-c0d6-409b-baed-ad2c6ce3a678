import React from 'react';
import { Navigate } from 'react-router-dom';
import { useAuth } from '@/store';
import LoadingSpinner from '@/components/ui/LoadingSpinner';

interface ProtectedRestaurantRouteProps {
  children: React.ReactNode;
}

/**
 * Componente de rota protegida para dashboard de restaurante
 * Verifica se o usuário está autenticado e tem permissão para acessar o dashboard do restaurante
 */
const ProtectedRestaurantRoute: React.FC<ProtectedRestaurantRouteProps> = ({ children }) => {
  const { isAuthenticated, user, authToken } = useAuth();

  // Se ainda está carregando o estado de autenticação
  if (authToken && !user) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <LoadingSpinner size="lg" />
          <p className="mt-4 text-gray-600 dark:text-gray-400">
            Verificando autenticação...
          </p>
        </div>
      </div>
    );
  }

  // Se não está autenticado, redirecionar para login
  if (!isAuthenticated || !user) {
    return <Navigate to="/login" replace />;
  }

  // Verificar se tem permissão para acessar dashboard de restaurante
  if (!['admin', 'moderator', 'staff'].includes(user.role)) {
    return <Navigate to="/" replace />;
  }

  return <>{children}</>;
};

export default ProtectedRestaurantRoute;
