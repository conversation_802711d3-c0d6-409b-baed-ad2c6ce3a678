import React, { useState, useEffect } from "react";
import { motion, AnimatePresence } from "framer-motion";
import {
  Plus,
  Edit,
  Trash2,
  Eye,
  Settings,
  Shield,
  ShieldOff,
  Play,
  Pause,
  Users,
  Music,
  TrendingUp,
  AlertCircle,
  CheckCircle,
  XCircle,
  RefreshCw,
  ExternalLink,
} from "lucide-react";
import { toast } from "react-hot-toast";
import apiService from "@/services/api";

interface Restaurant {
  id: string;
  name: string;
  email: string;
  description: string;
  isActive: boolean;
  isBlocked: boolean;
  createdAt: string;
  settings: {
    maxTables: number;
    maxResources: number;
    allowSuggestions: boolean;
    moderationRequired: boolean;
    autoPlayEnabled: boolean;
    maxSuggestionsPerUser: number;
  };
  stats: {
    totalSuggestions: number;
    totalVotes: number;
    totalPlays: number;
    activeUsers: number;
  };
}

const RestaurantManagement: React.FC = () => {
  const [restaurants, setRestaurants] = useState<Restaurant[]>([]);
  const [loading, setLoading] = useState(true);
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [editingRestaurant, setEditingRestaurant] = useState<Restaurant | null>(
    null
  );
  const [newRestaurant, setNewRestaurant] = useState({
    name: "",
    email: "",
    password: "",
    description: "",
    address: "",
    phone: "",
  });

  useEffect(() => {
    loadRestaurants();
  }, []);

  const loadRestaurants = async () => {
    try {
      setLoading(true);
      const response = await apiService.client.get("/admin/restaurants");
      setRestaurants(response.data.restaurants || []);
    } catch (error) {
      console.error("Erro ao carregar restaurantes:", error);
      toast.error("Erro ao carregar restaurantes");
    } finally {
      setLoading(false);
    }
  };

  const createRestaurant = async () => {
    try {
      const response = await apiService.client.post(
        "/admin/restaurants",
        newRestaurant
      );

      const data = response.data;
      toast.success("Restaurante criado com sucesso!");

      // Mostrar credenciais de login
      toast.success(
        `Login: ${data.credentials.email}\nSenha: ${data.credentials.password}\nURL: ${data.loginUrl}`,
        { duration: 10000 }
      );

      setShowCreateModal(false);
      setNewRestaurant({
        name: "",
        email: "",
        password: "",
        description: "",
        address: "",
        phone: "",
      });
      loadRestaurants();
    } catch (error: any) {
      console.error("Erro ao criar restaurante:", error);
      toast.error(error.response?.data?.error || "Erro ao criar restaurante");
    }
  };

  const toggleRestaurantStatus = async (
    id: string,
    field: "isActive" | "isBlocked",
    value: boolean
  ) => {
    try {
      const endpoint = field === "isActive" ? "activate" : "block";
      const param = field === "isActive" ? "active" : "blocked";

      const response = await apiService.client.patch(
        `/admin/restaurants/${id}/${endpoint}`,
        { [param]: value }
      );

      const action =
        field === "isActive"
          ? value
            ? "ativado"
            : "desativado"
          : value
          ? "bloqueado"
          : "desbloqueado";
      toast.success(`Restaurante ${action} com sucesso!`);
      loadRestaurants();
    } catch (error: any) {
      console.error("Erro ao atualizar restaurante:", error);
      toast.error(
        error.response?.data?.error || "Erro ao atualizar restaurante"
      );
    }
  };

  const deleteRestaurant = async (id: string) => {
    if (
      !confirm(
        "Tem certeza que deseja deletar este restaurante? Esta ação não pode ser desfeita."
      )
    ) {
      return;
    }

    try {
      await apiService.client.delete(`/admin/restaurants/${id}`);
      toast.success("Restaurante deletado com sucesso!");
      loadRestaurants();
    } catch (error: any) {
      console.error("Erro ao deletar restaurante:", error);
      toast.error(error.response?.data?.error || "Erro ao deletar restaurante");
    }
  };

  const getStatusColor = (restaurant: Restaurant) => {
    if (restaurant.isBlocked) return "text-red-600 bg-red-100";
    if (!restaurant.isActive) return "text-yellow-600 bg-yellow-100";
    return "text-green-600 bg-green-100";
  };

  const getStatusText = (restaurant: Restaurant) => {
    if (restaurant.isBlocked) return "Bloqueado";
    if (!restaurant.isActive) return "Inativo";
    return "Ativo";
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <RefreshCw className="w-8 h-8 animate-spin text-blue-600" />
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-2xl font-bold text-gray-900 dark:text-white">
            Gerenciamento de Restaurantes
          </h2>
          <p className="text-gray-600 dark:text-gray-400 mt-1">
            Gerencie todos os restaurantes da plataforma
          </p>
        </div>

        <button
          onClick={() => setShowCreateModal(true)}
          className="flex items-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
        >
          <Plus className="w-4 h-4" />
          <span>Novo Restaurante</span>
        </button>
      </div>

      {/* Lista de Restaurantes */}
      <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
        {restaurants.map((restaurant) => (
          <motion.div
            key={restaurant.id}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm border border-gray-200 dark:border-gray-700"
          >
            <div className="flex justify-between items-start mb-4">
              <div className="flex-1">
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                  {restaurant.name}
                </h3>
                <p className="text-sm text-gray-600 dark:text-gray-400">
                  {restaurant.email}
                </p>
                <p className="text-xs text-gray-500 mt-1">
                  {restaurant.description}
                </p>
              </div>

              <div
                className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(
                  restaurant
                )}`}
              >
                {getStatusText(restaurant)}
              </div>
            </div>

            {/* Estatísticas */}
            <div className="grid grid-cols-2 gap-4 mb-4">
              <div className="text-center">
                <div className="text-lg font-semibold text-blue-600">
                  {restaurant.stats.totalSuggestions}
                </div>
                <div className="text-xs text-gray-500">Sugestões</div>
              </div>
              <div className="text-center">
                <div className="text-lg font-semibold text-green-600">
                  {restaurant.stats.activeUsers}
                </div>
                <div className="text-xs text-gray-500">Usuários</div>
              </div>
            </div>

            {/* Configurações */}
            <div className="text-xs text-gray-600 dark:text-gray-400 mb-4">
              <div>Mesas: {restaurant.settings.maxTables}</div>
              <div>Recursos: {restaurant.settings.maxResources}</div>
              <div>
                Sugestões:{" "}
                {restaurant.settings.allowSuggestions ? "Ativas" : "Inativas"}
              </div>
            </div>

            {/* Ações */}
            <div className="flex flex-wrap gap-2">
              <button
                onClick={() =>
                  window.open(`http://localhost:3001/login`, "_blank")
                }
                className="flex items-center space-x-1 px-2 py-1 bg-blue-100 text-blue-700 rounded text-xs hover:bg-blue-200"
                title="Visualizar Restaurante"
              >
                <Eye className="w-3 h-3" />
                <span>Ver</span>
              </button>

              <button
                onClick={() => setEditingRestaurant(restaurant)}
                className="flex items-center space-x-1 px-2 py-1 bg-gray-100 text-gray-700 rounded text-xs hover:bg-gray-200"
                title="Configurar"
              >
                <Settings className="w-3 h-3" />
                <span>Config</span>
              </button>

              <button
                onClick={() =>
                  toggleRestaurantStatus(
                    restaurant.id,
                    "isActive",
                    !restaurant.isActive
                  )
                }
                className={`flex items-center space-x-1 px-2 py-1 rounded text-xs ${
                  restaurant.isActive
                    ? "bg-yellow-100 text-yellow-700 hover:bg-yellow-200"
                    : "bg-green-100 text-green-700 hover:bg-green-200"
                }`}
                title={restaurant.isActive ? "Desativar" : "Ativar"}
              >
                {restaurant.isActive ? (
                  <Pause className="w-3 h-3" />
                ) : (
                  <Play className="w-3 h-3" />
                )}
                <span>{restaurant.isActive ? "Pausar" : "Ativar"}</span>
              </button>

              <button
                onClick={() =>
                  toggleRestaurantStatus(
                    restaurant.id,
                    "isBlocked",
                    !restaurant.isBlocked
                  )
                }
                className={`flex items-center space-x-1 px-2 py-1 rounded text-xs ${
                  restaurant.isBlocked
                    ? "bg-green-100 text-green-700 hover:bg-green-200"
                    : "bg-red-100 text-red-700 hover:bg-red-200"
                }`}
                title={restaurant.isBlocked ? "Desbloquear" : "Bloquear"}
              >
                {restaurant.isBlocked ? (
                  <ShieldOff className="w-3 h-3" />
                ) : (
                  <Shield className="w-3 h-3" />
                )}
                <span>{restaurant.isBlocked ? "Desbloquear" : "Bloquear"}</span>
              </button>

              <button
                onClick={() => deleteRestaurant(restaurant.id)}
                className="flex items-center space-x-1 px-2 py-1 bg-red-100 text-red-700 rounded text-xs hover:bg-red-200"
                title="Deletar"
              >
                <Trash2 className="w-3 h-3" />
                <span>Deletar</span>
              </button>
            </div>
          </motion.div>
        ))}
      </div>

      {/* Modal de Criação */}
      <AnimatePresence>
        {showCreateModal && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50"
          >
            <motion.div
              initial={{ scale: 0.9, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              exit={{ scale: 0.9, opacity: 0 }}
              className="bg-white dark:bg-gray-800 rounded-lg p-6 w-full max-w-md mx-4"
            >
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
                Criar Novo Restaurante
              </h3>

              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                    Nome do Restaurante *
                  </label>
                  <input
                    type="text"
                    value={newRestaurant.name}
                    onChange={(e) =>
                      setNewRestaurant({
                        ...newRestaurant,
                        name: e.target.value,
                      })
                    }
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                    placeholder="Ex: Restaurante do João"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                    Email de Login *
                  </label>
                  <input
                    type="email"
                    value={newRestaurant.email}
                    onChange={(e) =>
                      setNewRestaurant({
                        ...newRestaurant,
                        email: e.target.value,
                      })
                    }
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                    placeholder="<EMAIL>"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                    Senha *
                  </label>
                  <input
                    type="password"
                    value={newRestaurant.password}
                    onChange={(e) =>
                      setNewRestaurant({
                        ...newRestaurant,
                        password: e.target.value,
                      })
                    }
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                    placeholder="Senha segura"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                    Descrição
                  </label>
                  <textarea
                    value={newRestaurant.description}
                    onChange={(e) =>
                      setNewRestaurant({
                        ...newRestaurant,
                        description: e.target.value,
                      })
                    }
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                    rows={2}
                    placeholder="Descrição do restaurante"
                  />
                </div>
              </div>

              <div className="flex space-x-3 mt-6">
                <button
                  onClick={() => setShowCreateModal(false)}
                  className="flex-1 px-4 py-2 bg-gray-200 text-gray-700 rounded-lg hover:bg-gray-300"
                >
                  Cancelar
                </button>
                <button
                  onClick={createRestaurant}
                  disabled={
                    !newRestaurant.name ||
                    !newRestaurant.email ||
                    !newRestaurant.password
                  }
                  className="flex-1 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50"
                >
                  Criar
                </button>
              </div>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
};

export default RestaurantManagement;
