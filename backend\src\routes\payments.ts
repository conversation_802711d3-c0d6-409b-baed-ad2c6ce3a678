import { Router } from "express";
import { body, param, query, validationR<PERSON>ult } from "express-validator";
import asyncHandler from "../middleware/asyncHandler";
import { optionalAuth } from "../middleware/auth";
import { ValidationError, NotFoundError } from "../utils/errors";
import { v4 as uuidv4 } from "uuid";
import { AppDataSource } from "../config/database";
import { Restaurant } from "../models/Restaurant";
import { Suggestion } from "../models/Suggestion";
import { logger } from "../utils/logger";
import * as QRCode from "qrcode";

const router = Router();

// Rota de teste
router.get("/test", (req, res) => {
  console.log("🧪 Rota de teste de pagamentos chamada");
  res.json({ message: "Rota de pagamentos funcionando!" });
});

/**
 * @swagger
 * /api/v1/payments/pix/suggestion:
 *   post:
 *     summary: Criar pagamento PIX para sugestão prioritária
 *     tags: [Payments]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - restaurantId
 *               - youtubeId
 *               - title
 *             properties:
 *               restaurantId:
 *                 type: string
 *               youtubeId:
 *                 type: string
 *               title:
 *                 type: string
 *               artist:
 *                 type: string
 *               clientName:
 *                 type: string
 *               tableNumber:
 *                 type: integer
 *               sessionId:
 *                 type: string
 *     responses:
 *       200:
 *         description: Pagamento PIX criado com sucesso
 *       400:
 *         description: Dados inválidos
 *       404:
 *         description: Restaurante não encontrado
 */
router.post(
  "/pix/suggestion",
  [
    body("restaurantId").notEmpty().withMessage("RestaurantId é obrigatório"),
    body("youtubeId").notEmpty().withMessage("YoutubeId é obrigatório"),
    body("title").notEmpty().withMessage("Title é obrigatório"),
  ],
  asyncHandler(async (req, res) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      throw new ValidationError("Dados inválidos", errors.array());
    }

    const {
      restaurantId,
      youtubeId,
      title,
      artist,
      clientName,
      tableNumber,
      sessionId,
    } = req.body;

    // Verificar se o restaurante existe
    const restaurantRepository = AppDataSource.getRepository(Restaurant);
    const restaurant = await restaurantRepository.findOne({
      where: { id: restaurantId },
    });

    if (!restaurant) {
      throw new NotFoundError("Restaurante não encontrado");
    }

    // Gerar ID único para o pagamento
    const paymentId = uuidv4();
    const amount = 2.0; // R$ 2,00 fixo para sugestões prioritárias

    // Dados do PIX (integração simplificada para desenvolvimento)
    const pixData = {
      id: paymentId,
      amount,
      description: `Sugestão prioritária: ${title}`,
      restaurantId,
      youtubeId,
      title,
      artist,
      clientName,
      tableNumber,
      sessionId,
      status: "pending",
      createdAt: new Date().toISOString(),
      expiresAt: new Date(Date.now() + 15 * 60 * 1000).toISOString(), // 15 minutos
      // PIX code simulado para desenvolvimento
      pixCode: `00020126580014BR.GOV.BCB.PIX0136${paymentId}520400005303986540${amount.toFixed(
        2
      )}5802BR5913${restaurant.name}6009SAO PAULO62070503***6304`,
      qrCodeData: await QRCode.toDataURL(
        `00020126580014BR.GOV.BCB.PIX0136${paymentId}520400005303986540${amount.toFixed(
          2
        )}5802BR5913${restaurant.name}6009SAO PAULO62070503***6304`,
        {
          errorCorrectionLevel: "M",
          type: "image/png",
          quality: 0.92,
          margin: 2,
          color: {
            dark: "#000000",
            light: "#FFFFFF",
          },
          width: 256,
        }
      ),
    };

    logger.info(`PIX gerado para restaurante ${restaurantId}:`, {
      paymentId,
      amount,
      title,
    });

    res.json({
      success: true,
      payment: pixData,
      message: "Código PIX gerado com sucesso!",
    });
  })
);

/**
 * @swagger
 * /api/v1/payments/:paymentId/status:
 *   get:
 *     summary: Verificar status do pagamento PIX
 *     tags: [Payments]
 *     parameters:
 *       - in: path
 *         name: paymentId
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Status do pagamento
 *       400:
 *         description: PaymentId inválido
 */
router.get(
  "/:paymentId/status",
  [param("paymentId").notEmpty().withMessage("PaymentId é obrigatório")],
  asyncHandler(async (req, res) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      throw new ValidationError("PaymentId inválido", errors.array());
    }

    const { paymentId } = req.params;

    // Em produção, consultar o gateway de pagamento real (Mercado Pago)
    // Por enquanto, simular verificação baseada no tempo
    const paymentStatus = {
      id: paymentId,
      status: "pending", // pending, paid, expired, cancelled
      paidAt: null,
      amount: 2.0,
    };

    // Simular pagamento aprovado após 30 segundos (apenas para desenvolvimento)
    // Em produção, isso seria uma consulta real ao Mercado Pago
    const timestampFromId = paymentId.split("-")[0];
    const createdTime = parseInt(timestampFromId, 16) || Date.now() - 60000;
    const now = Date.now();

    if (now - createdTime > 30000) {
      paymentStatus.status = "paid";
      paymentStatus.paidAt = new Date().toISOString();
    }

    logger.info(
      `Status verificado para pagamento ${paymentId}:`,
      paymentStatus
    );

    res.json({
      success: true,
      payment: paymentStatus,
    });
  })
);

/**
 * @swagger
 * /api/v1/payments/:paymentId/confirm:
 *   post:
 *     summary: Confirmar pagamento e criar sugestão prioritária
 *     tags: [Payments]
 *     parameters:
 *       - in: path
 *         name: paymentId
 *         required: true
 *         schema:
 *           type: string
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - restaurantId
 *               - youtubeId
 *               - title
 *             properties:
 *               restaurantId:
 *                 type: string
 *               youtubeId:
 *                 type: string
 *               title:
 *                 type: string
 *               artist:
 *                 type: string
 *               clientName:
 *                 type: string
 *               tableNumber:
 *                 type: integer
 *               sessionId:
 *                 type: string
 *     responses:
 *       200:
 *         description: Sugestão prioritária criada com sucesso
 *       400:
 *         description: Pagamento não aprovado ou dados inválidos
 */
router.post(
  "/:paymentId/confirm",
  [
    param("paymentId").notEmpty().withMessage("PaymentId é obrigatório"),
    body("restaurantId").notEmpty().withMessage("RestaurantId é obrigatório"),
    body("youtubeId").notEmpty().withMessage("YoutubeId é obrigatório"),
    body("title").notEmpty().withMessage("Title é obrigatório"),
  ],
  asyncHandler(async (req, res) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      throw new ValidationError("Dados inválidos", errors.array());
    }

    const { paymentId } = req.params;
    const {
      restaurantId,
      youtubeId,
      title,
      artist,
      clientName,
      tableNumber,
      sessionId,
    } = req.body;

    // Verificar se o pagamento foi aprovado
    // Em produção, consultar o gateway de pagamento (Mercado Pago)
    const timestampFromId = paymentId.split("-")[0];
    const createdTime = parseInt(timestampFromId, 16) || Date.now() - 60000;
    const now = Date.now();
    const paymentStatus = now - createdTime > 30000 ? "paid" : "pending";

    if (paymentStatus !== "paid") {
      return res.status(400).json({
        success: false,
        error: "Pagamento não foi aprovado ainda",
      });
    }

    // Criar sugestão prioritária
    const suggestionRepository = AppDataSource.getRepository(Suggestion);
    const suggestion = suggestionRepository.create({
      restaurantId,
      youtubeId,
      title,
      artist: artist || "Artista Desconhecido",
      clientName: clientName || "Anônimo",
      tableNumber: tableNumber || null,
      sessionId: sessionId || null,
      status: "approved",
      isPriority: true,
      paymentId,
      paymentAmount: 2.0,
      paymentStatus: "paid",
      votes: 0,
      createdAt: new Date(),
    });

    const savedSuggestion = await suggestionRepository.save(suggestion);

    logger.info(
      `Sugestão prioritária criada para restaurante ${restaurantId}:`,
      {
        suggestionId: savedSuggestion.id,
        paymentId,
        title,
      }
    );

    res.json({
      success: true,
      suggestion: savedSuggestion,
      message: "Sugestão prioritária criada com sucesso!",
    });
  })
);

/**
 * @swagger
 * /api/v1/payments/history:
 *   get:
 *     summary: Obter histórico de pagamentos de um restaurante
 *     tags: [Payments]
 *     parameters:
 *       - in: query
 *         name: restaurantId
 *         required: true
 *         schema:
 *           type: string
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           default: 50
 *     responses:
 *       200:
 *         description: Histórico de pagamentos
 */
router.get(
  "/history",
  [
    query("restaurantId").notEmpty().withMessage("RestaurantId é obrigatório"),
    query("limit").optional().isInt({ min: 1, max: 100 }),
  ],
  asyncHandler(async (req, res) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      throw new ValidationError("Parâmetros inválidos", errors.array());
    }

    const { restaurantId, limit = 50 } = req.query;

    // Buscar sugestões pagas do restaurante
    const suggestionRepository = AppDataSource.getRepository(Suggestion);
    const paidSuggestions = await suggestionRepository.find({
      where: {
        restaurantId: restaurantId as string,
        paymentStatus: "paid",
        isPriority: true,
      },
      order: { createdAt: "DESC" },
      take: Number(limit),
    });

    const payments = paidSuggestions.map((suggestion) => ({
      id: suggestion.paymentId || suggestion.id,
      amount: suggestion.paymentAmount || 2.0,
      title: suggestion.title,
      artist: suggestion.artist,
      clientName: suggestion.clientName,
      tableNumber: suggestion.tableNumber,
      status: suggestion.paymentStatus || "paid",
      createdAt: suggestion.createdAt,
    }));

    res.json({
      success: true,
      payments,
      total: payments.length,
      restaurantId,
    });
  })
);

/**
 * @swagger
 * /api/v1/payments/webhook:
 *   post:
 *     summary: Webhook do Mercado Pago (placeholder)
 *     tags: [Payments]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *     responses:
 *       200:
 *         description: Webhook processado
 */
router.post(
  "/webhook",
  asyncHandler(async (req, res) => {
    // Em produção, processar webhook do Mercado Pago
    logger.info("Webhook recebido:", req.body);

    res.status(200).json({
      success: true,
      message: "Webhook processado com sucesso",
    });
  })
);

/**
 * @swagger
 * /api/v1/payments/test:
 *   post:
 *     summary: Criar pagamento de teste
 *     tags: [Payments]
 *     responses:
 *       201:
 *         description: Pagamento de teste criado
 */
router.post(
  "/test",
  asyncHandler(async (req, res) => {
    // Criar um pagamento de teste com dados mock
    const pixCode =
      "00020126580014BR.GOV.BCB.PIX0136123e4567-e12b-12d1-a456-426614174000520400005303986540502.005802BR5913Fulano de Tal6008BRASILIA62070503***63041D3D";

    const testPayment = {
      id: `test_${Date.now()}`,
      amount: 2.0,
      status: "pending",
      pixCode,
      qrCodeData: await QRCode.toDataURL(pixCode, {
        errorCorrectionLevel: "M",
        type: "image/png",
        quality: 0.92,
        margin: 2,
        color: {
          dark: "#000000",
          light: "#FFFFFF",
        },
        width: 256,
      }),
      description: "Pagamento de teste",
      expiresAt: new Date(Date.now() + 15 * 60 * 1000).toISOString(),
    };

    res.status(201).json({
      success: true,
      message: "Pagamento de teste criado",
      payment: testPayment,
      note: "Este é um pagamento de teste. Use apenas para desenvolvimento.",
    });
  })
);

export default router;
