import React, { useState, useEffect } from "react";
import { motion, AnimatePresence } from "framer-motion";
import {
  X,
  User,
  Music,
  ThumbsUp,
  Trophy,
  Calendar,
  Clock,
  Star,
  TrendingUp,
  Award,
  Settings,
  LogOut,
  Edit3,
  Save,
  Camera,
  Share2,
  Heart,
  Target,
  Zap,
  Crown,
  Gift,
  Download,
  Upload,
  BarChart3,
  Headphones,
} from "lucide-react";
import { toast } from "react-hot-toast";
import BadgeDisplay from "./BadgeDisplay";

interface ClientProfileProps {
  isOpen: boolean;
  onClose: () => void;
  restaurantId: string;
  sessionId: string;
}

interface UserStats {
  totalSuggestions: number;
  totalVotes: number;
  approvedSuggestions: number;
  consecutiveDays: number;
  topVotedSongs: number;
  averageVotesPerSuggestion: number;
  favoriteGenres: string[];
  sessionDuration: number;
  pageViews: number;
  engagementLevel: string;
}

interface UserProfile {
  name: string;
  avatar?: string;
  joinedAt: string;
  level: number;
  experience: number;
  nextLevelExp: number;
  title: string;
  preferences: {
    favoriteGenres: string[];
    notifications: boolean;
    autoShare: boolean;
  };
}

const ClientProfile: React.FC<ClientProfileProps> = ({
  isOpen,
  onClose,
  restaurantId,
  sessionId,
}) => {
  const [activeTab, setActiveTab] = useState<
    "profile" | "stats" | "badges" | "settings"
  >("profile");
  const [isEditing, setIsEditing] = useState(false);
  const [loading, setLoading] = useState(false);

  const [profile, setProfile] = useState<UserProfile>({
    name: "",
    avatar: "",
    joinedAt: new Date().toISOString(),
    level: 1,
    experience: 0,
    nextLevelExp: 100,
    title: "Novo Ouvinte",
    preferences: {
      favoriteGenres: [],
      notifications: true,
      autoShare: false,
    },
  });

  const [stats, setStats] = useState<UserStats>({
    totalSuggestions: 0,
    totalVotes: 0,
    approvedSuggestions: 0,
    consecutiveDays: 1,
    topVotedSongs: 0,
    averageVotesPerSuggestion: 0,
    favoriteGenres: [],
    sessionDuration: 0,
    pageViews: 0,
    engagementLevel: "Iniciante",
  });

  const [badges, setBadges] = useState<any[]>([]);

  // Carregar dados do perfil
  useEffect(() => {
    if (isOpen) {
      loadProfile();
      loadStats();
      loadBadges();
    }
  }, [isOpen, sessionId]);

  const loadProfile = () => {
    const savedProfile = localStorage.getItem(`clientProfile_${sessionId}`);
    if (savedProfile) {
      setProfile(JSON.parse(savedProfile));
    } else {
      // Perfil padrão
      const defaultProfile = {
        ...profile,
        name: `Cliente ${sessionId.slice(0, 8)}`,
        joinedAt: new Date().toISOString(),
      };
      setProfile(defaultProfile);
      localStorage.setItem(
        `clientProfile_${sessionId}`,
        JSON.stringify(defaultProfile)
      );
    }
  };

  const loadStats = async () => {
    try {
      const response = await fetch(
        `http://localhost:8001/api/v1/client/${sessionId}/stats`
      );
      if (response.ok) {
        const data = await response.json();
        setStats(data.stats);
      }
    } catch (error) {
      console.error("Erro ao carregar estatísticas:", error);
      // Usar dados mock
      setStats({
        totalSuggestions: 5,
        totalVotes: 12,
        approvedSuggestions: 3,
        consecutiveDays: 2,
        topVotedSongs: 1,
        averageVotesPerSuggestion: 2.4,
        favoriteGenres: ["Pop", "Rock"],
        sessionDuration: 45,
        pageViews: 8,
        engagementLevel: "Ativo",
      });
    }
  };

  const loadBadges = async () => {
    try {
      const response = await fetch(
        `http://localhost:8001/api/v1/client/${sessionId}/badges`
      );
      if (response.ok) {
        const data = await response.json();
        setBadges(data.badges);
      }
    } catch (error) {
      console.error("Erro ao carregar badges:", error);
      // Usar badges mock
      setBadges([
        {
          id: "first_suggestion",
          name: "Primeira Sugestão",
          description: "Sugeriu sua primeira música",
          icon: "Music",
          color: "blue",
          unlocked: true,
          currentProgress: 1,
          requirement: 1,
          progressPercentage: 100,
        },
      ]);
    }
  };

  const saveProfile = () => {
    localStorage.setItem(`clientProfile_${sessionId}`, JSON.stringify(profile));
    setIsEditing(false);
    toast.success("Perfil salvo com sucesso!");
  };

  const exportData = () => {
    const data = {
      profile,
      stats,
      badges,
      exportedAt: new Date().toISOString(),
    };

    const blob = new Blob([JSON.stringify(data, null, 2)], {
      type: "application/json",
    });
    const url = URL.createObjectURL(blob);
    const a = document.createElement("a");
    a.href = url;
    a.download = `meu-perfil-musical-${sessionId.slice(0, 8)}.json`;
    a.click();
    URL.revokeObjectURL(url);

    toast.success("Dados exportados com sucesso!");
  };

  const shareProfile = async () => {
    const shareData = {
      title: `Perfil Musical de ${profile.name}`,
      text: `Confira meu perfil musical! Nível ${profile.level}, ${
        stats.totalSuggestions
      } sugestões, ${
        badges.filter((b) => b.unlocked).length
      } badges desbloqueados.`,
      url: window.location.href,
    };

    if (navigator.share) {
      try {
        await navigator.share(shareData);
        toast.success("Perfil compartilhado!");
      } catch (error) {
        console.error("Erro ao compartilhar:", error);
      }
    } else {
      navigator.clipboard.writeText(shareData.text + " " + shareData.url);
      toast.success("Link copiado para a área de transferência!");
    }
  };

  const getLevelProgress = () => {
    return (profile.experience / profile.nextLevelExp) * 100;
  };

  const getEngagementColor = (level: string) => {
    switch (level) {
      case "Iniciante":
        return "text-gray-600 bg-gray-100";
      case "Ativo":
        return "text-blue-600 bg-blue-100";
      case "Engajado":
        return "text-green-600 bg-green-100";
      case "Expert":
        return "text-purple-600 bg-purple-100";
      default:
        return "text-gray-600 bg-gray-100";
    }
  };

  const tabs = [
    { id: "profile", name: "Perfil", icon: User },
    { id: "stats", name: "Estatísticas", icon: BarChart3 },
    { id: "badges", name: "Conquistas", icon: Trophy },
    { id: "settings", name: "Configurações", icon: Settings },
  ];

  if (!isOpen) return null;

  return (
    <AnimatePresence>
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        exit={{ opacity: 0 }}
        className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4"
        onClick={onClose}
      >
        <motion.div
          initial={{ scale: 0.9, opacity: 0 }}
          animate={{ scale: 1, opacity: 1 }}
          exit={{ scale: 0.9, opacity: 0 }}
          onClick={(e) => e.stopPropagation()}
          className="bg-white dark:bg-gray-900 rounded-2xl shadow-2xl w-full max-w-4xl max-h-[90vh] overflow-hidden"
        >
          {/* Header */}
          <div className="bg-gradient-to-r from-blue-600 to-purple-600 p-6 text-white">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-4">
                <div className="w-16 h-16 bg-white/20 rounded-full flex items-center justify-center">
                  {profile.avatar ? (
                    <img
                      src={profile.avatar}
                      alt="Avatar"
                      className="w-full h-full rounded-full object-cover"
                    />
                  ) : (
                    <User className="w-8 h-8" />
                  )}
                </div>
                <div>
                  <h2 className="text-2xl font-bold">{profile.name}</h2>
                  <p className="text-blue-100">{profile.title}</p>
                  <div className="flex items-center space-x-2 mt-1">
                    <span className="text-sm">Nível {profile.level}</span>
                    <div className="w-24 h-2 bg-white/20 rounded-full">
                      <div
                        className="h-full bg-white rounded-full transition-all duration-300"
                        style={{ width: `${getLevelProgress()}%` }}
                      />
                    </div>
                  </div>
                </div>
              </div>

              <div className="flex items-center space-x-2">
                <button
                  onClick={shareProfile}
                  className="p-2 bg-white/20 rounded-lg hover:bg-white/30 transition-colors"
                  title="Compartilhar Perfil"
                >
                  <Share2 className="w-5 h-5" />
                </button>

                <button
                  onClick={exportData}
                  className="p-2 bg-white/20 rounded-lg hover:bg-white/30 transition-colors"
                  title="Exportar Dados"
                >
                  <Download className="w-5 h-5" />
                </button>

                <button
                  onClick={onClose}
                  className="p-2 bg-white/20 rounded-lg hover:bg-white/30 transition-colors"
                >
                  <X className="w-5 h-5" />
                </button>
              </div>
            </div>
          </div>

          {/* Tabs */}
          <div className="border-b border-gray-200 dark:border-gray-700">
            <div className="flex space-x-8 px-6">
              {tabs.map((tab) => {
                const IconComponent = tab.icon;
                return (
                  <button
                    key={tab.id}
                    onClick={() => setActiveTab(tab.id as any)}
                    className={`py-4 border-b-2 font-medium text-sm transition-colors ${
                      activeTab === tab.id
                        ? "border-blue-500 text-blue-600 dark:text-blue-400"
                        : "border-transparent text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300"
                    }`}
                  >
                    <div className="flex items-center space-x-2">
                      <IconComponent className="w-4 h-4" />
                      <span>{tab.name}</span>
                    </div>
                  </button>
                );
              })}
            </div>
          </div>

          {/* Content */}
          <div className="p-6 max-h-96 overflow-y-auto">
            {activeTab === "profile" && (
              <div className="space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      Nome
                    </label>
                    {isEditing ? (
                      <input
                        type="text"
                        value={profile.name}
                        onChange={(e) =>
                          setProfile({ ...profile, name: e.target.value })
                        }
                        className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-white"
                      />
                    ) : (
                      <p className="text-gray-900 dark:text-white">
                        {profile.name}
                      </p>
                    )}
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      Nível de Engajamento
                    </label>
                    <span
                      className={`inline-flex px-3 py-1 rounded-full text-sm font-medium ${getEngagementColor(
                        stats.engagementLevel
                      )}`}
                    >
                      {stats.engagementLevel}
                    </span>
                  </div>
                </div>

                <div className="flex justify-between items-center">
                  <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                    Resumo da Atividade
                  </h3>
                  {isEditing ? (
                    <button
                      onClick={saveProfile}
                      className="flex items-center space-x-2 px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors"
                    >
                      <Save className="w-4 h-4" />
                      <span>Salvar</span>
                    </button>
                  ) : (
                    <button
                      onClick={() => setIsEditing(true)}
                      className="flex items-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                    >
                      <Edit3 className="w-4 h-4" />
                      <span>Editar</span>
                    </button>
                  )}
                </div>

                <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                  <div className="bg-blue-50 dark:bg-blue-900/20 p-4 rounded-lg text-center">
                    <Music className="w-8 h-8 text-blue-600 mx-auto mb-2" />
                    <p className="text-2xl font-bold text-blue-600">
                      {stats.totalSuggestions}
                    </p>
                    <p className="text-sm text-gray-600 dark:text-gray-400">
                      Sugestões
                    </p>
                  </div>

                  <div className="bg-green-50 dark:bg-green-900/20 p-4 rounded-lg text-center">
                    <ThumbsUp className="w-8 h-8 text-green-600 mx-auto mb-2" />
                    <p className="text-2xl font-bold text-green-600">
                      {stats.totalVotes}
                    </p>
                    <p className="text-sm text-gray-600 dark:text-gray-400">
                      Votos
                    </p>
                  </div>

                  <div className="bg-purple-50 dark:bg-purple-900/20 p-4 rounded-lg text-center">
                    <Trophy className="w-8 h-8 text-purple-600 mx-auto mb-2" />
                    <p className="text-2xl font-bold text-purple-600">
                      {badges.filter((b) => b.unlocked).length}
                    </p>
                    <p className="text-sm text-gray-600 dark:text-gray-400">
                      Badges
                    </p>
                  </div>

                  <div className="bg-orange-50 dark:bg-orange-900/20 p-4 rounded-lg text-center">
                    <Clock className="w-8 h-8 text-orange-600 mx-auto mb-2" />
                    <p className="text-2xl font-bold text-orange-600">
                      {stats.sessionDuration}
                    </p>
                    <p className="text-sm text-gray-600 dark:text-gray-400">
                      Minutos
                    </p>
                  </div>
                </div>
              </div>
            )}

            {activeTab === "stats" && (
              <div className="space-y-6">
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                  Estatísticas Detalhadas
                </h3>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="space-y-4">
                    <div className="flex justify-between items-center">
                      <span className="text-gray-600 dark:text-gray-400">
                        Sugestões Aprovadas
                      </span>
                      <span className="font-semibold text-gray-900 dark:text-white">
                        {stats.approvedSuggestions}
                      </span>
                    </div>

                    <div className="flex justify-between items-center">
                      <span className="text-gray-600 dark:text-gray-400">
                        Dias Consecutivos
                      </span>
                      <span className="font-semibold text-gray-900 dark:text-white">
                        {stats.consecutiveDays}
                      </span>
                    </div>

                    <div className="flex justify-between items-center">
                      <span className="text-gray-600 dark:text-gray-400">
                        Músicas Top Votadas
                      </span>
                      <span className="font-semibold text-gray-900 dark:text-white">
                        {stats.topVotedSongs}
                      </span>
                    </div>
                  </div>

                  <div className="space-y-4">
                    <div className="flex justify-between items-center">
                      <span className="text-gray-600 dark:text-gray-400">
                        Média de Votos
                      </span>
                      <span className="font-semibold text-gray-900 dark:text-white">
                        {stats.averageVotesPerSuggestion.toFixed(1)}
                      </span>
                    </div>

                    <div className="flex justify-between items-center">
                      <span className="text-gray-600 dark:text-gray-400">
                        Visualizações
                      </span>
                      <span className="font-semibold text-gray-900 dark:text-white">
                        {stats.pageViews}
                      </span>
                    </div>

                    <div>
                      <span className="text-gray-600 dark:text-gray-400">
                        Gêneros Favoritos
                      </span>
                      <div className="flex flex-wrap gap-2 mt-2">
                        {stats.favoriteGenres.map((genre, index) => (
                          <span
                            key={index}
                            className="px-2 py-1 bg-blue-100 dark:bg-blue-900/20 text-blue-800 dark:text-blue-200 rounded-full text-sm"
                          >
                            {genre}
                          </span>
                        ))}
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            )}

            {activeTab === "badges" && <BadgeDisplay badges={badges} />}

            {activeTab === "settings" && (
              <div className="space-y-6">
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                  Configurações
                </h3>

                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="font-medium text-gray-900 dark:text-white">
                        Notificações
                      </p>
                      <p className="text-sm text-gray-600 dark:text-gray-400">
                        Receber notificações sobre atividades
                      </p>
                    </div>
                    <label className="relative inline-flex items-center cursor-pointer">
                      <input
                        type="checkbox"
                        checked={profile.preferences.notifications}
                        onChange={(e) =>
                          setProfile({
                            ...profile,
                            preferences: {
                              ...profile.preferences,
                              notifications: e.target.checked,
                            },
                          })
                        }
                        className="sr-only peer"
                      />
                      <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 dark:peer-focus:ring-blue-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-blue-600"></div>
                    </label>
                  </div>

                  <div className="flex items-center justify-between">
                    <div>
                      <p className="font-medium text-gray-900 dark:text-white">
                        Compartilhamento Automático
                      </p>
                      <p className="text-sm text-gray-600 dark:text-gray-400">
                        Compartilhar conquistas automaticamente
                      </p>
                    </div>
                    <label className="relative inline-flex items-center cursor-pointer">
                      <input
                        type="checkbox"
                        checked={profile.preferences.autoShare}
                        onChange={(e) =>
                          setProfile({
                            ...profile,
                            preferences: {
                              ...profile.preferences,
                              autoShare: e.target.checked,
                            },
                          })
                        }
                        className="sr-only peer"
                      />
                      <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 dark:peer-focus:ring-blue-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-blue-600"></div>
                    </label>
                  </div>
                </div>

                <div className="pt-4 border-t border-gray-200 dark:border-gray-700">
                  <button
                    onClick={() => {
                      localStorage.removeItem(`clientProfile_${sessionId}`);
                      toast.success("Dados limpos com sucesso!");
                      onClose();
                    }}
                    className="flex items-center space-x-2 px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors"
                  >
                    <LogOut className="w-4 h-4" />
                    <span>Limpar Dados</span>
                  </button>
                </div>
              </div>
            )}
          </div>
        </motion.div>
      </motion.div>
    </AnimatePresence>
  );
};

export default ClientProfile;
