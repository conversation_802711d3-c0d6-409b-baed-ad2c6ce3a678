import React, { useState, useEffect } from "react";
import { motion, AnimatePresence } from "framer-motion";
import {
  Plus,
  Music,
  Youtube,
  Play,
  Pause,
  Edit,
  Trash2,
  Eye,
  Clock,
  Users,
  Settings,
  Link as LinkIcon,
  CheckCircle,
  XCircle,
  AlertCircle,
  RefreshCw,
  Download,
  Power,
  Upload,
} from "lucide-react";
import { toast } from "react-hot-toast";

interface Playlist {
  id: string;
  name: string;
  description: string;
  youtubePlaylistId?: string;
  youtubeUrl?: string;
  videoCount?: number;
  isActive?: boolean;
  isDefault: boolean;
  createdAt: string;
  lastSync?: string;
  thumbnail?: string;
  tags?: string[];
  suggestionsCount?: number;
  schedule?: {
    enabled: boolean;
    startTime: string;
    endTime: string;
    days: string[];
  };
}

const PlaylistManager: React.FC = () => {
  const [playlists, setPlaylists] = useState<Playlist[]>([]);
  const [loading, setLoading] = useState(false);
  const [showAddForm, setShowAddForm] = useState(false);
  const [editingPlaylist, setEditingPlaylist] = useState<Playlist | null>(null);
  const [newPlaylist, setNewPlaylist] = useState({
    name: "",
    description: "",
    youtubeUrl: "",
    tags: "",
    isActive: true,
  });

  const restaurantId = "demo-restaurant";

  useEffect(() => {
    loadPlaylists();
  }, []);

  const loadPlaylists = async () => {
    setLoading(true);
    try {
      // Buscar playlists reais do backend usando o proxy
      const response = await fetch(`/api/v1/playlists/${restaurantId}`, {
        method: "GET",
        headers: {
          "Content-Type": "application/json",
        },
      });

      if (!response.ok) {
        throw new Error("Erro ao carregar playlists");
      }

      const data = await response.json();
      setPlaylists(data.playlists || []);
    } catch (error) {
      console.error("Erro ao carregar playlists:", error);
      toast.error("Erro ao carregar playlists");
    } finally {
      setLoading(false);
    }
  };

  const extractPlaylistId = (url: string): string | null => {
    const regex = /[?&]list=([^#\&\?]*)/;
    const match = url.match(regex);
    return match ? match[1] : null;
  };

  const addPlaylist = async () => {
    if (!newPlaylist.name.trim() || !newPlaylist.youtubeUrl.trim()) {
      toast.error("Nome e URL da playlist são obrigatórios");
      return;
    }

    const playlistId = extractPlaylistId(newPlaylist.youtubeUrl);
    if (!playlistId) {
      toast.error("URL da playlist do YouTube inválida");
      return;
    }

    setLoading(true);
    try {
      // Criar playlist via API usando o proxy
      const response = await fetch("/api/v1/playlists", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          restaurantId: restaurantId,
          name: newPlaylist.name,
          description: newPlaylist.description,
          youtubeUrl: newPlaylist.youtubeUrl,
          tags: newPlaylist.tags
            .split(",")
            .map((tag) => tag.trim())
            .filter(Boolean),
          isActive: newPlaylist.isActive,
        }),
      });

      if (!response.ok) {
        throw new Error("Erro ao criar playlist");
      }

      const data = await response.json();

      // Recarregar playlists
      await loadPlaylists();

      setNewPlaylist({
        name: "",
        description: "",
        youtubeUrl: "",
        tags: "",
        isActive: true,
      });
      setShowAddForm(false);
      toast.success("Playlist adicionada com sucesso!");
    } catch (error) {
      toast.error("Erro ao adicionar playlist");
    } finally {
      setLoading(false);
    }
  };

  const syncPlaylist = async (playlistId: string) => {
    setLoading(true);
    try {
      // Simular sincronização com YouTube API
      await new Promise((resolve) => setTimeout(resolve, 1500));

      setPlaylists((prev) =>
        prev.map((playlist) =>
          playlist.id === playlistId
            ? {
                ...playlist,
                lastSync: new Date().toISOString(),
                videoCount: Math.floor(Math.random() * 50) + 10,
              }
            : playlist
        )
      );

      toast.success("Playlist sincronizada com sucesso!");
    } catch (error) {
      toast.error("Erro ao sincronizar playlist");
    } finally {
      setLoading(false);
    }
  };

  const deletePlaylist = async (playlistId: string) => {
    const playlist = playlists.find((p) => p.id === playlistId);
    if (playlist?.isDefault) {
      toast.error("Não é possível excluir a playlist padrão");
      return;
    }

    if (!confirm("Tem certeza que deseja excluir esta playlist?")) {
      return;
    }

    try {
      const response = await fetch(`/api/v1/playlists/${playlistId}`, {
        method: "DELETE",
      });

      if (!response.ok) {
        throw new Error("Erro ao excluir playlist");
      }

      // Recarregar playlists
      await loadPlaylists();
      toast.success("Playlist excluída com sucesso!");
    } catch (error) {
      console.error("Erro ao excluir playlist:", error);
      toast.error("Erro ao excluir playlist");
    }
  };

  const updatePlaylist = async (playlist: Playlist) => {
    try {
      const response = await fetch(`/api/v1/playlists/${playlist.id}`, {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          name: playlist.name,
          description: playlist.description,
          youtubeUrl: playlist.youtubeUrl,
          isActive: playlist.isActive,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || "Erro ao atualizar playlist");
      }

      // Fechar modal e recarregar playlists
      setEditingPlaylist(null);
      await loadPlaylists();
      toast.success("Playlist atualizada com sucesso!");
    } catch (error) {
      console.error("Erro ao atualizar playlist:", error);
      toast.error(
        error instanceof Error ? error.message : "Erro ao atualizar playlist"
      );
    }
  };

  const togglePlaylistStatus = async (playlist: Playlist) => {
    try {
      const newStatus = !playlist.isActive;
      const response = await fetch(`/api/v1/playlists/${playlist.id}`, {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          name: playlist.name,
          description: playlist.description,
          youtubeUrl: playlist.youtubeUrl,
          isActive: newStatus,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(
          errorData.error || "Erro ao alterar status da playlist"
        );
      }

      // Recarregar playlists
      await loadPlaylists();
      toast.success(
        `Playlist ${newStatus ? "ativada" : "desativada"} com sucesso!`
      );
    } catch (error) {
      console.error("Erro ao alterar status da playlist:", error);
      toast.error(
        error instanceof Error
          ? error.message
          : "Erro ao alterar status da playlist"
      );
    }
  };

  const setAsDefault = (playlistId: string) => {
    setPlaylists((prev) =>
      prev.map((playlist) => ({
        ...playlist,
        isDefault: playlist.id === playlistId,
      }))
    );
    toast.success("Playlist definida como padrão");
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString("pt-BR", {
      day: "2-digit",
      month: "2-digit",
      year: "numeric",
      hour: "2-digit",
      minute: "2-digit",
    });
  };

  const getStatusColor = (isActive: boolean) => {
    return isActive
      ? "bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400"
      : "bg-gray-100 text-gray-800 dark:bg-gray-900/30 dark:text-gray-400";
  };

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-2xl font-bold text-gray-900 dark:text-white">
            Gerenciamento de Playlists
          </h2>
          <p className="text-gray-600 dark:text-gray-400 mt-1">
            Adicione e gerencie suas playlists do YouTube Premium
          </p>
        </div>

        <button
          onClick={() => setShowAddForm(!showAddForm)}
          className="bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 flex items-center space-x-2"
        >
          <Plus className="w-4 h-4" />
          <span>Nova Playlist</span>
        </button>
      </div>

      {/* Formulário de adição */}
      <AnimatePresence>
        {showAddForm && (
          <motion.div
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: "auto" }}
            exit={{ opacity: 0, height: 0 }}
            className="bg-white dark:bg-gray-800 rounded-lg shadow p-6"
          >
            <h3 className="text-lg font-semibold mb-4">
              Adicionar Nova Playlist
            </h3>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Nome da Playlist *
                </label>
                <input
                  type="text"
                  value={newPlaylist.name}
                  onChange={(e) =>
                    setNewPlaylist({ ...newPlaylist, name: e.target.value })
                  }
                  placeholder="Ex: Música Ambiente - Almoço"
                  className="w-full border border-gray-300 dark:border-gray-600 rounded-lg px-3 py-2 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  URL da Playlist do YouTube *
                </label>
                <input
                  type="url"
                  value={newPlaylist.youtubeUrl}
                  onChange={(e) =>
                    setNewPlaylist({
                      ...newPlaylist,
                      youtubeUrl: e.target.value,
                    })
                  }
                  placeholder="https://www.youtube.com/playlist?list=..."
                  className="w-full border border-gray-300 dark:border-gray-600 rounded-lg px-3 py-2 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                />
              </div>
            </div>

            <div className="mb-4">
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Descrição
              </label>
              <textarea
                value={newPlaylist.description}
                onChange={(e) =>
                  setNewPlaylist({
                    ...newPlaylist,
                    description: e.target.value,
                  })
                }
                placeholder="Descrição da playlist..."
                rows={3}
                className="w-full border border-gray-300 dark:border-gray-600 rounded-lg px-3 py-2 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
              />
            </div>

            <div className="mb-4">
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Tags (separadas por vírgula)
              </label>
              <input
                type="text"
                value={newPlaylist.tags}
                onChange={(e) =>
                  setNewPlaylist({ ...newPlaylist, tags: e.target.value })
                }
                placeholder="ambiente, jazz, relaxante"
                className="w-full border border-gray-300 dark:border-gray-600 rounded-lg px-3 py-2 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
              />
            </div>

            <div className="flex items-center justify-between">
              <label className="flex items-center space-x-2">
                <input
                  type="checkbox"
                  checked={newPlaylist.isActive}
                  onChange={(e) =>
                    setNewPlaylist({
                      ...newPlaylist,
                      isActive: e.target.checked,
                    })
                  }
                  className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                />
                <span className="text-sm text-gray-700 dark:text-gray-300">
                  Ativar playlist
                </span>
              </label>

              <div className="flex space-x-2">
                <button
                  onClick={() => setShowAddForm(false)}
                  className="bg-gray-600 text-white px-4 py-2 rounded-lg hover:bg-gray-700"
                >
                  Cancelar
                </button>
                <button
                  onClick={addPlaylist}
                  disabled={loading}
                  className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 disabled:opacity-50 flex items-center space-x-2"
                >
                  {loading ? (
                    <RefreshCw className="w-4 h-4 animate-spin" />
                  ) : (
                    <Plus className="w-4 h-4" />
                  )}
                  <span>{loading ? "Adicionando..." : "Adicionar"}</span>
                </button>
              </div>
            </div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Lista de playlists */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {playlists.map((playlist) => (
          <motion.div
            key={playlist.id}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-700 overflow-hidden"
          >
            {/* Header da playlist */}
            <div className="p-4 border-b border-gray-200 dark:border-gray-700">
              <div className="flex items-start justify-between">
                <div className="flex items-start space-x-3">
                  {playlist.thumbnail ? (
                    <img
                      src={playlist.thumbnail}
                      alt={playlist.name}
                      className="w-12 h-9 object-cover rounded flex-shrink-0"
                    />
                  ) : (
                    <div className="w-12 h-9 bg-gray-200 dark:bg-gray-700 rounded flex items-center justify-center flex-shrink-0">
                      <Music className="w-4 h-4 text-gray-400" />
                    </div>
                  )}

                  <div className="flex-1 min-w-0">
                    <div className="flex items-center space-x-2">
                      <h3 className="font-semibold text-gray-900 dark:text-white truncate">
                        {playlist.name}
                      </h3>
                      {playlist.isDefault && (
                        <span className="px-2 py-1 bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-400 text-xs rounded-full flex-shrink-0">
                          Padrão
                        </span>
                      )}
                    </div>
                    <p className="text-sm text-gray-600 dark:text-gray-400 truncate">
                      {playlist.description}
                    </p>
                  </div>
                </div>

                <span
                  className={`px-2 py-1 rounded-full text-xs font-medium flex-shrink-0 ${getStatusColor(
                    playlist.isActive ?? true
                  )}`}
                >
                  {playlist.isActive ?? true ? "Ativa" : "Inativa"}
                </span>
              </div>
            </div>

            {/* Conteúdo da playlist */}
            <div className="p-4">
              <div className="flex items-center space-x-4 text-sm text-gray-600 dark:text-gray-400 mb-3">
                <div className="flex items-center space-x-1">
                  <Music className="w-4 h-4 flex-shrink-0" />
                  <span>{playlist.videoCount ?? 0} vídeos</span>
                </div>

                <div className="flex items-center space-x-1">
                  <Clock className="w-4 h-4 flex-shrink-0" />
                  <span>Criada: {formatDate(playlist.createdAt)}</span>
                </div>

                {playlist.lastSync && (
                  <div className="flex items-center space-x-1">
                    <RefreshCw className="w-4 h-4 flex-shrink-0" />
                    <span>Sync: {formatDate(playlist.lastSync)}</span>
                  </div>
                )}
              </div>

              {/* Tags */}
              {playlist.tags && playlist.tags.length > 0 && (
                <div className="flex flex-wrap gap-1 mb-3">
                  {playlist.tags.map((tag) => (
                    <span
                      key={tag}
                      className="px-2 py-1 bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 text-xs rounded"
                    >
                      {tag}
                    </span>
                  ))}
                </div>
              )}

              {/* Horário programado */}
              {playlist.schedule?.enabled && (
                <div className="mb-3 p-2 bg-blue-50 dark:bg-blue-900/20 rounded text-xs">
                  <p className="text-blue-800 dark:text-blue-200">
                    📅 Programada: {playlist.schedule.startTime} -{" "}
                    {playlist.schedule.endTime}
                  </p>
                </div>
              )}

              {/* Ações */}
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <button
                    onClick={() => togglePlaylistStatus(playlist)}
                    className={`p-2 rounded-lg ${
                      playlist.isActive ?? true
                        ? "text-green-600 hover:bg-green-100 dark:hover:bg-green-900/20"
                        : "text-gray-600 hover:bg-gray-100 dark:hover:bg-gray-700"
                    }`}
                    title={playlist.isActive ?? true ? "Desativar" : "Ativar"}
                  >
                    {playlist.isActive ?? true ? (
                      <Pause className="w-4 h-4" />
                    ) : (
                      <Play className="w-4 h-4" />
                    )}
                  </button>

                  <button
                    onClick={() => syncPlaylist(playlist.id)}
                    disabled={loading}
                    className="p-2 text-blue-600 hover:bg-blue-100 dark:hover:bg-blue-900/20 rounded-lg"
                    title="Sincronizar"
                  >
                    <RefreshCw
                      className={`w-4 h-4 ${loading ? "animate-spin" : ""}`}
                    />
                  </button>

                  {!playlist.isDefault && (
                    <button
                      onClick={() => setAsDefault(playlist.id)}
                      className="p-2 text-yellow-600 hover:bg-yellow-100 dark:hover:bg-yellow-900/20 rounded-lg"
                      title="Definir como padrão"
                    >
                      <CheckCircle className="w-4 h-4" />
                    </button>
                  )}
                </div>

                <div className="flex items-center space-x-1">
                  {/* Botão Ver no YouTube */}
                  {playlist.youtubeUrl && (
                    <a
                      href={playlist.youtubeUrl}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="p-2 text-red-600 hover:bg-red-100 dark:hover:bg-red-900/20 rounded-lg"
                      title="Ver no YouTube"
                    >
                      <Youtube className="w-4 h-4" />
                    </a>
                  )}

                  {/* Botão Ativar/Desativar */}
                  <button
                    onClick={() => togglePlaylistStatus(playlist)}
                    className={`p-2 rounded-lg ${
                      playlist.isActive
                        ? "text-green-600 hover:bg-green-100 dark:hover:bg-green-900/20"
                        : "text-gray-400 hover:bg-gray-100 dark:hover:bg-gray-700"
                    }`}
                    title={
                      playlist.isActive
                        ? "Desativar playlist"
                        : "Ativar playlist"
                    }
                  >
                    <Power className="w-4 h-4" />
                  </button>

                  {/* Botão Editar */}
                  <button
                    onClick={() => setEditingPlaylist(playlist)}
                    className="p-2 text-gray-600 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg"
                    title="Editar"
                  >
                    <Edit className="w-4 h-4" />
                  </button>

                  {/* Botão Excluir */}
                  {!playlist.isDefault && (
                    <button
                      onClick={() => deletePlaylist(playlist.id)}
                      className="p-2 text-red-600 hover:bg-red-100 dark:hover:bg-red-900/20 rounded-lg"
                      title="Excluir"
                    >
                      <Trash2 className="w-4 h-4" />
                    </button>
                  )}
                </div>
              </div>
            </div>
          </motion.div>
        ))}
      </div>

      {/* Guia de uso do YouTube Premium */}
      <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-6">
        <div className="flex items-start space-x-3">
          <Youtube className="w-6 h-6 text-blue-600 dark:text-blue-400 flex-shrink-0 mt-0.5" />
          <div>
            <h3 className="text-lg font-semibold text-blue-900 dark:text-blue-100 mb-2">
              Como usar suas playlists do YouTube Premium
            </h3>
            <div className="space-y-2 text-sm text-blue-800 dark:text-blue-200">
              <p>
                <strong>1. Crie suas playlists no YouTube:</strong>
              </p>
              <ul className="list-disc list-inside ml-4 space-y-1">
                <li>Acesse o YouTube e faça login na sua conta Premium</li>
                <li>
                  Crie playlists temáticas (ex: "Ambiente Jantar", "Happy Hour",
                  "Música de Fundo")
                </li>
                <li>
                  Adicione músicas adequadas para cada momento do seu
                  restaurante
                </li>
              </ul>

              <p className="mt-3">
                <strong>2. Configure as playlists aqui:</strong>
              </p>
              <ul className="list-disc list-inside ml-4 space-y-1">
                <li>
                  Copie a URL da playlist do YouTube (ex:
                  https://www.youtube.com/playlist?list=...)
                </li>
                <li>Cole a URL no formulário acima</li>
                <li>
                  Configure horários específicos para cada playlist (opcional)
                </li>
                <li>Ative/desative conforme necessário</li>
              </ul>

              <p className="mt-3">
                <strong>3. Benefícios do YouTube Premium:</strong>
              </p>
              <ul className="list-disc list-inside ml-4 space-y-1">
                <li>Reprodução sem anúncios para uma experiência contínua</li>
                <li>Acesso a milhões de músicas de alta qualidade</li>
                <li>Playlists sempre atualizadas automaticamente</li>
                <li>Controle total sobre o conteúdo do seu restaurante</li>
              </ul>
            </div>
          </div>
        </div>
      </div>

      {playlists.length === 0 && !loading && (
        <div className="text-center py-12">
          <Youtube className="w-12 h-12 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
            Nenhuma playlist configurada
          </h3>
          <p className="text-gray-600 dark:text-gray-400 mb-4">
            Adicione suas playlists do YouTube Premium para começar
          </p>
          <button
            onClick={() => setShowAddForm(true)}
            className="bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700"
          >
            Adicionar Primeira Playlist
          </button>
        </div>
      )}

      {/* Modal de Edição */}
      {editingPlaylist && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white dark:bg-gray-800 rounded-lg p-6 w-full max-w-md mx-4">
            <h3 className="text-lg font-semibold mb-4 text-gray-900 dark:text-white">
              Editar Playlist
            </h3>

            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  Nome da Playlist
                </label>
                <input
                  type="text"
                  value={editingPlaylist.name}
                  onChange={(e) =>
                    setEditingPlaylist({
                      ...editingPlaylist,
                      name: e.target.value,
                    })
                  }
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white"
                  placeholder="Nome da playlist"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  Descrição
                </label>
                <textarea
                  value={editingPlaylist.description || ""}
                  onChange={(e) =>
                    setEditingPlaylist({
                      ...editingPlaylist,
                      description: e.target.value,
                    })
                  }
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white"
                  placeholder="Descrição da playlist"
                  rows={3}
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  URL do YouTube
                </label>
                <input
                  type="url"
                  value={editingPlaylist.youtubeUrl || ""}
                  onChange={(e) =>
                    setEditingPlaylist({
                      ...editingPlaylist,
                      youtubeUrl: e.target.value,
                    })
                  }
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white"
                  placeholder="https://www.youtube.com/playlist?list=..."
                />
              </div>

              <div className="flex items-center">
                <input
                  type="checkbox"
                  id="editIsActive"
                  checked={editingPlaylist.isActive ?? true}
                  onChange={(e) =>
                    setEditingPlaylist({
                      ...editingPlaylist,
                      isActive: e.target.checked,
                    })
                  }
                  className="mr-2"
                />
                <label
                  htmlFor="editIsActive"
                  className="text-sm text-gray-700 dark:text-gray-300"
                >
                  Playlist ativa
                </label>
              </div>
            </div>

            <div className="flex justify-end space-x-3 mt-6">
              <button
                onClick={() => setEditingPlaylist(null)}
                className="px-4 py-2 text-gray-600 dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg"
              >
                Cancelar
              </button>
              <button
                onClick={() => updatePlaylist(editingPlaylist)}
                className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
              >
                Salvar Alterações
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default PlaylistManager;
