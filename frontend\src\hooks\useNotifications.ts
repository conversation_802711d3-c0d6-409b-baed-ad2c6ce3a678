import { useEffect, useRef, useState } from "react";
import { toast } from "react-hot-toast";

export interface NotificationData {
  type:
    | "success"
    | "error"
    | "warning"
    | "info"
    | "music"
    | "vote"
    | "badge"
    | "system";
  title: string;
  message: string;
  priority?: "low" | "normal" | "high" | "urgent";
  category?: string;
  data?: any;
  autoClose?: boolean;
  duration?: number;
}

export interface NotificationHookOptions {
  restaurantId?: string;
  sessionId?: string;
  userType?: "client" | "restaurant" | "admin";
  enableWebSocket?: boolean;
  enableToasts?: boolean;
}

export const useNotifications = (options: NotificationHookOptions = {}) => {
  const {
    restaurantId,
    sessionId,
    userType = "client",
    enableWebSocket = true,
    enableToasts = true,
  } = options;

  const [isConnected, setIsConnected] = useState(false);
  const [connectionStatus, setConnectionStatus] = useState<
    "connected" | "disconnected" | "error"
  >("disconnected");
  const [notifications, setNotifications] = useState<NotificationData[]>([]);
  const wsRef = useRef<WebSocket | null>(null);

  // Conectar WebSocket
  useEffect(() => {
    if (!enableWebSocket) return;

    const connectWebSocket = () => {
      try {
        // Usar o serviço Socket.IO existente para notificações
        console.log("🔌 Conectando notificações via Socket.IO");

        // Importar o serviço WebSocket
        import("@/services/websocket").then(({ wsService }) => {
          // Configurar listeners para notificações
          wsService.on("notification", (data: any) => {
            const notification = data.notification || data;
            console.log("📨 Notificação recebida:", notification);

            // Adicionar à lista local
            setNotifications((prev) => [notification, ...prev.slice(0, 49)]); // Manter apenas 50

            // Mostrar toast se habilitado
            if (enableToasts) {
              showToast(notification);
            }

            // Emitir evento customizado
            window.dispatchEvent(
              new CustomEvent("notification", { detail: notification })
            );
          });

          // Configurar listeners de conexão
          wsService.onConnectionStatusChange((status) => {
            setConnectionStatus(status);
            setIsConnected(status === "connected");
          });

          // Verificar status inicial
          setIsConnected(wsService.isConnected());
          setConnectionStatus(wsService.getConnectionStatus());
        });
      } catch (error) {
        console.error("❌ Erro ao conectar notificações:", error);
        setConnectionStatus("error");
      }
    };

    connectWebSocket();

    return () => {
      // Cleanup handled by Socket.IO service
    };
  }, [restaurantId, sessionId, userType, enableWebSocket]);

  // Função para mostrar toast
  const showToast = (notification: NotificationData) => {
    const { type, title, message, priority } = notification;

    // Apenas mostrar toast para notificações importantes
    if (priority === "high" || priority === "urgent") {
      switch (type) {
        case "success":
          toast.success(`${title}: ${message}`);
          break;
        case "error":
          toast.error(`${title}: ${message}`);
          break;
        case "warning":
          toast(`${title}: ${message}`, { icon: "⚠️" });
          break;
        case "music":
          toast(`${title}: ${message}`, { icon: "🎵" });
          break;
        case "vote":
          toast(`${title}: ${message}`, { icon: "🗳️" });
          break;
        case "badge":
          toast(`${title}: ${message}`, { icon: "🏆" });
          break;
        default:
          toast(`${title}: ${message}`);
      }
    }
  };

  // API para enviar notificações programaticamente
  const sendNotification = async (
    notification: Omit<NotificationData, "id" | "timestamp">
  ) => {
    try {
      const response = await fetch(
        "http://localhost:8001/api/v1/notifications/send",
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify(notification),
        }
      );

      if (response.ok) {
        const result = await response.json();
        console.log("✅ Notificação enviada:", result);
        return result;
      } else {
        console.error("❌ Erro ao enviar notificação:", response.statusText);
      }
    } catch (error) {
      console.error("❌ Erro ao enviar notificação:", error);
    }
  };

  // Métodos de conveniência
  const notify = {
    success: (
      title: string,
      message: string,
      options?: Partial<NotificationData>
    ) => sendNotification({ type: "success", title, message, ...options }),

    error: (
      title: string,
      message: string,
      options?: Partial<NotificationData>
    ) => sendNotification({ type: "error", title, message, ...options }),

    warning: (
      title: string,
      message: string,
      options?: Partial<NotificationData>
    ) => sendNotification({ type: "warning", title, message, ...options }),

    info: (
      title: string,
      message: string,
      options?: Partial<NotificationData>
    ) => sendNotification({ type: "info", title, message, ...options }),

    music: (
      title: string,
      message: string,
      options?: Partial<NotificationData>
    ) => sendNotification({ type: "music", title, message, ...options }),

    vote: (
      title: string,
      message: string,
      options?: Partial<NotificationData>
    ) => sendNotification({ type: "vote", title, message, ...options }),

    badge: (
      title: string,
      message: string,
      options?: Partial<NotificationData>
    ) => sendNotification({ type: "badge", title, message, ...options }),

    system: (
      title: string,
      message: string,
      options?: Partial<NotificationData>
    ) => sendNotification({ type: "system", title, message, ...options }),
  };

  // Função para testar notificações
  const testNotification = async () => {
    return sendNotification({
      type: "info",
      title: "Teste de Notificação",
      message: `Notificação de teste enviada em ${new Date().toLocaleTimeString()}`,
      priority: "high",
      category: "test",
    });
  };

  // Limpar notificações
  const clearNotifications = () => {
    setNotifications([]);
  };

  // Marcar como lida
  const markAsRead = (notificationId: string) => {
    setNotifications((prev) =>
      prev.map((n) =>
        n.data?.id === notificationId ? { ...n, read: true } : n
      )
    );
  };

  return {
    // Estado
    isConnected,
    connectionStatus,
    notifications,

    // Métodos
    notify,
    sendNotification,
    testNotification,
    clearNotifications,
    markAsRead,

    // Utilitários
    reconnect: () => {
      if (wsRef.current) {
        wsRef.current.close();
      }
    },
  };
};

// Hook simplificado para uso básico
export const useSimpleNotifications = (restaurantId?: string) => {
  return useNotifications({
    restaurantId,
    sessionId: sessionStorage.getItem("sessionId") || undefined,
    userType: "client",
    enableWebSocket: true,
    enableToasts: true,
  });
};

export default useNotifications;
