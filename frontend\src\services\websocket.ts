import { io, Socket } from "socket.io-client";
import { toast } from "react-hot-toast";
import { WebSocketEvents, ConnectionStatus } from "@/types";

type EventCallback<T = any> = (data: T) => void;

class WebSocketService {
  private socket: Socket | null = null;
  private connectionStatus: ConnectionStatus = "disconnected";
  private reconnectAttempts = 0;
  private maxReconnectAttempts = 5;
  private reconnectDelay = 1000;
  private listeners: Map<string, Set<EventCallback>> = new Map();
  private statusListeners: Set<(status: ConnectionStatus) => void> = new Set();

  constructor() {
    this.connect();
  }

  private connect() {
    const wsUrl = import.meta.env.VITE_WS_URL || "http://localhost:8001";

    this.setConnectionStatus("connecting");

    try {
      this.socket = io(wsUrl, {
        transports: ["websocket", "polling"],
        timeout: 10000,
        forceNew: true,
        reconnection: true,
        reconnectionAttempts: 3, // Reduzir tentativas
        reconnectionDelay: 2000,
        autoConnect: false, // Não conectar automaticamente
      });

      this.setupEventListeners();

      // Conectar apenas se necessário
      if (this.socket && !this.socket.connected) {
        this.socket.connect();
      }
    } catch (error) {
      console.warn("WebSocket não disponível:", error);
      this.setConnectionStatus("disconnected");
    }
  }

  private setupEventListeners() {
    if (!this.socket) return;

    // Eventos de conexão
    this.socket.on("connect", () => {
      console.log("✅ WebSocket conectado");
      this.setConnectionStatus("connected");
      this.reconnectAttempts = 0;

      // Mostrar notificação apenas se estava desconectado
      if (this.connectionStatus !== "connected") {
        toast.success("Conectado ao servidor");
      }
    });

    this.socket.on("disconnect", (reason) => {
      console.log("❌ WebSocket desconectado:", reason);
      this.setConnectionStatus("disconnected");

      if (reason === "io server disconnect") {
        // Servidor forçou desconexão, reconectar manualmente
        this.socket?.connect();
      }
    });

    this.socket.on("connect_error", (error) => {
      console.warn("WebSocket connection failed:", error.message);
      this.setConnectionStatus("error");
      this.reconnectAttempts++;

      // Não mostrar toast de erro para evitar spam
      if (this.reconnectAttempts >= this.maxReconnectAttempts) {
        console.warn("WebSocket: Máximo de tentativas de reconexão atingido");
      }
    });

    this.socket.on("reconnect", (attemptNumber) => {
      console.log(`🔄 WebSocket reconectado após ${attemptNumber} tentativas`);
      this.setConnectionStatus("connected");
      toast.success("Reconectado ao servidor");
    });

    this.socket.on("reconnect_attempt", (attemptNumber) => {
      console.log(`🔄 Tentativa de reconexão ${attemptNumber}`);
      this.setConnectionStatus("connecting");
    });

    this.socket.on("reconnect_error", (error) => {
      console.error("❌ Erro na reconexão:", error);
      this.setConnectionStatus("error");
    });

    this.socket.on("reconnect_failed", () => {
      console.error("❌ Falha na reconexão após máximo de tentativas");
      this.setConnectionStatus("error");
      toast.error("Falha na conexão com o servidor");
    });

    // Eventos customizados da aplicação
    this.setupApplicationEvents();
  }

  private setupApplicationEvents() {
    if (!this.socket) return;

    // Eventos de sugestões
    this.socket.on(
      "new-suggestion",
      (data: WebSocketEvents["new-suggestion"]) => {
        console.log("🎵 Nova sugestão recebida:", data);
        this.emit("new-suggestion", data);

        toast.success(`Nova música sugerida: ${data.title}`, {
          duration: 4000,
        });
      }
    );

    this.socket.on("vote-update", (data: WebSocketEvents["vote-update"]) => {
      console.log("👍 Atualização de votos:", data);
      this.emit("vote-update", data);
    });

    this.socket.on("queue-update", (data: WebSocketEvents["queue-update"]) => {
      console.log("📋 Fila atualizada:", data);
      this.emit("queue-update", data);
    });

    this.socket.on(
      "suggestion-approved",
      (data: WebSocketEvents["suggestion-approved"]) => {
        console.log("✅ Sugestão aprovada:", data);
        this.emit("suggestion-approved", data);

        toast.success(`Música aprovada: ${data.title}`, {
          duration: 4000,
        });
      }
    );

    this.socket.on(
      "suggestion-rejected",
      (data: WebSocketEvents["suggestion-rejected"]) => {
        console.log("❌ Sugestão rejeitada:", data);
        this.emit("suggestion-rejected", data);

        toast.error(`Sugestão rejeitada: ${data.reason}`, {
          duration: 5000,
        });
      }
    );

    this.socket.on("now-playing", (data: WebSocketEvents["now-playing"]) => {
      console.log("🎵 Tocando agora:", data);
      this.emit("now-playing", data);

      toast(`Tocando agora: ${data.suggestion.title}`, {
        icon: "🎵",
        duration: 6000,
      });
    });

    this.socket.on("song-ended", (data: WebSocketEvents["song-ended"]) => {
      console.log("⏭️ Música finalizada:", data);
      this.emit("song-ended", data);
    });
  }

  private setConnectionStatus(status: ConnectionStatus) {
    this.connectionStatus = status;
    this.statusListeners.forEach((listener) => listener(status));
  }

  // Remover listener de status de conexão
  offConnectionStatusChange(callback: (status: ConnectionStatus) => void) {
    this.statusListeners.delete(callback);
  }

  // Métodos públicos

  // Entrar em uma sala específica do restaurante
  joinRestaurant(restaurantId: string) {
    if (this.socket && this.connectionStatus === "connected") {
      this.socket.emit("join-restaurant", restaurantId);
      console.log(`🏪 Entrou na sala do restaurante: ${restaurantId}`);
    }
  }

  // Sair de uma sala do restaurante
  leaveRestaurant(restaurantId: string) {
    if (this.socket && this.connectionStatus === "connected") {
      this.socket.emit("leave-restaurant", restaurantId);
      console.log(`🚪 Saiu da sala do restaurante: ${restaurantId}`);
    }
  }

  // Adicionar listener para eventos
  on<K extends keyof WebSocketEvents>(
    event: K,
    callback: EventCallback<WebSocketEvents[K]>
  ) {
    if (!this.listeners.has(event)) {
      this.listeners.set(event, new Set());
    }
    this.listeners.get(event)!.add(callback);
  }

  // Remover listener para eventos
  off<K extends keyof WebSocketEvents>(
    event: K,
    callback: EventCallback<WebSocketEvents[K]>
  ) {
    const eventListeners = this.listeners.get(event);
    if (eventListeners) {
      eventListeners.delete(callback);
    }
  }

  // Emitir evento para listeners locais
  private emit<K extends keyof WebSocketEvents>(
    event: K,
    data: WebSocketEvents[K]
  ) {
    const eventListeners = this.listeners.get(event);
    if (eventListeners) {
      eventListeners.forEach((callback) => callback(data));
    }
  }

  // Adicionar listener para mudanças de status de conexão
  onConnectionStatusChange(callback: (status: ConnectionStatus) => void) {
    this.statusListeners.add(callback);

    // Retornar função para remover o listener
    return () => {
      this.statusListeners.delete(callback);
    };
  }

  // Obter status atual da conexão
  getConnectionStatus(): ConnectionStatus {
    return this.connectionStatus;
  }

  // Verificar se está conectado
  isConnected(): boolean {
    return this.connectionStatus === "connected";
  }

  // Escutar mudanças de conexão
  onConnectionChange(callback: (connected: boolean) => void): void {
    this.onConnectionStatusChange((status) => {
      callback(status === "connected");
    });
  }

  // Reconectar manualmente
  reconnect() {
    if (this.socket) {
      this.socket.disconnect();
      this.socket.connect();
    } else {
      this.connect();
    }
  }

  // Desconectar
  disconnect() {
    if (this.socket) {
      this.socket.disconnect();
      this.socket = null;
    }
    this.setConnectionStatus("disconnected");
  }

  // Limpar todos os listeners
  removeAllListeners() {
    this.listeners.clear();
    this.statusListeners.clear();
  }

  // Obter informações de debug
  getDebugInfo() {
    return {
      connected: this.isConnected(),
      status: this.connectionStatus,
      reconnectAttempts: this.reconnectAttempts,
      socketId: this.socket?.id,
      transport: this.socket?.io.engine.transport.name,
      listenersCount: Array.from(this.listeners.entries()).reduce(
        (acc, [event, listeners]) => ({ ...acc, [event]: listeners.size }),
        {}
      ),
    };
  }
}

// Instância singleton do WebSocket
export const wsService = new WebSocketService();

// Hook personalizado para React
export const useWebSocket = () => {
  return {
    service: wsService,
    isConnected: wsService.isConnected(),
    status: wsService.getConnectionStatus(),
    joinRestaurant: wsService.joinRestaurant.bind(wsService),
    leaveRestaurant: wsService.leaveRestaurant.bind(wsService),
    on: wsService.on.bind(wsService),
    off: wsService.off.bind(wsService),
    reconnect: wsService.reconnect.bind(wsService),
  };
};

export default wsService;
