import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { 
  Youtube, 
  CheckCircle, 
  AlertCircle, 
  Settings, 
  Play,
  Users,
  TrendingUp,
  ExternalLink,
  RefreshCw
} from 'lucide-react';
import toast from 'react-hot-toast';

interface YouTubeAuthStatus {
  isAuthenticated: boolean;
  capabilities: string[];
  message: string;
}

interface PlaylistReorderResult {
  success: boolean;
  message: string;
  tracksReordered: number;
  newOrder: Array<{
    title: string;
    artist: string;
    voteScore: number;
    upvotes: number;
    downvotes: number;
    positionChange: number;
  }>;
}

interface YouTubeAuthManagerProps {
  restaurantId: string;
  onAuthStatusChange?: (isAuthenticated: boolean) => void;
}

export const YouTubeAuthManager: React.FC<YouTubeAuthManagerProps> = ({
  restaurantId,
  onAuthStatusChange
}) => {
  const [authStatus, setAuthStatus] = useState<YouTubeAuthStatus | null>(null);
  const [loading, setLoading] = useState(false);
  const [reorderLoading, setReorderLoading] = useState(false);
  const [showCreatePlaylist, setShowCreatePlaylist] = useState(false);
  const [newPlaylistTitle, setNewPlaylistTitle] = useState('');
  const [newPlaylistDescription, setNewPlaylistDescription] = useState('');

  // Verificar status da autenticação
  const checkAuthStatus = async () => {
    setLoading(true);
    try {
      const response = await fetch(`/api/v1/youtube-auth/${restaurantId}/status`);
      const data = await response.json();
      
      if (data.success) {
        setAuthStatus(data);
        onAuthStatusChange?.(data.isAuthenticated);
      }
    } catch (error) {
      console.error('Erro ao verificar status:', error);
      toast.error('Erro ao verificar status da autenticação');
    } finally {
      setLoading(false);
    }
  };

  // Iniciar processo de autorização
  const startAuthorization = async () => {
    setLoading(true);
    try {
      const response = await fetch(`/api/v1/youtube-auth/${restaurantId}/authorize`);
      const data = await response.json();
      
      if (data.success && data.authUrl) {
        // Abrir URL de autorização em nova janela
        window.open(data.authUrl, 'youtube-auth', 'width=600,height=700');
        
        // Mostrar instruções
        toast.success('Janela de autorização aberta! Siga as instruções.');
        
        // Verificar status periodicamente
        const checkInterval = setInterval(async () => {
          await checkAuthStatus();
          if (authStatus?.isAuthenticated) {
            clearInterval(checkInterval);
            toast.success('Autenticação concluída com sucesso!');
          }
        }, 3000);
        
        // Limpar intervalo após 5 minutos
        setTimeout(() => clearInterval(checkInterval), 300000);
      }
    } catch (error) {
      console.error('Erro ao iniciar autorização:', error);
      toast.error('Erro ao iniciar processo de autorização');
    } finally {
      setLoading(false);
    }
  };

  // Criar nova playlist
  const createPlaylist = async () => {
    if (!newPlaylistTitle.trim()) {
      toast.error('Título da playlist é obrigatório');
      return;
    }

    setLoading(true);
    try {
      const response = await fetch(`/api/v1/youtube-auth/${restaurantId}/create-playlist`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          title: newPlaylistTitle,
          description: newPlaylistDescription
        })
      });

      const data = await response.json();
      
      if (data.success) {
        toast.success('Playlist criada com sucesso!');
        setShowCreatePlaylist(false);
        setNewPlaylistTitle('');
        setNewPlaylistDescription('');
        
        // Mostrar URL da playlist
        if (data.playlistUrl) {
          toast.success(
            <div>
              <p>Playlist criada!</p>
              <a 
                href={data.playlistUrl} 
                target="_blank" 
                rel="noopener noreferrer"
                className="text-blue-500 underline"
              >
                Ver no YouTube
              </a>
            </div>
          );
        }
      } else {
        toast.error(data.message || 'Erro ao criar playlist');
      }
    } catch (error) {
      console.error('Erro ao criar playlist:', error);
      toast.error('Erro ao criar playlist');
    } finally {
      setLoading(false);
    }
  };

  // Reordenar playlist baseada em votos
  const reorderPlaylistByVotes = async (playlistId: string) => {
    setReorderLoading(true);
    try {
      const response = await fetch(`/api/v1/youtube-auth/${restaurantId}/playlists/${playlistId}/reorder`, {
        method: 'POST'
      });

      const data: PlaylistReorderResult = await response.json();
      
      if (data.success) {
        if (data.tracksReordered > 0) {
          toast.success(`${data.tracksReordered} músicas reordenadas baseado nos votos!`);
          
          // Mostrar top 3 músicas mais votadas
          if (data.newOrder.length > 0) {
            const topTracks = data.newOrder.slice(0, 3);
            console.log('🏆 Top músicas:', topTracks);
          }
        } else {
          toast.success('Playlist já está na ordem ideal!');
        }
      } else {
        toast.error(data.message || 'Erro ao reordenar playlist');
      }
    } catch (error) {
      console.error('Erro ao reordenar playlist:', error);
      toast.error('Erro ao reordenar playlist');
    } finally {
      setReorderLoading(false);
    }
  };

  useEffect(() => {
    checkAuthStatus();
  }, [restaurantId]);

  if (loading && !authStatus) {
    return (
      <div className="flex items-center justify-center p-8">
        <RefreshCw className="w-6 h-6 animate-spin text-purple-500" />
        <span className="ml-2">Verificando autenticação...</span>
      </div>
    );
  }

  return (
    <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center">
          <Youtube className="w-8 h-8 text-red-500 mr-3" />
          <div>
            <h2 className="text-xl font-bold text-gray-900 dark:text-white">
              Controle de Playlist YouTube
            </h2>
            <p className="text-sm text-gray-600 dark:text-gray-400">
              Gerencie playlists com controle total baseado em votações
            </p>
          </div>
        </div>
        
        <button
          onClick={checkAuthStatus}
          disabled={loading}
          className="p-2 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
        >
          <RefreshCw className={`w-5 h-5 ${loading ? 'animate-spin' : ''}`} />
        </button>
      </div>

      {authStatus && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="space-y-6"
        >
          {/* Status da Autenticação */}
          <div className={`p-4 rounded-lg border-2 ${
            authStatus.isAuthenticated 
              ? 'border-green-200 bg-green-50 dark:border-green-800 dark:bg-green-900/20'
              : 'border-yellow-200 bg-yellow-50 dark:border-yellow-800 dark:bg-yellow-900/20'
          }`}>
            <div className="flex items-center">
              {authStatus.isAuthenticated ? (
                <CheckCircle className="w-6 h-6 text-green-500 mr-3" />
              ) : (
                <AlertCircle className="w-6 h-6 text-yellow-500 mr-3" />
              )}
              <div>
                <h3 className={`font-semibold ${
                  authStatus.isAuthenticated ? 'text-green-800 dark:text-green-200' : 'text-yellow-800 dark:text-yellow-200'
                }`}>
                  {authStatus.isAuthenticated ? 'Autenticado com YouTube' : 'Não Autenticado'}
                </h3>
                <p className={`text-sm ${
                  authStatus.isAuthenticated ? 'text-green-600 dark:text-green-300' : 'text-yellow-600 dark:text-yellow-300'
                }`}>
                  {authStatus.message}
                </p>
              </div>
            </div>
          </div>

          {/* Capacidades */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {authStatus.capabilities.map((capability, index) => (
              <div key={index} className="flex items-center p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                <div className="w-2 h-2 bg-purple-500 rounded-full mr-3"></div>
                <span className="text-sm text-gray-700 dark:text-gray-300">{capability}</span>
              </div>
            ))}
          </div>

          {/* Ações */}
          <div className="flex flex-wrap gap-4">
            {!authStatus.isAuthenticated ? (
              <button
                onClick={startAuthorization}
                disabled={loading}
                className="flex items-center px-6 py-3 bg-red-500 text-white rounded-lg hover:bg-red-600 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
              >
                <Youtube className="w-5 h-5 mr-2" />
                {loading ? 'Processando...' : 'Conectar com YouTube'}
              </button>
            ) : (
              <>
                <button
                  onClick={() => setShowCreatePlaylist(true)}
                  className="flex items-center px-4 py-2 bg-purple-500 text-white rounded-lg hover:bg-purple-600 transition-colors"
                >
                  <Play className="w-4 h-4 mr-2" />
                  Criar Playlist
                </button>
                
                <button
                  onClick={() => reorderPlaylistByVotes('current-playlist-id')}
                  disabled={reorderLoading}
                  className="flex items-center px-4 py-2 bg-green-500 text-white rounded-lg hover:bg-green-600 disabled:opacity-50 transition-colors"
                >
                  <TrendingUp className={`w-4 h-4 mr-2 ${reorderLoading ? 'animate-spin' : ''}`} />
                  {reorderLoading ? 'Reordenando...' : 'Reordenar por Votos'}
                </button>
              </>
            )}
          </div>
        </motion.div>
      )}

      {/* Modal Criar Playlist */}
      {showCreatePlaylist && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <motion.div
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            className="bg-white dark:bg-gray-800 rounded-lg p-6 w-full max-w-md mx-4"
          >
            <h3 className="text-lg font-bold text-gray-900 dark:text-white mb-4">
              Criar Nova Playlist
            </h3>
            
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Título da Playlist *
                </label>
                <input
                  type="text"
                  value={newPlaylistTitle}
                  onChange={(e) => setNewPlaylistTitle(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-purple-500 dark:bg-gray-700 dark:text-white"
                  placeholder="Ex: Playlist Interativa - Restaurante"
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Descrição
                </label>
                <textarea
                  value={newPlaylistDescription}
                  onChange={(e) => setNewPlaylistDescription(e.target.value)}
                  rows={3}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-purple-500 dark:bg-gray-700 dark:text-white"
                  placeholder="Playlist controlada pelos clientes através de votações..."
                />
              </div>
            </div>
            
            <div className="flex justify-end space-x-3 mt-6">
              <button
                onClick={() => setShowCreatePlaylist(false)}
                className="px-4 py-2 text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200"
              >
                Cancelar
              </button>
              <button
                onClick={createPlaylist}
                disabled={loading || !newPlaylistTitle.trim()}
                className="px-4 py-2 bg-purple-500 text-white rounded-lg hover:bg-purple-600 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {loading ? 'Criando...' : 'Criar Playlist'}
              </button>
            </div>
          </motion.div>
        </div>
      )}
    </div>
  );
};
