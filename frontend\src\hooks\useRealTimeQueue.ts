import { useState, useEffect, useCallback } from 'react';
import { toast } from 'react-hot-toast';
import { wsService } from '@/services/websocket';
import { PlayQueue, Suggestion } from '@/types';
import { useAppStore } from '@/store';

interface QueueUpdate {
  queue: Suggestion[];
  currentlyPlaying?: Suggestion;
  estimatedWaitTime?: number;
}

interface NowPlayingUpdate {
  suggestion: Suggestion;
  position: number;
  estimatedEndTime: string;
}

export const useRealTimeQueue = (restaurantId: string) => {
  const [queue, setQueue] = useState<PlayQueue | null>(null);
  const [isConnected, setIsConnected] = useState(false);
  const [currentlyPlaying, setCurrentlyPlaying] = useState<Suggestion | null>(null);
  const { setPlayQueue, setCurrentlyPlaying: setStoreCurrentlyPlaying } = useAppStore();

  // Atualizar fila de reprodução
  const updateQueue = useCallback((newQueue: PlayQueue) => {
    setQueue(newQueue);
    setStoreCurrentlyPlaying(newQueue.currentlyPlaying || null);
    setPlayQueue(newQueue);
  }, [setPlayQueue, setStoreCurrentlyPlaying]);

  // Atualizar música atual
  const updateCurrentlyPlaying = useCallback((suggestion: Suggestion | null) => {
    setCurrentlyPlaying(suggestion);
    setStoreCurrentlyPlaying(suggestion);
  }, [setStoreCurrentlyPlaying]);

  // Configurar listeners do WebSocket
  useEffect(() => {
    const handleQueueUpdate = (data: QueueUpdate) => {
      console.log('📋 Fila atualizada em tempo real:', data);
      
      const updatedQueue: PlayQueue = {
        queue: data.queue,
        currentlyPlaying: data.currentlyPlaying || null,
        totalDuration: data.queue.reduce((total, song) => total + (song.duration || 180), 0),
        estimatedWaitTime: data.estimatedWaitTime || 0,
        lastUpdated: new Date().toISOString()
      };

      updateQueue(updatedQueue);
      
      // Mostrar notificação se a fila mudou significativamente
      if (data.queue.length > (queue?.queue.length || 0)) {
        toast.success('Nova música adicionada à fila!', {
          duration: 3000,
          icon: '🎵'
        });
      }
    };

    const handleNowPlaying = (data: NowPlayingUpdate) => {
      console.log('🎵 Tocando agora:', data);
      
      updateCurrentlyPlaying(data.suggestion);
      
      toast(`Tocando agora: ${data.suggestion.title}`, {
        icon: '🎵',
        duration: 5000,
      });
    };

    const handleSongEnded = (data: { suggestionId: string; nextSong?: Suggestion }) => {
      console.log('⏭️ Música finalizada:', data);
      
      if (data.nextSong) {
        updateCurrentlyPlaying(data.nextSong);
        toast(`Próxima música: ${data.nextSong.title}`, {
          icon: '⏭️',
          duration: 4000,
        });
      } else {
        updateCurrentlyPlaying(null);
      }
    };

    const handleNewSuggestion = (suggestion: Suggestion) => {
      console.log('🎵 Nova sugestão recebida:', suggestion);
      
      // Atualizar fila se a sugestão foi aprovada automaticamente
      if (suggestion.status === 'approved' && queue) {
        const updatedQueue = {
          ...queue,
          queue: [...queue.queue, suggestion],
          totalDuration: queue.totalDuration + (suggestion.duration || 180)
        };
        updateQueue(updatedQueue);
      }
      
      toast.success(`Nova música sugerida: ${suggestion.title}`, {
        duration: 4000,
        icon: '🎵'
      });
    };

    const handleSuggestionApproved = (suggestion: Suggestion) => {
      console.log('✅ Sugestão aprovada:', suggestion);
      
      // Adicionar à fila se não estiver lá
      if (queue && !queue.queue.find(s => s.id === suggestion.id)) {
        const updatedQueue = {
          ...queue,
          queue: [...queue.queue, suggestion],
          totalDuration: queue.totalDuration + (suggestion.duration || 180)
        };
        updateQueue(updatedQueue);
      }
      
      toast.success(`Música aprovada: ${suggestion.title}`, {
        duration: 4000,
        icon: '✅'
      });
    };

    const handleConnectionChange = (connected: boolean) => {
      setIsConnected(connected);
      
      if (connected) {
        // Reconectar à sala do restaurante
        wsService.joinRestaurant(restaurantId);
      }
    };

    // Conectar ao WebSocket se não estiver conectado
    if (!wsService.isConnected()) {
      wsService.reconnect();
    }
    
    // Entrar na sala do restaurante
    wsService.joinRestaurant(restaurantId);

    // Registrar listeners
    wsService.on('queue-update', handleQueueUpdate);
    wsService.on('now-playing', handleNowPlaying);
    wsService.on('song-ended', handleSongEnded);
    wsService.on('new-suggestion', handleNewSuggestion);
    wsService.on('suggestion-approved', handleSuggestionApproved);
    
    // Listeners de conexão
    wsService.onConnectionChange(handleConnectionChange);
    setIsConnected(wsService.isConnected());

    return () => {
      // Remover listeners
      wsService.off('queue-update', handleQueueUpdate);
      wsService.off('now-playing', handleNowPlaying);
      wsService.off('song-ended', handleSongEnded);
      wsService.off('new-suggestion', handleNewSuggestion);
      wsService.off('suggestion-approved', handleSuggestionApproved);
      
      // Sair da sala do restaurante
      wsService.leaveRestaurant(restaurantId);
    };
  }, [restaurantId, queue, updateQueue, updateCurrentlyPlaying]);

  // Função para solicitar atualização da fila
  const refreshQueue = useCallback(() => {
    if (wsService.isConnected()) {
      wsService.emit('request-queue-update', { restaurantId });
    }
  }, [restaurantId]);

  // Função para reportar posição atual da música
  const reportPlaybackPosition = useCallback((suggestionId: string, position: number) => {
    if (wsService.isConnected()) {
      wsService.emit('playback-position', {
        suggestionId,
        position,
        restaurantId
      });
    }
  }, [restaurantId]);

  return {
    queue,
    currentlyPlaying,
    isConnected,
    refreshQueue,
    reportPlaybackPosition,
    updateQueue,
    updateCurrentlyPlaying
  };
};

export default useRealTimeQueue;
