import {
  <PERSON><PERSON><PERSON>,
  PrimaryC<PERSON>umn,
  <PERSON>umn,
  CreateDate<PERSON><PERSON>umn,
  UpdateDateColumn,
  OneToMany,
  Index,
} from "typeorm";
import { User } from "./User";
import { Playlist } from "./Playlist";
import { Suggestion } from "./Suggestion";
import { ModerationRule } from "./ModerationRule";
import { PlayHistory } from "./PlayHistory";
import { AnalyticsDaily } from "./AnalyticsDaily";

export enum RestaurantStatus {
  ACTIVE = "active",
  INACTIVE = "inactive",
  SUSPENDED = "suspended",
  TRIAL = "trial",
}

@Entity("restaurants")
@Index(["status"])
@Index(["createdAt"])
export class Restaurant {
  @PrimaryColumn({ type: "varchar", length: 255 })
  id: string;

  @Column({ type: "varchar", length: 255 })
  name: string;

  @Column({ type: "text", nullable: true })
  description: string;

  @Column({ type: "varchar", length: 255, nullable: true })
  logo: string;

  @Column({ type: "varchar", length: 20, nullable: true })
  phone: string;

  @Column({ type: "varchar", length: 255, nullable: true })
  email: string;

  @Column({ type: "varchar", length: 255, nullable: true })
  website: string;

  @Column({ type: "json", nullable: true })
  address: {
    street?: string;
    number?: string;
    complement?: string;
    neighborhood?: string;
    city?: string;
    state?: string;
    zipCode?: string;
    country?: string;
    coordinates?: {
      lat: number;
      lng: number;
    };
  };

  @Column({
    type: "enum",
    enum: RestaurantStatus,
    default: RestaurantStatus.TRIAL,
  })
  status: RestaurantStatus;

  @Column({ type: "varchar", length: 255, nullable: true })
  youtubeChannelId: string;

  @Column({ type: "text", nullable: true })
  youtubePremiumToken: string;

  @Column({ type: "json", nullable: true })
  youtubeCredentials: {
    access_token?: string;
    refresh_token?: string;
    expiry_date?: number;
    scope?: string;
    token_type?: string;
  };

  @Column({ type: "json", nullable: true })
  settings: {
    // Configurações de moderação
    moderation?: {
      autoApprove?: boolean;
      requireApproval?: boolean;
      maxSuggestionsPerUser?: number;
      maxSuggestionsPerHour?: number;
      allowExplicitContent?: boolean;
      allowLiveVideos?: boolean;
      minVideoDuration?: number; // em segundos
      maxVideoDuration?: number; // em segundos
    };

    // Configurações de playlist
    playlist?: {
      maxQueueSize?: number;
      allowDuplicates?: boolean;
      shuffleMode?: boolean;
      repeatMode?: "none" | "one" | "all";
      crossfadeDuration?: number; // em segundos
      defaultVolume?: number; // 0-100
    };

    // Configurações de interface
    interface?: {
      theme?: "light" | "dark" | "auto";
      primaryColor?: string;
      secondaryColor?: string;
      showVoteCount?: boolean;
      showQueuePosition?: boolean;
      allowAnonymousSuggestions?: boolean;
      requireSessionId?: boolean;
    };

    // Configurações de horário
    schedule?: {
      timezone?: string;
      operatingHours?: {
        [key: string]: {
          // 'monday', 'tuesday', etc.
          open?: string; // HH:mm
          close?: string; // HH:mm
          closed?: boolean;
        };
      };
      playlistSchedule?: {
        [key: string]: {
          // 'morning', 'afternoon', 'evening', 'night'
          startTime?: string; // HH:mm
          endTime?: string; // HH:mm
          playlistId?: string;
        };
      };
    };

    // Configurações de notificações
    notifications?: {
      email?: {
        enabled?: boolean;
        newSuggestions?: boolean;
        moderationRequired?: boolean;
        dailyReport?: boolean;
      };
      webhook?: {
        enabled?: boolean;
        url?: string;
        events?: string[];
      };
    };

    // Configurações ECAD
    ecad?: {
      enabled?: boolean;
      licenseNumber?: string;
      contactInfo?: string;
      reportingEnabled?: boolean;
    };
  };

  @Column({ type: "json", nullable: true })
  socialMedia: {
    facebook?: string;
    instagram?: string;
    twitter?: string;
    tiktok?: string;
    youtube?: string;
  };

  @Column({ type: "varchar", length: 10, default: "pt-BR" })
  language: string;

  @Column({ type: "varchar", length: 50, default: "America/Sao_Paulo" })
  timezone: string;

  @Column({ type: "timestamp", nullable: true })
  trialExpiresAt: Date;

  @Column({ type: "timestamp", nullable: true })
  lastActivityAt: Date;

  @Column({ type: "boolean", default: true })
  isActive: boolean;

  // Relacionamentos
  @OneToMany(() => User, (user) => user.restaurant)
  users: User[];

  @OneToMany(() => Playlist, (playlist) => playlist.restaurant)
  playlists: Playlist[];

  @OneToMany(() => Suggestion, (suggestion) => suggestion.restaurant)
  suggestions: Suggestion[];

  @OneToMany(() => ModerationRule, (rule) => rule.restaurant)
  moderationRules: ModerationRule[];

  @OneToMany(() => PlayHistory, (history) => history.restaurant)
  playHistory: PlayHistory[];

  @OneToMany(() => AnalyticsDaily, (analytics) => analytics.restaurant)
  analytics: AnalyticsDaily[];

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;

  // Métodos da instância

  // Verificar se está em horário de funcionamento
  isOpen(): boolean {
    if (!this.settings?.schedule?.operatingHours) {
      return true; // Se não há horário definido, considera sempre aberto
    }

    const now = new Date();
    const dayOfWeek = now
      .toLocaleDateString("en-US", { weekday: "long" })
      .toLowerCase();
    const currentTime = now.toTimeString().slice(0, 5); // HH:mm

    const todaySchedule = this.settings.schedule.operatingHours[dayOfWeek];

    if (!todaySchedule || todaySchedule.closed) {
      return false;
    }

    if (!todaySchedule.open || !todaySchedule.close) {
      return true;
    }

    return (
      currentTime >= todaySchedule.open && currentTime <= todaySchedule.close
    );
  }

  // Obter configuração específica com valor padrão
  getSetting<T>(path: string, defaultValue: T): T {
    const keys = path.split(".");
    let current: any = this.settings;

    for (const key of keys) {
      if (current && typeof current === "object" && key in current) {
        current = current[key];
      } else {
        return defaultValue;
      }
    }

    return current !== undefined ? current : defaultValue;
  }

  // Atualizar configuração específica
  setSetting(path: string, value: any): void {
    if (!this.settings) {
      this.settings = {};
    }

    const keys = path.split(".");
    let current: any = this.settings;

    for (let i = 0; i < keys.length - 1; i++) {
      const key = keys[i];
      if (!current[key] || typeof current[key] !== "object") {
        current[key] = {};
      }
      current = current[key];
    }

    current[keys[keys.length - 1]] = value;
  }

  // Verificar se está em período de trial
  isInTrial(): boolean {
    return (
      this.status === RestaurantStatus.TRIAL &&
      this.trialExpiresAt &&
      this.trialExpiresAt > new Date()
    );
  }

  // Verificar se trial expirou
  isTrialExpired(): boolean {
    return (
      this.status === RestaurantStatus.TRIAL &&
      this.trialExpiresAt &&
      this.trialExpiresAt <= new Date()
    );
  }

  // Obter URL do QR code para clientes
  getQRCodeUrl(): string {
    const baseUrl = process.env.FRONTEND_URL || "http://localhost:3000";
    return `${baseUrl}/restaurant/${this.id}`;
  }

  // Serialização para JSON (remover dados sensíveis)
  toJSON() {
    const { youtubePremiumToken, ...restaurant } = this;
    return restaurant;
  }

  // Serialização pública (para clientes)
  toPublicJSON() {
    return {
      id: this.id,
      name: this.name,
      description: this.description,
      logo: this.logo,
      address: this.address,
      socialMedia: this.socialMedia,
      isOpen: this.isOpen(),
      settings: {
        interface: this.settings?.interface,
        playlist: {
          showVoteCount: this.settings?.interface?.showVoteCount,
          showQueuePosition: this.settings?.interface?.showQueuePosition,
        },
      },
    };
  }
}
