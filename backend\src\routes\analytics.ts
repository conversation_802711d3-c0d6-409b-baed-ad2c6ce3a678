import { Router, Request, Response } from "express";
import { param, query, validationResult } from "express-validator";
import { analyticsService } from "../services/AnalyticsService";
import {
  asyncHandler,
  createValidationError,
} from "../middleware/errorHandler";
import { optionalAuth } from "../middleware/auth";
import { ValidationError, NotFoundError } from "../utils/errors";

// Interfaces para tipos de dados
interface PaymentStats {
  totalRevenue: number;
  totalPayments: number;
  averageAmount: number;
  dailyRevenue: Array<{
    date: string;
    revenue: number;
    count: number;
  }>;
  approvedPayments: number;
}

interface PaymentData {
  stats: PaymentStats;
}

interface VotingLeaderboardEntry {
  totalVotes: number;
  [key: string]: any;
}

interface VotingData {
  leaderboard: VotingLeaderboardEntry[];
  total: number;
}

interface QueueData {
  [key: string]: any;
}

const router = Router();

/**
 * @swagger
 * /api/v1/analytics/tracks/{restaurantId}:
 *   get:
 *     summary: Obter análise de<PERSON><PERSON><PERSON> das tracks da playlist
 *     tags: [Analytics]
 *     parameters:
 *       - in: path
 *         name: restaurantId
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Análise das tracks
 */
router.get(
  "/tracks/:restaurantId",
  [
    param("restaurantId")
      .notEmpty()
      .withMessage("ID do restaurante é obrigatório"),
  ],
  optionalAuth,
  asyncHandler(async (req, res) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      throw new ValidationError("Parâmetros inválidos", errors.array());
    }

    const { restaurantId } = req.params;

    try {
      const trackAnalytics = await analyticsService.getTracksAnalytics(
        restaurantId
      );

      res.json({
        success: true,
        trackAnalytics,
        generatedAt: new Date().toISOString(),
      });
    } catch (error) {
      console.error("Erro ao buscar analytics de tracks:", error);
      res.json({
        success: true,
        trackAnalytics: [],
        generatedAt: new Date().toISOString(),
      });
    }
  })
);

/**
 * @swagger
 * /api/v1/analytics/playlist-health/{restaurantId}:
 *   get:
 *     summary: Obter saúde geral da playlist
 *     tags: [Analytics]
 *     parameters:
 *       - in: path
 *         name: restaurantId
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Saúde da playlist
 */
router.get(
  "/playlist-health/:restaurantId",
  [
    param("restaurantId")
      .notEmpty()
      .withMessage("ID do restaurante é obrigatório"),
  ],
  optionalAuth,
  asyncHandler(async (req, res) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      throw new ValidationError("Parâmetros inválidos", errors.array());
    }

    const { restaurantId } = req.params;

    try {
      const playlistHealth = await analyticsService.getPlaylistHealth(
        restaurantId
      );

      res.json({
        success: true,
        playlistHealth,
        generatedAt: new Date().toISOString(),
      });
    } catch (error) {
      console.error("Erro ao buscar saúde da playlist:", error);
      res.json({
        success: true,
        playlistHealth: null,
        generatedAt: new Date().toISOString(),
      });
    }
  })
);

/**
 * @swagger
 * /api/v1/analytics/dashboard/{restaurantId}:
 *   get:
 *     summary: Obter resumo do dashboard de analytics
 *     tags: [Analytics]
 *     parameters:
 *       - in: path
 *         name: restaurantId
 *         required: true
 *         schema:
 *           type: string
 *       - in: query
 *         name: period
 *         schema:
 *           type: string
 *           enum: [1d, 7d, 30d, 90d]
 *           default: 7d
 *     responses:
 *       200:
 *         description: Resumo do dashboard
 */
router.get(
  "/dashboard/:restaurantId",
  [
    param("restaurantId")
      .notEmpty()
      .withMessage("ID do restaurante é obrigatório"),
    query("period").optional().isIn(["1d", "7d", "30d", "90d"]),
  ],
  optionalAuth,
  asyncHandler(async (req, res) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      throw new ValidationError("Parâmetros inválidos", errors.array());
    }

    const { restaurantId } = req.params;
    const { period = "7d" } = req.query;

    const summary = await analyticsService.generateDashboardSummary(
      restaurantId,
      period as string
    );

    res.json({
      success: true,
      summary,
      period,
      generatedAt: new Date().toISOString(),
    });
  })
);

/**
 * @swagger
 * /api/v1/analytics/metrics/{restaurantId}:
 *   get:
 *     summary: Obter métricas detalhadas
 *     tags: [Analytics]
 *     parameters:
 *       - in: path
 *         name: restaurantId
 *         required: true
 *         schema:
 *           type: string
 *       - in: query
 *         name: period
 *         schema:
 *           type: string
 *           enum: [1d, 7d, 30d, 90d]
 *           default: 7d
 *     responses:
 *       200:
 *         description: Métricas detalhadas
 */
router.get(
  "/metrics/:restaurantId",
  [
    param("restaurantId")
      .notEmpty()
      .withMessage("ID do restaurante é obrigatório"),
    query("period").optional().isIn(["1d", "7d", "30d", "90d"]),
  ],
  optionalAuth,
  asyncHandler(async (req, res) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      throw new ValidationError("Parâmetros inválidos", errors.array());
    }

    const { restaurantId } = req.params;
    const { period = "7d" } = req.query;

    const metrics = await analyticsService.generateRealTimeMetrics(
      restaurantId,
      period as string
    );

    res.json({
      success: true,
      metrics,
      period,
      generatedAt: new Date().toISOString(),
    });
  })
);

/**
 * @swagger
 * /api/v1/analytics/stats/{restaurantId}:
 *   get:
 *     summary: Obter estatísticas rápidas (compatibilidade)
 *     tags: [Analytics]
 *     parameters:
 *       - in: path
 *         name: restaurantId
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Estatísticas rápidas
 */
router.get(
  "/stats/:restaurantId",
  [
    param("restaurantId")
      .notEmpty()
      .withMessage("ID do restaurante é obrigatório"),
  ],
  optionalAuth,
  asyncHandler(async (req, res) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      throw new ValidationError("Parâmetros inválidos", errors.array());
    }

    const { restaurantId } = req.params;

    // Compatibilidade com endpoint antigo
    const [summary, metrics, popularSongs] = await Promise.all([
      analyticsService.generateDashboardSummary(restaurantId, "7d"),
      analyticsService.generateRealTimeMetrics(restaurantId, "7d"),
      analyticsService.getPopularSongs(restaurantId, 5),
    ]);

    res.json({
      success: true,
      stats: {
        summary,
        totalSuggestions: metrics.totalSuggestions,
        totalVotes: metrics.totalVotes,
        activeUsers: metrics.uniqueSessions,
        topSongs: popularSongs,
        hourlyActivity: metrics.hourlyActivity,
        topGenres: metrics.topGenres,
      },
      generatedAt: new Date().toISOString(),
    });
  })
);

/**
 * @swagger
 * /api/v1/analytics/competitive/{restaurantId}:
 *   get:
 *     summary: Obter analytics competitivos
 *     tags: [Analytics]
 *     parameters:
 *       - in: path
 *         name: restaurantId
 *         required: true
 *         schema:
 *           type: string
 *       - in: query
 *         name: period
 *         schema:
 *           type: string
 *           enum: [1d, 7d, 30d]
 *           default: 1d
 *     responses:
 *       200:
 *         description: Analytics competitivos
 */
router.get(
  "/competitive/:restaurantId",
  [
    param("restaurantId")
      .notEmpty()
      .withMessage("ID do restaurante é obrigatório"),
    query("period").optional().isIn(["1d", "7d", "30d"]),
  ],
  asyncHandler(async (req, res) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      throw createValidationError(errors.array());
    }

    const { restaurantId } = req.params;
    const { period = "1d" } = req.query;

    // Calcular período
    const endDate = new Date();
    const startDate = new Date();

    switch (period) {
      case "1d":
        startDate.setDate(endDate.getDate() - 1);
        break;
      case "7d":
        startDate.setDate(endDate.getDate() - 7);
        break;
      case "30d":
        startDate.setDate(endDate.getDate() - 30);
        break;
      default:
        startDate.setDate(endDate.getDate() - 1);
    }

    try {
      // Buscar dados de pagamentos
      const paymentResponse = await fetch(
        `http://localhost:5000/api/v1/payments/stats/${restaurantId}?period=${period}`
      );
      const paymentData: PaymentData | null = paymentResponse.ok
        ? await paymentResponse.json()
        : null;

      // Buscar dados de votação
      const votingResponse = await fetch(
        `http://localhost:5000/api/v1/competitive-voting/leaderboard/${restaurantId}`
      );
      const votingData: VotingData | null = votingResponse.ok
        ? await votingResponse.json()
        : null;

      // Buscar fila de reprodução
      const queueResponse = await fetch(
        `http://localhost:5000/api/v1/playback-queue/${restaurantId}/stats`
      );
      const queueData: QueueData | null = queueResponse.ok
        ? await queueResponse.json()
        : null;

      // Compilar analytics competitivos
      const competitiveAnalytics = {
        period,
        revenue: {
          total: paymentData?.stats?.totalRevenue || 0,
          totalPayments: paymentData?.stats?.totalPayments || 0,
          averageAmount: paymentData?.stats?.averageAmount || 0,
          dailyRevenue: paymentData?.stats?.dailyRevenue || [],
        },
        voting: {
          totalVotes:
            votingData?.leaderboard?.reduce(
              (sum: number, entry: any) => sum + entry.totalVotes,
              0
            ) || 0,
          totalPerformances: votingData?.total || 0,
          averageRating:
            votingData?.leaderboard?.length > 0
              ? votingData.leaderboard.reduce(
                  (sum: number, entry: any) => sum + entry.averageRating,
                  0
                ) / votingData.leaderboard.length
              : 0,
          topPerformers: votingData?.leaderboard?.slice(0, 5) || [],
        },
        queue: {
          totalItems: queueData?.stats?.totalItems || 0,
          paidItems: queueData?.stats?.paidItems || 0,
          freeItems: queueData?.stats?.freeItems || 0,
          paidPercentage:
            queueData?.stats?.totalItems > 0
              ? Math.round(
                  (queueData.stats.paidItems / queueData.stats.totalItems) * 100
                )
              : 0,
        },
        engagement: {
          paymentConversionRate:
            paymentData?.stats?.totalPayments &&
            paymentData?.stats?.totalPayments > 0
              ? Math.round(
                  (paymentData.stats.approvedPayments /
                    paymentData.stats.totalPayments) *
                    100
                )
              : 0,
          votingParticipationRate:
            votingData?.leaderboard?.length > 0 &&
            queueData?.stats?.paidItems > 0
              ? Math.round(
                  (votingData.leaderboard.length / queueData.stats.paidItems) *
                    100
                )
              : 0,
          averageVotesPerSong:
            votingData?.leaderboard?.length > 0
              ? Math.round(
                  votingData.leaderboard.reduce(
                    (sum: number, entry: any) => sum + entry.totalVotes,
                    0
                  ) / votingData.leaderboard.length
                )
              : 0,
        },
        trends: {
          hourlyRevenue:
            paymentData?.stats?.dailyRevenue?.map((day: any) => ({
              hour: new Date(day.date).getHours(),
              revenue: day.revenue,
              count: day.count,
            })) || [],
          topTables:
            votingData?.leaderboard
              ?.reduce((tables: any[], entry: any) => {
                const existing = tables.find(
                  (t) => t.tableName === entry.tableName
                );
                if (existing) {
                  existing.totalVotes += entry.totalVotes;
                  existing.performances += 1;
                  existing.averageRating =
                    (existing.averageRating + entry.averageRating) / 2;
                } else {
                  tables.push({
                    tableName: entry.tableName,
                    totalVotes: entry.totalVotes,
                    performances: 1,
                    averageRating: entry.averageRating,
                  });
                }
                return tables;
              }, [])
              ?.sort((a: any, b: any) => b.averageRating - a.averageRating)
              ?.slice(0, 5) || [],
        },
      };

      res.json({
        success: true,
        analytics: competitiveAnalytics,
        generatedAt: new Date().toISOString(),
      });
    } catch (error) {
      console.error("Erro ao obter analytics competitivos:", error);

      // Retornar dados mock em caso de erro
      res.json({
        success: true,
        analytics: {
          period,
          revenue: {
            total: 0,
            totalPayments: 0,
            averageAmount: 0,
            dailyRevenue: [],
          },
          voting: {
            totalVotes: 0,
            totalPerformances: 0,
            averageRating: 0,
            topPerformers: [],
          },
          queue: {
            totalItems: 0,
            paidItems: 0,
            freeItems: 0,
            paidPercentage: 0,
          },
          engagement: {
            paymentConversionRate: 0,
            votingParticipationRate: 0,
            averageVotesPerSong: 0,
          },
          trends: { hourlyRevenue: [], topTables: [] },
        },
        generatedAt: new Date().toISOString(),
      });
    }
  })
);

// Rota de teste simples
router.get("/test-tracks", (req, res) => {
  res.json({
    success: true,
    message: "Rota de teste tracks funcionando!",
    timestamp: new Date().toISOString()
  });
});

export default router;
