# 🚀 G<PERSON><PERSON> <PERSON> Iní<PERSON>ápido - Sistema de Playlist Interativa

## 📋 **Pré-requisitos**

### Obrigatórios

- **Docker** 20.10+ e **Docker Compose** 2.0+
- **Chave da YouTube Data API v3** ([<PERSON> obter](https://developers.google.com/youtube/v3/getting-started))

### Para Desenvolvimento Local

- **Node.js** 18+
- **npm** ou **yarn**
- **Git**

---

## ⚡ **Instalação Rápida (Docker)**

### 1. Clone o Repositório

```bash
git clone <repository-url>
cd restaurant-playlist-system
```

### 2. Configure as Variáveis de Ambiente

```bash
cp .env.example .env
```

**Edite o arquivo `.env` e configure:**

```env
# OBRIGATÓRIO: Chave da API do YouTube
YOUTUBE_API_KEY=sua-chave-da-api-do-youtube

# RECOMENDADO: Alterar em produção
JWT_SECRET=sua-chave-secreta-jwt-super-segura

# URLs (IMPORTANTE: Use as portas corretas)
FRONTEND_URL=http://localhost:8000
API_URL=http://localhost:8001
VITE_API_URL=http://localhost:8001
VITE_WS_URL=ws://localhost:8001
```

### 3. Execute o Script de Setup

```bash
chmod +x scripts/setup.sh
./scripts/setup.sh
```

### 4. Acesse o Sistema

- **Frontend**: http://localhost:8000
- **API**: http://localhost:8001
- **Admin**: http://localhost:8000/admin
- **Adminer** (dev): http://localhost:8004

### 5. Login Inicial

```
Email: <EMAIL>
Senha: admin123
```

---

## 🛠️ **Desenvolvimento Local**

### Configuração do Ambiente de Desenvolvimento

```bash
# Instalar dependências e iniciar serviços
chmod +x scripts/dev.sh
./scripts/dev.sh start
```

### Comandos Úteis

```bash
# Ver logs em tempo real
./scripts/dev.sh logs

# Parar todos os serviços
./scripts/dev.sh stop

# Reiniciar ambiente
./scripts/dev.sh restart

# Ver status dos serviços
./scripts/dev.sh status

# Reset completo (cuidado!)
./scripts/dev.sh reset
```

### Estrutura de Desenvolvimento

```
# Backend (Node.js + TypeScript)
cd backend
npm install
npm run dev        # Servidor com hot reload
npm run build      # Build para produção
npm run lint       # Verificar código
npm test           # Executar testes

# Frontend (React + TypeScript)
cd frontend
npm install
npm run dev        # Servidor com hot reload
npm run build      # Build para produção
npm run lint       # Verificar código
npm test           # Executar testes
```

---

## 🎯 **Como Usar o Sistema**

### Para Clientes (Interface Principal)

1. **Acesse um Restaurante**

   - Escaneie o QR Code na mesa
   - Ou acesse: `http://localhost:8000/restaurant/demo-restaurant`

2. **Sugira Músicas**

   - Use a aba "Buscar Música"
   - Digite o nome da música ou artista
   - Clique em "Sugerir" na música desejada

3. **Vote nas Sugestões**

   - Use a aba "Fila" para ver músicas na fila
   - Vote 👍 ou 👎 nas suas favoritas
   - Acompanhe sua posição na fila

4. **Veja Todas as Sugestões**
   - Use a aba "Sugestões" para ver histórico
   - Filtre por status (aprovada, pendente, etc.)

### Para Administradores (Dashboard)

1. **Acesse o Admin**

   - Vá para: `http://localhost:8000/admin`
   - Faça login com as credenciais iniciais

2. **Gerencie a Fila**

   - Visualize músicas na fila atual
   - Aprove/rejeite sugestões pendentes
   - Controle a reprodução

3. **Configure o Restaurante**

   - Defina horários de funcionamento
   - Configure filtros de moderação
   - Personalize a interface

4. **Veja Analytics**
   - Métricas de engajamento
   - Músicas mais populares
   - Atividade por horário

---

## 🔧 **Configurações Avançadas**

### Configuração de Produção

1. **Variáveis de Ambiente**

```env
NODE_ENV=production
JWT_SECRET=chave-super-secreta-de-producao
DATABASE_URL=********************************/db
REDIS_URL=redis://host:6379
YOUTUBE_API_KEY=sua-chave-real
FRONTEND_URL=https://seudominio.com
API_URL=https://api.seudominio.com
```

2. **Deploy para Produção**

```bash
chmod +x scripts/deploy.sh
./scripts/deploy.sh
```

### Configuração do YouTube API

1. **Acesse o Google Cloud Console**

   - Vá para: https://console.cloud.google.com/

2. **Crie um Projeto**

   - Clique em "Novo Projeto"
   - Dê um nome ao projeto

3. **Ative a YouTube Data API v3**

   - Vá para "APIs e Serviços" > "Biblioteca"
   - Busque por "YouTube Data API v3"
   - Clique em "Ativar"

4. **Crie Credenciais**

   - Vá para "APIs e Serviços" > "Credenciais"
   - Clique em "Criar Credenciais" > "Chave de API"
   - Copie a chave gerada

5. **Configure Restrições (Recomendado)**
   - Edite a chave criada
   - Adicione restrições de IP ou HTTP referrer
   - Limite às APIs do YouTube

### Configuração de SSL/HTTPS

1. **Obtenha Certificados SSL**

```bash
# Usando Let's Encrypt (Certbot)
sudo certbot certonly --standalone -d seudominio.com
```

2. **Configure o Nginx**

```bash
# Edite nginx/nginx.conf
# Descomente as seções HTTPS
# Aponte para seus certificados SSL
```

3. **Atualize URLs**

```env
FRONTEND_URL=https://seudominio.com
API_URL=https://api.seudominio.com
```

---

## 🐛 **Troubleshooting**

### Problemas Comuns

#### "YouTube API Key não configurada"

```bash
# Verifique se a chave está no .env
grep YOUTUBE_API_KEY .env

# Reinicie os containers
docker-compose restart backend
```

#### "Erro de conexão com banco de dados"

```bash
# Verifique se o PostgreSQL está rodando
docker-compose ps postgres

# Veja os logs do banco
docker-compose logs postgres

# Reinicie o banco
docker-compose restart postgres
```

#### "Frontend não carrega"

```bash
# Verifique se o container está rodando
docker-compose ps frontend

# Veja os logs
docker-compose logs frontend

# Reconstrua a imagem
docker-compose build frontend
```

#### "WebSocket não conecta"

```bash
# Verifique as URLs no .env
grep WS_URL .env

# Teste a conexão
curl -f http://localhost:5000/health

# Reinicie o backend
docker-compose restart backend
```

### Logs e Debug

```bash
# Ver todos os logs
docker-compose logs -f

# Logs específicos
docker-compose logs -f backend
docker-compose logs -f frontend
docker-compose logs -f postgres

# Entrar no container para debug
docker-compose exec backend sh
docker-compose exec frontend sh
```

### Reset Completo

```bash
# Parar tudo e remover volumes
docker-compose down -v

# Remover imagens
docker-compose down --rmi all

# Reconstruir tudo
docker-compose build --no-cache
docker-compose up -d
```

---

## 📚 **Recursos Adicionais**

### Documentação

- **API Docs**: http://localhost:5000/api-docs (em desenvolvimento)
- **Roadmap**: [ROADMAP.md](./ROADMAP.md)
- **Contribuição**: [CONTRIBUTING.md](./CONTRIBUTING.md)

### Ferramentas de Desenvolvimento

- **Adminer**: http://localhost:8080 (gestão do banco)
- **Redis Commander**: http://localhost:8081 (gestão do cache)

### Suporte

- **Issues**: [GitHub Issues](https://github.com/seu-repo/issues)
- **Discussões**: [GitHub Discussions](https://github.com/seu-repo/discussions)

---

## 🎉 **Próximos Passos**

1. **Explore o Sistema**

   - Teste todas as funcionalidades
   - Sugira algumas músicas
   - Vote nas sugestões

2. **Personalize**

   - Configure seu restaurante
   - Ajuste as configurações de moderação
   - Personalize a interface

3. **Contribua**

   - Veja o [ROADMAP.md](./ROADMAP.md) para próximas funcionalidades
   - Reporte bugs ou sugira melhorias
   - Contribua com código

4. **Deploy**
   - Configure um ambiente de produção
   - Obtenha um domínio
   - Configure SSL/HTTPS

**Divirta-se criando playlists colaborativas! 🎵**
