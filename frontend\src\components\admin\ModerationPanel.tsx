import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  CheckCircle, 
  XCircle, 
  Clock, 
  AlertTriangle, 
  Music, 
  User, 
  Calendar,
  Filter,
  Search,
  Eye,
  MessageSquare
} from 'lucide-react';
import { toast } from 'react-hot-toast';

interface Suggestion {
  id: string;
  songTitle: string;
  artist: string;
  thumbnailUrl: string;
  duration: string;
  status: 'pending' | 'approved' | 'rejected';
  submittedBy: string;
  submittedAt: string;
  voteCount: number;
  upvotes: number;
  downvotes: number;
  moderationReason?: string;
  flagged: boolean;
  priority: 'low' | 'medium' | 'high';
}

const ModerationPanel: React.FC = () => {
  const [suggestions, setSuggestions] = useState<Suggestion[]>([]);
  const [filter, setFilter] = useState<'all' | 'pending' | 'approved' | 'rejected'>('pending');
  const [searchQuery, setSearchQuery] = useState('');
  const [loading, setLoading] = useState(false);
  const [selectedSuggestion, setSelectedSuggestion] = useState<Suggestion | null>(null);

  // Mock data para demonstração
  useEffect(() => {
    const mockSuggestions: Suggestion[] = [
      {
        id: '1',
        songTitle: 'Bohemian Rhapsody',
        artist: 'Queen',
        thumbnailUrl: 'https://i.ytimg.com/vi/fJ9rUzIMcZQ/mqdefault.jpg',
        duration: '5:55',
        status: 'pending',
        submittedBy: 'Cliente #1234',
        submittedAt: '2024-01-15T14:30:00Z',
        voteCount: 15,
        upvotes: 18,
        downvotes: 3,
        flagged: false,
        priority: 'high'
      },
      {
        id: '2',
        songTitle: 'Despacito',
        artist: 'Luis Fonsi ft. Daddy Yankee',
        thumbnailUrl: 'https://i.ytimg.com/vi/kJQP7kiw5Fk/mqdefault.jpg',
        duration: '4:41',
        status: 'pending',
        submittedBy: 'Cliente #5678',
        submittedAt: '2024-01-15T13:15:00Z',
        voteCount: 8,
        upvotes: 10,
        downvotes: 2,
        flagged: true,
        priority: 'medium'
      },
      {
        id: '3',
        songTitle: 'Shape of You',
        artist: 'Ed Sheeran',
        thumbnailUrl: 'https://i.ytimg.com/vi/JGwWNGJdvx8/mqdefault.jpg',
        duration: '4:23',
        status: 'approved',
        submittedBy: 'Cliente #9012',
        submittedAt: '2024-01-15T12:00:00Z',
        voteCount: 22,
        upvotes: 25,
        downvotes: 3,
        flagged: false,
        priority: 'low'
      },
      {
        id: '4',
        songTitle: 'Never Gonna Give You Up',
        artist: 'Rick Astley',
        thumbnailUrl: 'https://i.ytimg.com/vi/dQw4w9WgXcQ/mqdefault.jpg',
        duration: '3:33',
        status: 'rejected',
        submittedBy: 'Cliente #3456',
        submittedAt: '2024-01-15T11:45:00Z',
        voteCount: -5,
        upvotes: 2,
        downvotes: 7,
        moderationReason: 'Música não adequada para o ambiente do restaurante',
        flagged: false,
        priority: 'low'
      }
    ];
    setSuggestions(mockSuggestions);
  }, []);

  const filteredSuggestions = suggestions.filter(suggestion => {
    const matchesFilter = filter === 'all' || suggestion.status === filter;
    const matchesSearch = searchQuery === '' || 
      suggestion.songTitle.toLowerCase().includes(searchQuery.toLowerCase()) ||
      suggestion.artist.toLowerCase().includes(searchQuery.toLowerCase());
    
    return matchesFilter && matchesSearch;
  });

  const moderateSuggestion = async (suggestionId: string, action: 'approve' | 'reject', reason?: string) => {
    setLoading(true);
    try {
      // Em uma implementação real, isso faria uma chamada para a API
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      setSuggestions(prev => prev.map(s => 
        s.id === suggestionId 
          ? { 
              ...s, 
              status: action === 'approve' ? 'approved' : 'rejected',
              moderationReason: reason
            }
          : s
      ));
      
      toast.success(`Sugestão ${action === 'approve' ? 'aprovada' : 'rejeitada'} com sucesso!`);
      setSelectedSuggestion(null);
    } catch (error) {
      toast.error('Erro ao moderar sugestão');
    } finally {
      setLoading(false);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'approved':
        return 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400';
      case 'rejected':
        return 'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-400';
      default:
        return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-400';
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'high':
        return 'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-400';
      case 'medium':
        return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-400';
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-400';
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString('pt-BR');
  };

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-2xl font-bold text-gray-900 dark:text-white">
            Painel de Moderação
          </h2>
          <p className="text-gray-600 dark:text-gray-400 mt-1">
            Gerencie sugestões de música dos clientes
          </p>
        </div>
        
        <div className="flex items-center space-x-4">
          <div className="flex items-center space-x-2 text-sm">
            <div className="flex items-center space-x-1">
              <div className="w-3 h-3 bg-yellow-400 rounded-full"></div>
              <span className="text-gray-600 dark:text-gray-400">
                {suggestions.filter(s => s.status === 'pending').length} pendentes
              </span>
            </div>
            <div className="flex items-center space-x-1">
              <div className="w-3 h-3 bg-red-400 rounded-full"></div>
              <span className="text-gray-600 dark:text-gray-400">
                {suggestions.filter(s => s.flagged).length} sinalizadas
              </span>
            </div>
          </div>
        </div>
      </div>

      {/* Filtros e busca */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
        <div className="flex flex-col sm:flex-row gap-4">
          <div className="flex-1">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
              <input
                type="text"
                placeholder="Buscar por música ou artista..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="w-full pl-10 pr-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
              />
            </div>
          </div>
          
          <div className="flex items-center space-x-2">
            <Filter className="w-4 h-4 text-gray-400" />
            <select
              value={filter}
              onChange={(e) => setFilter(e.target.value as any)}
              className="border border-gray-300 dark:border-gray-600 rounded-lg px-3 py-2 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
            >
              <option value="all">Todas</option>
              <option value="pending">Pendentes</option>
              <option value="approved">Aprovadas</option>
              <option value="rejected">Rejeitadas</option>
            </select>
          </div>
        </div>
      </div>

      {/* Lista de sugestões */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow">
        <div className="p-6">
          <div className="space-y-4">
            <AnimatePresence>
              {filteredSuggestions.map((suggestion) => (
                <motion.div
                  key={suggestion.id}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0, y: -20 }}
                  className="border border-gray-200 dark:border-gray-700 rounded-lg p-4 hover:bg-gray-50 dark:hover:bg-gray-700/50 transition-colors"
                >
                  <div className="flex items-start justify-between">
                    <div className="flex items-start space-x-4">
                      <img
                        src={suggestion.thumbnailUrl}
                        alt={suggestion.songTitle}
                        className="w-16 h-12 object-cover rounded"
                      />
                      
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center space-x-2 mb-1">
                          <h4 className="font-medium text-gray-900 dark:text-white">
                            {suggestion.songTitle}
                          </h4>
                          {suggestion.flagged && (
                            <AlertTriangle className="w-4 h-4 text-red-500" />
                          )}
                        </div>
                        
                        <p className="text-sm text-gray-600 dark:text-gray-400 mb-2">
                          {suggestion.artist} • {suggestion.duration}
                        </p>
                        
                        <div className="flex items-center space-x-4 text-xs text-gray-500 dark:text-gray-400">
                          <div className="flex items-center space-x-1">
                            <User className="w-3 h-3" />
                            <span>{suggestion.submittedBy}</span>
                          </div>
                          <div className="flex items-center space-x-1">
                            <Calendar className="w-3 h-3" />
                            <span>{formatDate(suggestion.submittedAt)}</span>
                          </div>
                          <div className="flex items-center space-x-1">
                            <span>👍 {suggestion.upvotes}</span>
                            <span>👎 {suggestion.downvotes}</span>
                          </div>
                        </div>
                        
                        {suggestion.moderationReason && (
                          <div className="mt-2 p-2 bg-gray-100 dark:bg-gray-700 rounded text-xs">
                            <strong>Motivo:</strong> {suggestion.moderationReason}
                          </div>
                        )}
                      </div>
                    </div>
                    
                    <div className="flex items-center space-x-2">
                      <span className={`px-2 py-1 rounded-full text-xs font-medium ${getPriorityColor(suggestion.priority)}`}>
                        {suggestion.priority}
                      </span>
                      
                      <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(suggestion.status)}`}>
                        {suggestion.status === 'pending' ? 'Pendente' : 
                         suggestion.status === 'approved' ? 'Aprovada' : 'Rejeitada'}
                      </span>
                      
                      {suggestion.status === 'pending' && (
                        <div className="flex items-center space-x-1">
                          <button
                            onClick={() => moderateSuggestion(suggestion.id, 'approve')}
                            disabled={loading}
                            className="p-2 text-green-600 hover:bg-green-100 dark:hover:bg-green-900/20 rounded-lg"
                            title="Aprovar"
                          >
                            <CheckCircle className="w-4 h-4" />
                          </button>
                          
                          <button
                            onClick={() => setSelectedSuggestion(suggestion)}
                            disabled={loading}
                            className="p-2 text-red-600 hover:bg-red-100 dark:hover:bg-red-900/20 rounded-lg"
                            title="Rejeitar"
                          >
                            <XCircle className="w-4 h-4" />
                          </button>
                        </div>
                      )}
                    </div>
                  </div>
                </motion.div>
              ))}
            </AnimatePresence>
            
            {filteredSuggestions.length === 0 && (
              <div className="text-center py-12">
                <Music className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                <p className="text-gray-600 dark:text-gray-400">
                  {searchQuery ? 'Nenhuma sugestão encontrada' : 'Nenhuma sugestão para moderar'}
                </p>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Modal de rejeição */}
      {selectedSuggestion && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white dark:bg-gray-800 rounded-lg p-6 w-full max-w-md">
            <h3 className="text-lg font-semibold mb-4">Rejeitar Sugestão</h3>
            <p className="text-gray-600 dark:text-gray-400 mb-4">
              Você está rejeitando: <strong>{selectedSuggestion.songTitle}</strong>
            </p>
            
            <textarea
              placeholder="Motivo da rejeição (opcional)"
              className="w-full border border-gray-300 dark:border-gray-600 rounded-lg p-3 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
              rows={3}
              onChange={(e) => {
                setSelectedSuggestion({
                  ...selectedSuggestion,
                  moderationReason: e.target.value
                });
              }}
            />
            
            <div className="flex justify-end space-x-3 mt-4">
              <button
                onClick={() => setSelectedSuggestion(null)}
                className="px-4 py-2 text-gray-600 dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg"
              >
                Cancelar
              </button>
              <button
                onClick={() => moderateSuggestion(selectedSuggestion.id, 'reject', selectedSuggestion.moderationReason)}
                disabled={loading}
                className="px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 disabled:opacity-50"
              >
                {loading ? 'Rejeitando...' : 'Rejeitar'}
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default ModerationPanel;
