import { v4 as uuidv4 } from "uuid";

export interface ClientSession {
  id: string;
  sessionToken: string;
  restaurantId: string;
  tableNumber?: string;
  clientName?: string;
  deviceInfo?: {
    type?: "mobile" | "tablet" | "desktop";
    os?: string;
    browser?: string;
    screenResolution?: string;
    language?: string;
    timezone?: string;
  };
  lastActivity: string;
  suggestionsCount: number;
  votesCount: number;
  pageViews: number;
  sessionDuration: number;
  formattedDuration: string;
  preferences?: {
    favoriteGenres?: string[];
    language?: string;
    theme?: "light" | "dark";
    notifications?: boolean;
  };
  isActive: boolean;
  isSessionActive: boolean;
  isNewSession: boolean;
  engagementLevel: "low" | "medium" | "high";
  createdAt: string;
  updatedAt: string;
  // Gamification
  points: number;
  level: number;
  badges: string[];
  streak: number;
}

export interface UserStats {
  points: number;
  level: number;
  badges: string[];
  suggestionsCount: number;
  votesCount: number;
  streak: number;
}

class SessionService {
  private sessionToken: string | null = null;
  private session: ClientSession | null = null;
  private apiUrl = "http://localhost:8001/api/v1";

  constructor() {
    // Try to restore session from localStorage
    this.restoreSession();
  }

  // Generate unique session for each QR scan
  async createSession(
    restaurantId: string,
    tableNumber?: string,
    clientName?: string
  ): Promise<ClientSession> {
    try {
      // Always create a new session for each QR scan
      const sessionToken = uuidv4();
      const deviceInfo = this.getDeviceInfo();

      const response = await fetch(`${this.apiUrl}/client/session`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          sessionToken,
          restaurantId,
          tableNumber,
          clientName,
          deviceInfo,
        }),
      });

      if (response.ok) {
        const session = await response.json();
        this.session = session;
        this.sessionToken = session.sessionToken;
        this.saveSession();
        return session;
      } else {
        // Fallback to local session if backend fails
        return this.createLocalSession(restaurantId, tableNumber, clientName);
      }
    } catch (error) {
      console.error("Error creating session:", error);
      return this.createLocalSession(restaurantId, tableNumber, clientName);
    }
  }

  // Create local session as fallback
  private createLocalSession(
    restaurantId: string,
    tableNumber?: string,
    clientName?: string
  ): ClientSession {
    const sessionToken = uuidv4();
    const now = new Date().toISOString();

    const session: ClientSession = {
      id: uuidv4(),
      sessionToken,
      restaurantId,
      tableNumber,
      clientName,
      deviceInfo: this.getDeviceInfo(),
      lastActivity: now,
      suggestionsCount: 0,
      votesCount: 0,
      pageViews: 1,
      sessionDuration: 0,
      formattedDuration: "0m",
      isActive: true,
      isSessionActive: true,
      isNewSession: true,
      engagementLevel: "low",
      createdAt: now,
      updatedAt: now,
      points: 0,
      level: 1,
      badges: [],
      streak: 0,
    };

    this.session = session;
    this.sessionToken = sessionToken;
    this.saveSession();
    return session;
  }

  // Get current session
  getSession(): ClientSession | null {
    return this.session;
  }

  // Get session token for API calls
  getSessionToken(): string | null {
    return this.sessionToken;
  }

  // Update session stats
  updateStats(stats: Partial<UserStats>): void {
    if (this.session) {
      this.session = { ...this.session, ...stats };
      this.saveSession();
    }
  }

  // Increment suggestion count
  incrementSuggestions(): void {
    if (this.session) {
      this.session.suggestionsCount++;
      this.session.points += 20; // Award points for suggestions
      this.updateLevel();
      this.saveSession();
    }
  }

  // Increment vote count
  incrementVotes(): void {
    if (this.session) {
      this.session.votesCount++;
      this.session.points += 10; // Award points for votes
      this.updateLevel();
      this.saveSession();
    }
  }

  // Update level based on points
  private updateLevel(): void {
    if (this.session) {
      const newLevel = Math.floor(this.session.points / 100) + 1;
      if (newLevel > this.session.level) {
        this.session.level = newLevel;
        // Could trigger level up notification here
      }
    }
  }

  // Award badge
  awardBadge(badge: string): boolean {
    if (this.session && !this.session.badges.includes(badge)) {
      this.session.badges.push(badge);
      this.saveSession();
      return true; // New badge awarded
    }
    return false; // Badge already exists
  }

  // Get device info
  private getDeviceInfo() {
    const userAgent = navigator.userAgent;
    const screenResolution = `${screen.width}x${screen.height}`;
    const language = navigator.language;
    const timezone = Intl.DateTimeFormat().resolvedOptions().timeZone;

    let deviceType: "mobile" | "tablet" | "desktop" = "desktop";
    if (/Mobile|Android|iPhone|iPad/.test(userAgent)) {
      deviceType = /iPad/.test(userAgent) ? "tablet" : "mobile";
    }

    return {
      type: deviceType,
      screenResolution,
      language,
      timezone,
    };
  }

  // Save session to localStorage
  private saveSession(): void {
    if (this.session && this.sessionToken) {
      localStorage.setItem("clientSession", JSON.stringify(this.session));
      localStorage.setItem("sessionToken", this.sessionToken);
    }
  }

  // Restore session from localStorage
  private restoreSession(): void {
    try {
      const savedSession = localStorage.getItem("clientSession");
      const savedToken = localStorage.getItem("sessionToken");

      if (savedSession && savedToken) {
        this.session = JSON.parse(savedSession);
        this.sessionToken = savedToken;
      }
    } catch (error) {
      console.error("Error restoring session:", error);
      this.clearSession();
    }
  }

  // Clear session
  clearSession(): void {
    this.session = null;
    this.sessionToken = null;
    localStorage.removeItem("clientSession");
    localStorage.removeItem("sessionToken");
  }

  // Check if session is valid
  isSessionValid(): boolean {
    if (!this.session || !this.sessionToken) return false;

    // Check if session is too old (24 hours)
    const sessionAge = Date.now() - new Date(this.session.createdAt).getTime();
    const maxAge = 24 * 60 * 60 * 1000; // 24 hours

    return sessionAge < maxAge;
  }

  // Force new session (for new QR scans)
  async forceNewSession(
    restaurantId: string,
    tableNumber?: string,
    clientName?: string
  ): Promise<ClientSession> {
    this.clearSession();
    return this.createSession(restaurantId, tableNumber, clientName);
  }
}

export const sessionService = new SessionService();
export default sessionService;
