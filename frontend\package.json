{"name": "restaurant-playlist-frontend", "version": "1.0.0", "description": "Frontend para Sistema de Playlist Interativa para Restaurantes", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "build:check": "tsc && vite build", "preview": "vite preview", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "lint:fix": "eslint . --ext ts,tsx --fix", "test": "vitest", "test:ui": "vitest --ui", "test:coverage": "vitest --coverage"}, "dependencies": {"@types/react-youtube": "^7.6.2", "axios": "^1.4.0", "clsx": "^2.0.0", "date-fns": "^2.30.0", "framer-motion": "^10.12.18", "lucide-react": "^0.263.1", "react": "^18.2.0", "react-dom": "^18.2.0", "react-hook-form": "^7.45.2", "react-hot-toast": "^2.4.1", "react-intersection-observer": "^9.5.2", "react-query": "^3.39.3", "react-router-dom": "^6.14.2", "react-use": "^17.4.0", "react-virtualized-auto-sizer": "^1.0.20", "react-window": "^1.8.8", "react-youtube": "^10.1.0", "socket.io-client": "^4.7.2", "tailwind-merge": "^1.14.0", "uuid": "^9.0.1", "zustand": "^4.4.1"}, "devDependencies": {"@testing-library/jest-dom": "^5.17.0", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^14.4.3", "@types/react": "^18.2.15", "@types/react-dom": "^18.2.7", "@types/react-window": "^1.8.5", "@types/uuid": "^9.0.8", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "@vitejs/plugin-react": "^4.0.3", "@vitest/ui": "^0.34.1", "autoprefixer": "^10.4.14", "eslint": "^8.45.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.3", "jsdom": "^22.1.0", "postcss": "^8.4.27", "tailwindcss": "^3.3.3", "typescript": "^5.0.2", "vite": "^4.4.5", "vite-plugin-pwa": "^0.16.4", "vitest": "^0.34.1"}, "keywords": ["restaurant", "playlist", "music", "react", "typescript", "vite", "tailwindcss"], "author": "Restaurant Playlist System", "license": "MIT"}