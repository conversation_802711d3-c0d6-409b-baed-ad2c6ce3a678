import React from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { X, CheckCircle, AlertCircle, Info, AlertTriangle } from 'lucide-react';
import { useNotifications } from '@/store';
import { Notification } from '@/types';

const NotificationContainer: React.FC = () => {
  const { notifications, removeNotification } = useNotifications();

  const getNotificationConfig = (type: Notification['type']) => {
    switch (type) {
      case 'success':
        return {
          icon: CheckCircle,
          bgColor: 'bg-green-50 dark:bg-green-900/20',
          borderColor: 'border-green-200 dark:border-green-800',
          iconColor: 'text-green-600 dark:text-green-400',
          titleColor: 'text-green-900 dark:text-green-100',
          messageColor: 'text-green-700 dark:text-green-300',
        };
      case 'error':
        return {
          icon: AlertCircle,
          bgColor: 'bg-red-50 dark:bg-red-900/20',
          borderColor: 'border-red-200 dark:border-red-800',
          iconColor: 'text-red-600 dark:text-red-400',
          titleColor: 'text-red-900 dark:text-red-100',
          messageColor: 'text-red-700 dark:text-red-300',
        };
      case 'warning':
        return {
          icon: AlertTriangle,
          bgColor: 'bg-yellow-50 dark:bg-yellow-900/20',
          borderColor: 'border-yellow-200 dark:border-yellow-800',
          iconColor: 'text-yellow-600 dark:text-yellow-400',
          titleColor: 'text-yellow-900 dark:text-yellow-100',
          messageColor: 'text-yellow-700 dark:text-yellow-300',
        };
      case 'info':
      default:
        return {
          icon: Info,
          bgColor: 'bg-blue-50 dark:bg-blue-900/20',
          borderColor: 'border-blue-200 dark:border-blue-800',
          iconColor: 'text-blue-600 dark:text-blue-400',
          titleColor: 'text-blue-900 dark:text-blue-100',
          messageColor: 'text-blue-700 dark:text-blue-300',
        };
    }
  };

  return (
    <div className="fixed top-4 right-4 z-50 space-y-2 max-w-sm w-full">
      <AnimatePresence>
        {notifications.map((notification) => {
          const config = getNotificationConfig(notification.type);
          const Icon = config.icon;

          return (
            <motion.div
              key={notification.id}
              initial={{ opacity: 0, x: 300, scale: 0.9 }}
              animate={{ opacity: 1, x: 0, scale: 1 }}
              exit={{ opacity: 0, x: 300, scale: 0.9 }}
              transition={{ type: "spring", stiffness: 500, damping: 30 }}
              className={`
                ${config.bgColor} ${config.borderColor}
                border rounded-lg shadow-lg p-4 backdrop-blur-sm
              `}
            >
              <div className="flex items-start space-x-3">
                {/* Ícone */}
                <div className="flex-shrink-0">
                  <Icon className={`w-5 h-5 ${config.iconColor}`} />
                </div>

                {/* Conteúdo */}
                <div className="flex-1 min-w-0">
                  <h4 className={`text-sm font-medium ${config.titleColor}`}>
                    {notification.title}
                  </h4>
                  <p className={`text-sm mt-1 ${config.messageColor}`}>
                    {notification.message}
                  </p>

                  {/* Ação opcional */}
                  {notification.action && (
                    <button
                      onClick={notification.action.onClick}
                      className={`
                        text-sm font-medium mt-2 hover:underline
                        ${config.iconColor}
                      `}
                    >
                      {notification.action.label}
                    </button>
                  )}
                </div>

                {/* Botão de fechar */}
                <button
                  onClick={() => removeNotification(notification.id)}
                  className={`
                    flex-shrink-0 p-1 rounded-md hover:bg-black/5 dark:hover:bg-white/5
                    ${config.iconColor} transition-colors
                  `}
                >
                  <X className="w-4 h-4" />
                </button>
              </div>

              {/* Barra de progresso para notificações com duração */}
              {notification.duration && notification.duration > 0 && (
                <motion.div
                  initial={{ width: "100%" }}
                  animate={{ width: "0%" }}
                  transition={{ duration: notification.duration / 1000, ease: "linear" }}
                  className={`
                    h-1 mt-3 rounded-full
                    ${config.iconColor.replace('text-', 'bg-')}
                    opacity-30
                  `}
                />
              )}
            </motion.div>
          );
        })}
      </AnimatePresence>
    </div>
  );
};

export default NotificationContainer;
