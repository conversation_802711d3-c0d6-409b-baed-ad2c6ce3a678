import { OAuth2Client, JW<PERSON>, Compute, UserRefreshClient, BaseExternalAccountClient, GaxiosResponseWithHTTP2, GoogleConfigurable, MethodOptions, StreamMethodOptions, GlobalOptions, GoogleAuth, BodyResponseCallback, APIRequestContext } from 'googleapis-common';
import { Readable } from 'stream';
export declare namespace config_v1 {
    export interface Options extends GlobalOptions {
        version: 'v1';
    }
    interface StandardParameters {
        /**
         * Auth client or API Key for the request
         */
        auth?: string | OAuth2Client | JWT | Compute | UserRefreshClient | BaseExternalAccountClient | GoogleAuth;
        /**
         * V1 error format.
         */
        '$.xgafv'?: string;
        /**
         * OAuth access token.
         */
        access_token?: string;
        /**
         * Data format for response.
         */
        alt?: string;
        /**
         * JSONP
         */
        callback?: string;
        /**
         * Selector specifying which fields to include in a partial response.
         */
        fields?: string;
        /**
         * API key. Your API key identifies your project and provides you with API access, quota, and reports. Required unless you provide an OAuth 2.0 token.
         */
        key?: string;
        /**
         * OAuth 2.0 token for the current user.
         */
        oauth_token?: string;
        /**
         * Returns response with indentations and line breaks.
         */
        prettyPrint?: boolean;
        /**
         * Available to use for quota purposes for server-side applications. Can be any arbitrary string assigned to a user, but should not exceed 40 characters.
         */
        quotaUser?: string;
        /**
         * Legacy upload protocol for media (e.g. "media", "multipart").
         */
        uploadType?: string;
        /**
         * Upload protocol for media (e.g. "raw", "multipart").
         */
        upload_protocol?: string;
    }
    /**
     * Infrastructure Manager API
     *
     * Creates and manages Google Cloud Platform resources and infrastructure.
     *
     * @example
     * ```js
     * const {google} = require('googleapis');
     * const config = google.config('v1');
     * ```
     */
    export class Config {
        context: APIRequestContext;
        projects: Resource$Projects;
        constructor(options: GlobalOptions, google?: GoogleConfigurable);
    }
    /**
     * Outputs and artifacts from applying a deployment.
     */
    export interface Schema$ApplyResults {
        /**
         * Location of artifacts (e.g. logs) in Google Cloud Storage. Format: `gs://{bucket\}/{object\}`
         */
        artifacts?: string | null;
        /**
         * Location of a blueprint copy and other manifests in Google Cloud Storage. Format: `gs://{bucket\}/{object\}`
         */
        content?: string | null;
        /**
         * Map of output name to output info.
         */
        outputs?: {
            [key: string]: Schema$TerraformOutput;
        } | null;
    }
    /**
     * Specifies the audit configuration for a service. The configuration determines which permission types are logged, and what identities, if any, are exempted from logging. An AuditConfig must have one or more AuditLogConfigs. If there are AuditConfigs for both `allServices` and a specific service, the union of the two AuditConfigs is used for that service: the log_types specified in each AuditConfig are enabled, and the exempted_members in each AuditLogConfig are exempted. Example Policy with multiple AuditConfigs: { "audit_configs": [ { "service": "allServices", "audit_log_configs": [ { "log_type": "DATA_READ", "exempted_members": [ "user:<EMAIL>" ] \}, { "log_type": "DATA_WRITE" \}, { "log_type": "ADMIN_READ" \} ] \}, { "service": "sampleservice.googleapis.com", "audit_log_configs": [ { "log_type": "DATA_READ" \}, { "log_type": "DATA_WRITE", "exempted_members": [ "user:<EMAIL>" ] \} ] \} ] \} For sampleservice, this policy enables DATA_READ, DATA_WRITE and ADMIN_READ logging. It also exempts `<EMAIL>` from DATA_READ logging, and `<EMAIL>` from DATA_WRITE logging.
     */
    export interface Schema$AuditConfig {
        /**
         * The configuration for logging of each type of permission.
         */
        auditLogConfigs?: Schema$AuditLogConfig[];
        /**
         * Specifies a service that will be enabled for audit logging. For example, `storage.googleapis.com`, `cloudsql.googleapis.com`. `allServices` is a special value that covers all services.
         */
        service?: string | null;
    }
    /**
     * Provides the configuration for logging a type of permissions. Example: { "audit_log_configs": [ { "log_type": "DATA_READ", "exempted_members": [ "user:<EMAIL>" ] \}, { "log_type": "DATA_WRITE" \} ] \} This enables 'DATA_READ' and 'DATA_WRITE' logging, <NAME_EMAIL> from DATA_READ logging.
     */
    export interface Schema$AuditLogConfig {
        /**
         * Specifies the identities that do not cause logging for this type of permission. Follows the same format of Binding.members.
         */
        exemptedMembers?: string[] | null;
        /**
         * The log type that this config enables.
         */
        logType?: string | null;
    }
    /**
     * Associates `members`, or principals, with a `role`.
     */
    export interface Schema$Binding {
        /**
         * The condition that is associated with this binding. If the condition evaluates to `true`, then this binding applies to the current request. If the condition evaluates to `false`, then this binding does not apply to the current request. However, a different role binding might grant the same role to one or more of the principals in this binding. To learn which resources support conditions in their IAM policies, see the [IAM documentation](https://cloud.google.com/iam/help/conditions/resource-policies).
         */
        condition?: Schema$Expr;
        /**
         * Specifies the principals requesting access for a Google Cloud resource. `members` can have the following values: * `allUsers`: A special identifier that represents anyone who is on the internet; with or without a Google account. * `allAuthenticatedUsers`: A special identifier that represents anyone who is authenticated with a Google account or a service account. Does not include identities that come from external identity providers (IdPs) through identity federation. * `user:{emailid\}`: An email address that represents a specific Google account. For example, `<EMAIL>` . * `serviceAccount:{emailid\}`: An email address that represents a Google service account. For example, `<EMAIL>`. * `serviceAccount:{projectid\}.svc.id.goog[{namespace\}/{kubernetes-sa\}]`: An identifier for a [Kubernetes service account](https://cloud.google.com/kubernetes-engine/docs/how-to/kubernetes-service-accounts). For example, `my-project.svc.id.goog[my-namespace/my-kubernetes-sa]`. * `group:{emailid\}`: An email address that represents a Google group. For example, `<EMAIL>`. * `domain:{domain\}`: The G Suite domain (primary) that represents all the users of that domain. For example, `google.com` or `example.com`. * `principal://iam.googleapis.com/locations/global/workforcePools/{pool_id\}/subject/{subject_attribute_value\}`: A single identity in a workforce identity pool. * `principalSet://iam.googleapis.com/locations/global/workforcePools/{pool_id\}/group/{group_id\}`: All workforce identities in a group. * `principalSet://iam.googleapis.com/locations/global/workforcePools/{pool_id\}/attribute.{attribute_name\}/{attribute_value\}`: All workforce identities with a specific attribute value. * `principalSet://iam.googleapis.com/locations/global/workforcePools/{pool_id\}/x`: All identities in a workforce identity pool. * `principal://iam.googleapis.com/projects/{project_number\}/locations/global/workloadIdentityPools/{pool_id\}/subject/{subject_attribute_value\}`: A single identity in a workload identity pool. * `principalSet://iam.googleapis.com/projects/{project_number\}/locations/global/workloadIdentityPools/{pool_id\}/group/{group_id\}`: A workload identity pool group. * `principalSet://iam.googleapis.com/projects/{project_number\}/locations/global/workloadIdentityPools/{pool_id\}/attribute.{attribute_name\}/{attribute_value\}`: All identities in a workload identity pool with a certain attribute. * `principalSet://iam.googleapis.com/projects/{project_number\}/locations/global/workloadIdentityPools/{pool_id\}/x`: All identities in a workload identity pool. * `deleted:user:{emailid\}?uid={uniqueid\}`: An email address (plus unique identifier) representing a user that has been recently deleted. For example, `<EMAIL>?uid=123456789012345678901`. If the user is recovered, this value reverts to `user:{emailid\}` and the recovered user retains the role in the binding. * `deleted:serviceAccount:{emailid\}?uid={uniqueid\}`: An email address (plus unique identifier) representing a service account that has been recently deleted. For example, `<EMAIL>?uid=123456789012345678901`. If the service account is undeleted, this value reverts to `serviceAccount:{emailid\}` and the undeleted service account retains the role in the binding. * `deleted:group:{emailid\}?uid={uniqueid\}`: An email address (plus unique identifier) representing a Google group that has been recently deleted. For example, `<EMAIL>?uid=123456789012345678901`. If the group is recovered, this value reverts to `group:{emailid\}` and the recovered group retains the role in the binding. * `deleted:principal://iam.googleapis.com/locations/global/workforcePools/{pool_id\}/subject/{subject_attribute_value\}`: Deleted single identity in a workforce identity pool. For example, `deleted:principal://iam.googleapis.com/locations/global/workforcePools/my-pool-id/subject/my-subject-attribute-value`.
         */
        members?: string[] | null;
        /**
         * Role that is assigned to the list of `members`, or principals. For example, `roles/viewer`, `roles/editor`, or `roles/owner`. For an overview of the IAM roles and permissions, see the [IAM documentation](https://cloud.google.com/iam/docs/roles-overview). For a list of the available pre-defined roles, see [here](https://cloud.google.com/iam/docs/understanding-roles).
         */
        role?: string | null;
    }
    /**
     * The request message for Operations.CancelOperation.
     */
    export interface Schema$CancelOperationRequest {
    }
    /**
     * A request to delete a state file passed to a 'DeleteStatefile' call.
     */
    export interface Schema$DeleteStatefileRequest {
        /**
         * Required. Lock ID of the lock file to verify that the user who is deleting the state file previously locked the Deployment.
         */
        lockId?: string | null;
    }
    /**
     * A Deployment is a group of resources and configs managed and provisioned by Infra Manager.
     */
    export interface Schema$Deployment {
        /**
         * Optional. Arbitrary key-value metadata storage e.g. to help client tools identify deployments during automation. See https://google.aip.dev/148#annotations for details on format and size limitations.
         */
        annotations?: {
            [key: string]: string;
        } | null;
        /**
         * Optional. User-defined location of Cloud Build logs and artifacts in Google Cloud Storage. Format: `gs://{bucket\}/{folder\}` A default bucket will be bootstrapped if the field is not set or empty. Default bucket format: `gs://--blueprint-config` Constraints: - The bucket needs to be in the same project as the deployment - The path cannot be within the path of `gcs_source` - The field cannot be updated, including changing its presence
         */
        artifactsGcsBucket?: string | null;
        /**
         * Output only. Time when the deployment was created.
         */
        createTime?: string | null;
        /**
         * Output only. Cloud Build instance UUID associated with deleting this deployment.
         */
        deleteBuild?: string | null;
        /**
         * Output only. Location of Cloud Build logs in Google Cloud Storage, populated when deleting this deployment. Format: `gs://{bucket\}/{object\}`.
         */
        deleteLogs?: string | null;
        /**
         * Output only. Location of artifacts from a DeleteDeployment operation.
         */
        deleteResults?: Schema$ApplyResults;
        /**
         * Output only. Error code describing errors that may have occurred.
         */
        errorCode?: string | null;
        /**
         * Output only. Location of Terraform error logs in Google Cloud Storage. Format: `gs://{bucket\}/{object\}`.
         */
        errorLogs?: string | null;
        /**
         * By default, Infra Manager will return a failure when Terraform encounters a 409 code (resource conflict error) during actuation. If this flag is set to true, Infra Manager will instead attempt to automatically import the resource into the Terraform state (for supported resource types) and continue actuation. Not all resource types are supported, refer to documentation.
         */
        importExistingResources?: boolean | null;
        /**
         * Optional. User-defined metadata for the deployment.
         */
        labels?: {
            [key: string]: string;
        } | null;
        /**
         * Output only. Revision name that was most recently applied. Format: `projects/{project\}/locations/{location\}/deployments/{deployment\}/ revisions/{revision\}`
         */
        latestRevision?: string | null;
        /**
         * Output only. Current lock state of the deployment.
         */
        lockState?: string | null;
        /**
         * Identifier. Resource name of the deployment. Format: `projects/{project\}/locations/{location\}/deployments/{deployment\}`
         */
        name?: string | null;
        /**
         * Optional. Input to control quota checks for resources in terraform configuration files. There are limited resources on which quota validation applies.
         */
        quotaValidation?: string | null;
        /**
         * Required. User-specified Service Account (SA) credentials to be used when actuating resources. Format: `projects/{projectID\}/serviceAccounts/{serviceAccount\}`
         */
        serviceAccount?: string | null;
        /**
         * Output only. Current state of the deployment.
         */
        state?: string | null;
        /**
         * Output only. Additional information regarding the current state.
         */
        stateDetail?: string | null;
        /**
         * A blueprint described using Terraform's HashiCorp Configuration Language as a root module.
         */
        terraformBlueprint?: Schema$TerraformBlueprint;
        /**
         * Output only. Errors encountered when deleting this deployment. Errors are truncated to 10 entries, see `delete_results` and `error_logs` for full details.
         */
        tfErrors?: Schema$TerraformError[];
        /**
         * Output only. The current Terraform version set on the deployment. It is in the format of "Major.Minor.Patch", for example, "1.3.10".
         */
        tfVersion?: string | null;
        /**
         * Optional. The user-specified Terraform version constraint. Example: "=1.3.10".
         */
        tfVersionConstraint?: string | null;
        /**
         * Output only. Time when the deployment was last modified.
         */
        updateTime?: string | null;
        /**
         * Optional. The user-specified Cloud Build worker pool resource in which the Cloud Build job will execute. Format: `projects/{project\}/locations/{location\}/workerPools/{workerPoolId\}`. If this field is unspecified, the default Cloud Build worker pool will be used.
         */
        workerPool?: string | null;
    }
    /**
     * Ephemeral metadata content describing the state of a deployment operation.
     */
    export interface Schema$DeploymentOperationMetadata {
        /**
         * Outputs and artifacts from applying a deployment.
         */
        applyResults?: Schema$ApplyResults;
        /**
         * Output only. Cloud Build instance UUID associated with this operation.
         */
        build?: string | null;
        /**
         * Output only. Location of Deployment operations logs in `gs://{bucket\}/{object\}` format.
         */
        logs?: string | null;
        /**
         * The current step the deployment operation is running.
         */
        step?: string | null;
    }
    /**
     * A generic empty message that you can re-use to avoid defining duplicated empty messages in your APIs. A typical example is to use it as the request or the response type of an API method. For instance: service Foo { rpc Bar(google.protobuf.Empty) returns (google.protobuf.Empty); \}
     */
    export interface Schema$Empty {
    }
    /**
     * A request to export a state file passed to a 'ExportDeploymentStatefile' call.
     */
    export interface Schema$ExportDeploymentStatefileRequest {
        /**
         * Optional. If this flag is set to true, the exported deployment state file will be the draft state. This will enable the draft file to be validated before copying it over to the working state on unlock.
         */
        draft?: boolean | null;
    }
    /**
     * A request to export preview results.
     */
    export interface Schema$ExportPreviewResultRequest {
    }
    /**
     * A response to `ExportPreviewResult` call. Contains preview results.
     */
    export interface Schema$ExportPreviewResultResponse {
        /**
         * Output only. Signed URLs for accessing the plan files.
         */
        result?: Schema$PreviewResult;
    }
    /**
     * A request to export a state file passed to a 'ExportRevisionStatefile' call.
     */
    export interface Schema$ExportRevisionStatefileRequest {
    }
    /**
     * Represents a textual expression in the Common Expression Language (CEL) syntax. CEL is a C-like expression language. The syntax and semantics of CEL are documented at https://github.com/google/cel-spec. Example (Comparison): title: "Summary size limit" description: "Determines if a summary is less than 100 chars" expression: "document.summary.size() < 100" Example (Equality): title: "Requestor is owner" description: "Determines if requestor is the document owner" expression: "document.owner == request.auth.claims.email" Example (Logic): title: "Public documents" description: "Determine whether the document should be publicly visible" expression: "document.type != 'private' && document.type != 'internal'" Example (Data Manipulation): title: "Notification string" description: "Create a notification string with a timestamp." expression: "'New message received at ' + string(document.create_time)" The exact variables and functions that may be referenced within an expression are determined by the service that evaluates it. See the service documentation for additional information.
     */
    export interface Schema$Expr {
        /**
         * Optional. Description of the expression. This is a longer text which describes the expression, e.g. when hovered over it in a UI.
         */
        description?: string | null;
        /**
         * Textual representation of an expression in Common Expression Language syntax.
         */
        expression?: string | null;
        /**
         * Optional. String indicating the location of the expression for error reporting, e.g. a file name and a position in the file.
         */
        location?: string | null;
        /**
         * Optional. Title for the expression, i.e. a short string describing its purpose. This can be used e.g. in UIs which allow to enter the expression.
         */
        title?: string | null;
    }
    /**
     * A set of files in a Git repository.
     */
    export interface Schema$GitSource {
        /**
         * Optional. Subdirectory inside the repository. Example: 'staging/my-package'
         */
        directory?: string | null;
        /**
         * Optional. Git reference (e.g. branch or tag).
         */
        ref?: string | null;
        /**
         * Optional. Repository URL. Example: 'https://github.com/kubernetes/examples.git'
         */
        repo?: string | null;
    }
    /**
     * A request to import a state file passed to a 'ImportStatefile' call.
     */
    export interface Schema$ImportStatefileRequest {
        /**
         * Required. Lock ID of the lock file to verify that the user who is importing the state file previously locked the Deployment.
         */
        lockId?: string | null;
    }
    export interface Schema$ListDeploymentsResponse {
        /**
         * List of Deployments.
         */
        deployments?: Schema$Deployment[];
        /**
         * Token to be supplied to the next ListDeployments request via `page_token` to obtain the next set of results.
         */
        nextPageToken?: string | null;
        /**
         * Locations that could not be reached.
         */
        unreachable?: string[] | null;
    }
    /**
     * The response message for Locations.ListLocations.
     */
    export interface Schema$ListLocationsResponse {
        /**
         * A list of locations that matches the specified filter in the request.
         */
        locations?: Schema$Location[];
        /**
         * The standard List next-page token.
         */
        nextPageToken?: string | null;
    }
    /**
     * The response message for Operations.ListOperations.
     */
    export interface Schema$ListOperationsResponse {
        /**
         * The standard List next-page token.
         */
        nextPageToken?: string | null;
        /**
         * A list of operations that matches the specified filter in the request.
         */
        operations?: Schema$Operation[];
    }
    /**
     * A response to a `ListPreviews` call. Contains a list of Previews.
     */
    export interface Schema$ListPreviewsResponse {
        /**
         * Token to be supplied to the next ListPreviews request via `page_token` to obtain the next set of results.
         */
        nextPageToken?: string | null;
        /**
         * List of Previews.
         */
        previews?: Schema$Preview[];
        /**
         * Locations that could not be reached.
         */
        unreachable?: string[] | null;
    }
    /**
     * A response to a 'ListResourceChanges' call. Contains a list of ResourceChanges.
     */
    export interface Schema$ListResourceChangesResponse {
        /**
         * A token to request the next page of resources from the 'ListResourceChanges' method. The value of an empty string means that there are no more resources to return.
         */
        nextPageToken?: string | null;
        /**
         * List of ResourceChanges.
         */
        resourceChanges?: Schema$ResourceChange[];
        /**
         * Unreachable resources, if any.
         */
        unreachable?: string[] | null;
    }
    /**
     * A response to a 'ListResourceDrifts' call. Contains a list of ResourceDrifts.
     */
    export interface Schema$ListResourceDriftsResponse {
        /**
         * A token to request the next page of resources from the 'ListResourceDrifts' method. The value of an empty string means that there are no more resources to return.
         */
        nextPageToken?: string | null;
        /**
         * List of ResourceDrifts.
         */
        resourceDrifts?: Schema$ResourceDrift[];
        /**
         * Unreachable resources, if any.
         */
        unreachable?: string[] | null;
    }
    /**
     * A response to a 'ListResources' call. Contains a list of Resources.
     */
    export interface Schema$ListResourcesResponse {
        /**
         * A token to request the next page of resources from the 'ListResources' method. The value of an empty string means that there are no more resources to return.
         */
        nextPageToken?: string | null;
        /**
         * List of Resources.
         */
        resources?: Schema$Resource[];
        /**
         * Locations that could not be reached.
         */
        unreachable?: string[] | null;
    }
    /**
     * A response to a 'ListRevisions' call. Contains a list of Revisions.
     */
    export interface Schema$ListRevisionsResponse {
        /**
         * A token to request the next page of resources from the 'ListRevisions' method. The value of an empty string means that there are no more resources to return.
         */
        nextPageToken?: string | null;
        /**
         * List of Revisions.
         */
        revisions?: Schema$Revision[];
        /**
         * Locations that could not be reached.
         */
        unreachable?: string[] | null;
    }
    /**
     * The response message for the `ListTerraformVersions` method.
     */
    export interface Schema$ListTerraformVersionsResponse {
        /**
         * Token to be supplied to the next ListTerraformVersions request via `page_token` to obtain the next set of results.
         */
        nextPageToken?: string | null;
        /**
         * List of TerraformVersions.
         */
        terraformVersions?: Schema$TerraformVersion[];
        /**
         * Unreachable resources, if any.
         */
        unreachable?: string[] | null;
    }
    /**
     * A resource that represents a Google Cloud location.
     */
    export interface Schema$Location {
        /**
         * The friendly name for this location, typically a nearby city name. For example, "Tokyo".
         */
        displayName?: string | null;
        /**
         * Cross-service attributes for the location. For example {"cloud.googleapis.com/region": "us-east1"\}
         */
        labels?: {
            [key: string]: string;
        } | null;
        /**
         * The canonical id for this location. For example: `"us-east1"`.
         */
        locationId?: string | null;
        /**
         * Service-specific metadata. For example the available capacity at the given location.
         */
        metadata?: {
            [key: string]: any;
        } | null;
        /**
         * Resource name for the location, which may vary between implementations. For example: `"projects/example-project/locations/us-east1"`
         */
        name?: string | null;
    }
    /**
     * A request to lock a deployment passed to a 'LockDeployment' call.
     */
    export interface Schema$LockDeploymentRequest {
    }
    /**
     * Details about the lock which locked the deployment.
     */
    export interface Schema$LockInfo {
        /**
         * Time that the lock was taken.
         */
        createTime?: string | null;
        /**
         * Extra information to store with the lock, provided by the caller.
         */
        info?: string | null;
        /**
         * Unique ID for the lock to be overridden with generation ID in the backend.
         */
        lockId?: string | null;
        /**
         * Terraform operation, provided by the caller.
         */
        operation?: string | null;
        /**
         * Terraform version
         */
        version?: string | null;
        /**
         * user@hostname when available
         */
        who?: string | null;
    }
    /**
     * This resource represents a long-running operation that is the result of a network API call.
     */
    export interface Schema$Operation {
        /**
         * If the value is `false`, it means the operation is still in progress. If `true`, the operation is completed, and either `error` or `response` is available.
         */
        done?: boolean | null;
        /**
         * The error result of the operation in case of failure or cancellation.
         */
        error?: Schema$Status;
        /**
         * Service-specific metadata associated with the operation. It typically contains progress information and common metadata such as create time. Some services might not provide such metadata. Any method that returns a long-running operation should document the metadata type, if any.
         */
        metadata?: {
            [key: string]: any;
        } | null;
        /**
         * The server-assigned name, which is only unique within the same service that originally returns it. If you use the default HTTP mapping, the `name` should be a resource name ending with `operations/{unique_id\}`.
         */
        name?: string | null;
        /**
         * The normal, successful response of the operation. If the original method returns no data on success, such as `Delete`, the response is `google.protobuf.Empty`. If the original method is standard `Get`/`Create`/`Update`, the response should be the resource. For other methods, the response should have the type `XxxResponse`, where `Xxx` is the original method name. For example, if the original method name is `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.
         */
        response?: {
            [key: string]: any;
        } | null;
    }
    /**
     * Represents the metadata of the long-running operation.
     */
    export interface Schema$OperationMetadata {
        /**
         * Output only. API version used to start the operation.
         */
        apiVersion?: string | null;
        /**
         * Output only. Time when the operation was created.
         */
        createTime?: string | null;
        /**
         * Output only. Metadata about the deployment operation state.
         */
        deploymentMetadata?: Schema$DeploymentOperationMetadata;
        /**
         * Output only. Time when the operation finished running.
         */
        endTime?: string | null;
        /**
         * Output only. Metadata about the preview operation state.
         */
        previewMetadata?: Schema$PreviewOperationMetadata;
        /**
         * Output only. Identifies whether the user has requested cancellation of the operation. Operations that have successfully been cancelled have google.longrunning.Operation.error value with a google.rpc.Status.code of `1`, corresponding to `Code.CANCELLED`.
         */
        requestedCancellation?: boolean | null;
        /**
         * Output only. Human-readable status of the operation, if any.
         */
        statusMessage?: string | null;
        /**
         * Output only. Server-defined resource path for the target of the operation.
         */
        target?: string | null;
        /**
         * Output only. Name of the verb executed by the operation.
         */
        verb?: string | null;
    }
    /**
     * An Identity and Access Management (IAM) policy, which specifies access controls for Google Cloud resources. A `Policy` is a collection of `bindings`. A `binding` binds one or more `members`, or principals, to a single `role`. Principals can be user accounts, service accounts, Google groups, and domains (such as G Suite). A `role` is a named list of permissions; each `role` can be an IAM predefined role or a user-created custom role. For some types of Google Cloud resources, a `binding` can also specify a `condition`, which is a logical expression that allows access to a resource only if the expression evaluates to `true`. A condition can add constraints based on attributes of the request, the resource, or both. To learn which resources support conditions in their IAM policies, see the [IAM documentation](https://cloud.google.com/iam/help/conditions/resource-policies). **JSON example:** ``` { "bindings": [ { "role": "roles/resourcemanager.organizationAdmin", "members": [ "user:<EMAIL>", "group:<EMAIL>", "domain:google.com", "serviceAccount:<EMAIL>" ] \}, { "role": "roles/resourcemanager.organizationViewer", "members": [ "user:<EMAIL>" ], "condition": { "title": "expirable access", "description": "Does not grant access after Sep 2020", "expression": "request.time < timestamp('2020-10-01T00:00:00.000Z')", \} \} ], "etag": "BwWWja0YfJA=", "version": 3 \} ``` **YAML example:** ``` bindings: - members: - user:<EMAIL> - group:<EMAIL> - domain:google.com - serviceAccount:<EMAIL> role: roles/resourcemanager.organizationAdmin - members: - user:<EMAIL> role: roles/resourcemanager.organizationViewer condition: title: expirable access description: Does not grant access after Sep 2020 expression: request.time < timestamp('2020-10-01T00:00:00.000Z') etag: BwWWja0YfJA= version: 3 ``` For a description of IAM and its features, see the [IAM documentation](https://cloud.google.com/iam/docs/).
     */
    export interface Schema$Policy {
        /**
         * Specifies cloud audit logging configuration for this policy.
         */
        auditConfigs?: Schema$AuditConfig[];
        /**
         * Associates a list of `members`, or principals, with a `role`. Optionally, may specify a `condition` that determines how and when the `bindings` are applied. Each of the `bindings` must contain at least one principal. The `bindings` in a `Policy` can refer to up to 1,500 principals; up to 250 of these principals can be Google groups. Each occurrence of a principal counts towards these limits. For example, if the `bindings` grant 50 different roles to `user:<EMAIL>`, and not to any other principal, then you can add another 1,450 principals to the `bindings` in the `Policy`.
         */
        bindings?: Schema$Binding[];
        /**
         * `etag` is used for optimistic concurrency control as a way to help prevent simultaneous updates of a policy from overwriting each other. It is strongly suggested that systems make use of the `etag` in the read-modify-write cycle to perform policy updates in order to avoid race conditions: An `etag` is returned in the response to `getIamPolicy`, and systems are expected to put that etag in the request to `setIamPolicy` to ensure that their change will be applied to the same version of the policy. **Important:** If you use IAM Conditions, you must include the `etag` field whenever you call `setIamPolicy`. If you omit this field, then IAM allows you to overwrite a version `3` policy with a version `1` policy, and all of the conditions in the version `3` policy are lost.
         */
        etag?: string | null;
        /**
         * Specifies the format of the policy. Valid values are `0`, `1`, and `3`. Requests that specify an invalid value are rejected. Any operation that affects conditional role bindings must specify version `3`. This requirement applies to the following operations: * Getting a policy that includes a conditional role binding * Adding a conditional role binding to a policy * Changing a conditional role binding in a policy * Removing any role binding, with or without a condition, from a policy that includes conditions **Important:** If you use IAM Conditions, you must include the `etag` field whenever you call `setIamPolicy`. If you omit this field, then IAM allows you to overwrite a version `3` policy with a version `1` policy, and all of the conditions in the version `3` policy are lost. If a policy does not include any conditions, operations on that policy may specify any valid version or leave the field unset. To learn which resources support conditions in their IAM policies, see the [IAM documentation](https://cloud.google.com/iam/help/conditions/resource-policies).
         */
        version?: number | null;
    }
    /**
     * A preview represents a set of actions Infra Manager would perform to move the resources towards the desired state as specified in the configuration.
     */
    export interface Schema$Preview {
        /**
         * Optional. Arbitrary key-value metadata storage e.g. to help client tools identify preview during automation. See https://google.aip.dev/148#annotations for details on format and size limitations.
         */
        annotations?: {
            [key: string]: string;
        } | null;
        /**
         * Optional. User-defined location of Cloud Build logs, artifacts, and in Google Cloud Storage. Format: `gs://{bucket\}/{folder\}` A default bucket will be bootstrapped if the field is not set or empty Default Bucket Format: `gs://--blueprint-config` Constraints: - The bucket needs to be in the same project as the deployment - The path cannot be within the path of `gcs_source` If omitted and deployment resource ref provided has artifacts_gcs_bucket defined, that artifact bucket is used.
         */
        artifactsGcsBucket?: string | null;
        /**
         * Output only. Cloud Build instance UUID associated with this preview.
         */
        build?: string | null;
        /**
         * Output only. Time the preview was created.
         */
        createTime?: string | null;
        /**
         * Optional. Optional deployment reference. If specified, the preview will be performed using the provided deployment's current state and use any relevant fields from the deployment unless explicitly specified in the preview create request.
         */
        deployment?: string | null;
        /**
         * Output only. Code describing any errors that may have occurred.
         */
        errorCode?: string | null;
        /**
         * Output only. Link to tf-error.ndjson file, which contains the full list of the errors encountered during a Terraform preview. Format: `gs://{bucket\}/{object\}`.
         */
        errorLogs?: string | null;
        /**
         * Output only. Additional information regarding the current state.
         */
        errorStatus?: Schema$Status;
        /**
         * Optional. User-defined labels for the preview.
         */
        labels?: {
            [key: string]: string;
        } | null;
        /**
         * Output only. Location of preview logs in `gs://{bucket\}/{object\}` format.
         */
        logs?: string | null;
        /**
         * Identifier. Resource name of the preview. Resource name can be user provided or server generated ID if unspecified. Format: `projects/{project\}/locations/{location\}/previews/{preview\}`
         */
        name?: string | null;
        /**
         * Output only. Artifacts from preview.
         */
        previewArtifacts?: Schema$PreviewArtifacts;
        /**
         * Optional. Current mode of preview.
         */
        previewMode?: string | null;
        /**
         * Required. User-specified Service Account (SA) credentials to be used when previewing resources. Format: `projects/{projectID\}/serviceAccounts/{serviceAccount\}`
         */
        serviceAccount?: string | null;
        /**
         * Output only. Current state of the preview.
         */
        state?: string | null;
        /**
         * The terraform blueprint to preview.
         */
        terraformBlueprint?: Schema$TerraformBlueprint;
        /**
         * Output only. Summary of errors encountered during Terraform preview. It has a size limit of 10, i.e. only top 10 errors will be summarized here.
         */
        tfErrors?: Schema$TerraformError[];
        /**
         * Output only. The current Terraform version set on the preview. It is in the format of "Major.Minor.Patch", for example, "1.3.10".
         */
        tfVersion?: string | null;
        /**
         * Optional. The user-specified Terraform version constraint. Example: "=1.3.10".
         */
        tfVersionConstraint?: string | null;
        /**
         * Optional. The user-specified Worker Pool resource in which the Cloud Build job will execute. Format projects/{project\}/locations/{location\}/workerPools/{workerPoolId\} If this field is unspecified, the default Cloud Build worker pool will be used. If omitted and deployment resource ref provided has worker_pool defined, that worker pool is used.
         */
        workerPool?: string | null;
    }
    /**
     * Artifacts created by preview.
     */
    export interface Schema$PreviewArtifacts {
        /**
         * Output only. Location of artifacts in Google Cloud Storage. Format: `gs://{bucket\}/{object\}`
         */
        artifacts?: string | null;
        /**
         * Output only. Location of a blueprint copy and other content in Google Cloud Storage. Format: `gs://{bucket\}/{object\}`
         */
        content?: string | null;
    }
    /**
     * Ephemeral metadata content describing the state of a preview operation.
     */
    export interface Schema$PreviewOperationMetadata {
        /**
         * Output only. Cloud Build instance UUID associated with this preview.
         */
        build?: string | null;
        /**
         * Output only. Location of preview logs in `gs://{bucket\}/{object\}` format.
         */
        logs?: string | null;
        /**
         * Artifacts from preview.
         */
        previewArtifacts?: Schema$PreviewArtifacts;
        /**
         * The current step the preview operation is running.
         */
        step?: string | null;
    }
    /**
     * Contains a signed Cloud Storage URLs.
     */
    export interface Schema$PreviewResult {
        /**
         * Output only. Plan binary signed URL
         */
        binarySignedUri?: string | null;
        /**
         * Output only. Plan JSON signed URL
         */
        jsonSignedUri?: string | null;
    }
    /**
     * A property change represents a change to a property in the state file.
     */
    export interface Schema$PropertyChange {
        /**
         * Output only. Representations of the object value after the actions.
         */
        after?: any | null;
        /**
         * Output only. The paths of sensitive fields in `after`. Paths are relative to `path`.
         */
        afterSensitivePaths?: string[] | null;
        /**
         * Output only. Representations of the object value before the actions.
         */
        before?: any | null;
        /**
         * Output only. The paths of sensitive fields in `before`. Paths are relative to `path`.
         */
        beforeSensitivePaths?: string[] | null;
        /**
         * Output only. The path of the property change.
         */
        path?: string | null;
    }
    /**
     * A property drift represents a drift to a property in the state file.
     */
    export interface Schema$PropertyDrift {
        /**
         * Output only. Representations of the object value after the actions.
         */
        after?: any | null;
        /**
         * Output only. The paths of sensitive fields in `after`. Paths are relative to `path`.
         */
        afterSensitivePaths?: string[] | null;
        /**
         * Output only. Representations of the object value before the actions.
         */
        before?: any | null;
        /**
         * Output only. The paths of sensitive fields in `before`. Paths are relative to `path`.
         */
        beforeSensitivePaths?: string[] | null;
        /**
         * Output only. The path of the property drift.
         */
        path?: string | null;
    }
    /**
     * Resource represents a Google Cloud Platform resource actuated by IM. Resources are child resources of Revisions.
     */
    export interface Schema$Resource {
        /**
         * Output only. Map of Cloud Asset Inventory (CAI) type to CAI info (e.g. CAI ID). CAI type format follows https://cloud.google.com/asset-inventory/docs/supported-asset-types
         */
        caiAssets?: {
            [key: string]: Schema$ResourceCAIInfo;
        } | null;
        /**
         * Output only. Intent of the resource.
         */
        intent?: string | null;
        /**
         * Output only. Resource name. Format: `projects/{project\}/locations/{location\}/deployments/{deployment\}/revisions/{revision\}/resources/{resource\}`
         */
        name?: string | null;
        /**
         * Output only. Current state of the resource.
         */
        state?: string | null;
        /**
         * Output only. Terraform-specific info if this resource was created using Terraform.
         */
        terraformInfo?: Schema$ResourceTerraformInfo;
    }
    /**
     * CAI info of a Resource.
     */
    export interface Schema$ResourceCAIInfo {
        /**
         * CAI resource name in the format following https://cloud.google.com/apis/design/resource_names#full_resource_name
         */
        fullResourceName?: string | null;
    }
    /**
     * A resource change represents a change to a resource in the state file.
     */
    export interface Schema$ResourceChange {
        /**
         * Output only. The intent of the resource change.
         */
        intent?: string | null;
        /**
         * Identifier. The name of the resource change. Format: 'projects/{project_id\}/locations/{location\}/previews/{preview\}/resourceChanges/{resource_change\}'.
         */
        name?: string | null;
        /**
         * Output only. The property changes of the resource change.
         */
        propertyChanges?: Schema$PropertyChange[];
        /**
         * Output only. Terraform info of the resource change.
         */
        terraformInfo?: Schema$ResourceChangeTerraformInfo;
    }
    /**
     * Terraform info of a ResourceChange.
     */
    export interface Schema$ResourceChangeTerraformInfo {
        /**
         * Output only. TF resource actions.
         */
        actions?: string[] | null;
        /**
         * Output only. TF resource address that uniquely identifies the resource.
         */
        address?: string | null;
        /**
         * Output only. TF resource provider.
         */
        provider?: string | null;
        /**
         * Output only. TF resource name.
         */
        resourceName?: string | null;
        /**
         * Output only. TF resource type.
         */
        type?: string | null;
    }
    /**
     * A resource drift represents a drift to a resource in the state file.
     */
    export interface Schema$ResourceDrift {
        /**
         * Identifier. The name of the resource drift. Format: 'projects/{project_id\}/locations/{location\}/previews/{preview\}/resourceDrifts/{resource_drift\}'.
         */
        name?: string | null;
        /**
         * Output only. The property drifts of the resource drift.
         */
        propertyDrifts?: Schema$PropertyDrift[];
        /**
         * Output only. Terraform info of the resource drift.
         */
        terraformInfo?: Schema$ResourceDriftTerraformInfo;
    }
    /**
     * Terraform info of a ResourceChange.
     */
    export interface Schema$ResourceDriftTerraformInfo {
        /**
         * Output only. The address of the drifted resource.
         */
        address?: string | null;
        /**
         * Output only. The provider of the drifted resource.
         */
        provider?: string | null;
        /**
         * Output only. TF resource name.
         */
        resourceName?: string | null;
        /**
         * Output only. The type of the drifted resource.
         */
        type?: string | null;
    }
    /**
     * Terraform info of a Resource.
     */
    export interface Schema$ResourceTerraformInfo {
        /**
         * TF resource address that uniquely identifies this resource within this deployment.
         */
        address?: string | null;
        /**
         * ID attribute of the TF resource
         */
        id?: string | null;
        /**
         * TF resource type
         */
        type?: string | null;
    }
    /**
     * A child resource of a Deployment generated by a 'CreateDeployment' or 'UpdateDeployment' call. Each Revision contains metadata pertaining to a snapshot of a particular Deployment.
     */
    export interface Schema$Revision {
        /**
         * Output only. The action which created this revision
         */
        action?: string | null;
        /**
         * Output only. Outputs and artifacts from applying a deployment.
         */
        applyResults?: Schema$ApplyResults;
        /**
         * Output only. Cloud Build instance UUID associated with this revision.
         */
        build?: string | null;
        /**
         * Output only. Time when the revision was created.
         */
        createTime?: string | null;
        /**
         * Output only. Code describing any errors that may have occurred.
         */
        errorCode?: string | null;
        /**
         * Output only. Location of Terraform error logs in Google Cloud Storage. Format: `gs://{bucket\}/{object\}`.
         */
        errorLogs?: string | null;
        /**
         * Output only. By default, Infra Manager will return a failure when Terraform encounters a 409 code (resource conflict error) during actuation. If this flag is set to true, Infra Manager will instead attempt to automatically import the resource into the Terraform state (for supported resource types) and continue actuation. Not all resource types are supported, refer to documentation.
         */
        importExistingResources?: boolean | null;
        /**
         * Output only. Location of Revision operation logs in `gs://{bucket\}/{object\}` format.
         */
        logs?: string | null;
        /**
         * Revision name. Format: `projects/{project\}/locations/{location\}/deployments/{deployment\}/ revisions/{revision\}`
         */
        name?: string | null;
        /**
         * Optional. Input to control quota checks for resources in terraform configuration files. There are limited resources on which quota validation applies.
         */
        quotaValidation?: string | null;
        /**
         * Output only. Cloud Storage path containing quota validation results. This field is set when a user sets Deployment.quota_validation field to ENABLED or ENFORCED. Format: `gs://{bucket\}/{object\}`.
         */
        quotaValidationResults?: string | null;
        /**
         * Output only. User-specified Service Account (SA) to be used as credential to manage resources. Format: `projects/{projectID\}/serviceAccounts/{serviceAccount\}`
         */
        serviceAccount?: string | null;
        /**
         * Output only. Current state of the revision.
         */
        state?: string | null;
        /**
         * Output only. Additional info regarding the current state.
         */
        stateDetail?: string | null;
        /**
         * Output only. A blueprint described using Terraform's HashiCorp Configuration Language as a root module.
         */
        terraformBlueprint?: Schema$TerraformBlueprint;
        /**
         * Output only. Errors encountered when creating or updating this deployment. Errors are truncated to 10 entries, see `delete_results` and `error_logs` for full details.
         */
        tfErrors?: Schema$TerraformError[];
        /**
         * Output only. The version of Terraform used to create the Revision. It is in the format of "Major.Minor.Patch", for example, "1.3.10".
         */
        tfVersion?: string | null;
        /**
         * Output only. The user-specified Terraform version constraint. Example: "=1.3.10".
         */
        tfVersionConstraint?: string | null;
        /**
         * Output only. Time when the revision was last modified.
         */
        updateTime?: string | null;
        /**
         * Output only. The user-specified Cloud Build worker pool resource in which the Cloud Build job will execute. Format: `projects/{project\}/locations/{location\}/workerPools/{workerPoolId\}`. If this field is unspecified, the default Cloud Build worker pool will be used.
         */
        workerPool?: string | null;
    }
    /**
     * Request message for `SetIamPolicy` method.
     */
    export interface Schema$SetIamPolicyRequest {
        /**
         * REQUIRED: The complete policy to be applied to the `resource`. The size of the policy is limited to a few 10s of KB. An empty policy is a valid policy but certain Google Cloud services (such as Projects) might reject them.
         */
        policy?: Schema$Policy;
        /**
         * OPTIONAL: A FieldMask specifying which fields of the policy to modify. Only the fields in the mask will be modified. If no mask is provided, the following default mask is used: `paths: "bindings, etag"`
         */
        updateMask?: string | null;
    }
    /**
     * Contains info about a Terraform state file
     */
    export interface Schema$Statefile {
        /**
         * Output only. Cloud Storage signed URI used for downloading or uploading the state file.
         */
        signedUri?: string | null;
    }
    /**
     * The `Status` type defines a logical error model that is suitable for different programming environments, including REST APIs and RPC APIs. It is used by [gRPC](https://github.com/grpc). Each `Status` message contains three pieces of data: error code, error message, and error details. You can find out more about this error model and how to work with it in the [API Design Guide](https://cloud.google.com/apis/design/errors).
     */
    export interface Schema$Status {
        /**
         * The status code, which should be an enum value of google.rpc.Code.
         */
        code?: number | null;
        /**
         * A list of messages that carry the error details. There is a common set of message types for APIs to use.
         */
        details?: Array<{
            [key: string]: any;
        }> | null;
        /**
         * A developer-facing error message, which should be in English. Any user-facing error message should be localized and sent in the google.rpc.Status.details field, or localized by the client.
         */
        message?: string | null;
    }
    /**
     * TerraformBlueprint describes the source of a Terraform root module which describes the resources and configs to be deployed.
     */
    export interface Schema$TerraformBlueprint {
        /**
         * URI of an object in Google Cloud Storage. Format: `gs://{bucket\}/{object\}` URI may also specify an object version for zipped objects. Format: `gs://{bucket\}/{object\}#{version\}`
         */
        gcsSource?: string | null;
        /**
         * URI of a public Git repo.
         */
        gitSource?: Schema$GitSource;
        /**
         * Optional. Input variable values for the Terraform blueprint.
         */
        inputValues?: {
            [key: string]: Schema$TerraformVariable;
        } | null;
    }
    /**
     * Errors encountered during actuation using Terraform
     */
    export interface Schema$TerraformError {
        /**
         * Output only. Original error response from underlying Google API, if available.
         */
        error?: Schema$Status;
        /**
         * A human-readable error description.
         */
        errorDescription?: string | null;
        /**
         * HTTP response code returned from Google Cloud Platform APIs when Terraform fails to provision the resource. If unset or 0, no HTTP response code was returned by Terraform.
         */
        httpResponseCode?: number | null;
        /**
         * Address of the resource associated with the error, e.g. `google_compute_network.vpc_network`.
         */
        resourceAddress?: string | null;
    }
    /**
     * Describes a Terraform output.
     */
    export interface Schema$TerraformOutput {
        /**
         * Identifies whether Terraform has set this output as a potential sensitive value.
         */
        sensitive?: boolean | null;
        /**
         * Value of output.
         */
        value?: any | null;
    }
    /**
     * A Terraform input variable.
     */
    export interface Schema$TerraformVariable {
        /**
         * Optional. Input variable value.
         */
        inputValue?: any | null;
    }
    /**
     * A TerraformVersion represents the support state the corresponding Terraform version.
     */
    export interface Schema$TerraformVersion {
        /**
         * Output only. When the version is deprecated.
         */
        deprecateTime?: string | null;
        /**
         * Identifier. The version name is in the format: 'projects/{project_id\}/locations/{location\}/terraformVersions/{terraform_version\}'.
         */
        name?: string | null;
        /**
         * Output only. When the version is obsolete.
         */
        obsoleteTime?: string | null;
        /**
         * Output only. The state of the version, ACTIVE, DEPRECATED or OBSOLETE.
         */
        state?: string | null;
        /**
         * Output only. When the version is supported.
         */
        supportTime?: string | null;
    }
    /**
     * Request message for `TestIamPermissions` method.
     */
    export interface Schema$TestIamPermissionsRequest {
        /**
         * The set of permissions to check for the `resource`. Permissions with wildcards (such as `*` or `storage.*`) are not allowed. For more information see [IAM Overview](https://cloud.google.com/iam/docs/overview#permissions).
         */
        permissions?: string[] | null;
    }
    /**
     * Response message for `TestIamPermissions` method.
     */
    export interface Schema$TestIamPermissionsResponse {
        /**
         * A subset of `TestPermissionsRequest.permissions` that the caller is allowed.
         */
        permissions?: string[] | null;
    }
    /**
     * A request to unlock a state file passed to a 'UnlockDeployment' call.
     */
    export interface Schema$UnlockDeploymentRequest {
        /**
         * Required. Lock ID of the lock file to be unlocked.
         */
        lockId?: string | null;
    }
    export class Resource$Projects {
        context: APIRequestContext;
        locations: Resource$Projects$Locations;
        constructor(context: APIRequestContext);
    }
    export class Resource$Projects$Locations {
        context: APIRequestContext;
        deployments: Resource$Projects$Locations$Deployments;
        operations: Resource$Projects$Locations$Operations;
        previews: Resource$Projects$Locations$Previews;
        terraformVersions: Resource$Projects$Locations$Terraformversions;
        constructor(context: APIRequestContext);
        /**
         * Gets information about a location.
         * @example
         * ```js
         * // Before running the sample:
         * // - Enable the API at:
         * //   https://console.developers.google.com/apis/api/config.googleapis.com
         * // - Login into gcloud by running:
         * //   ```sh
         * //   $ gcloud auth application-default login
         * //   ```
         * // - Install the npm module by running:
         * //   ```sh
         * //   $ npm install googleapis
         * //   ```
         *
         * const {google} = require('googleapis');
         * const config = google.config('v1');
         *
         * async function main() {
         *   const auth = new google.auth.GoogleAuth({
         *     // Scopes can be specified either as an array or as a single, space-delimited string.
         *     scopes: ['https://www.googleapis.com/auth/cloud-platform'],
         *   });
         *
         *   // Acquire an auth client, and bind it to all future calls
         *   const authClient = await auth.getClient();
         *   google.options({auth: authClient});
         *
         *   // Do the magic
         *   const res = await config.projects.locations.get({
         *     // Resource name for the location.
         *     name: 'projects/my-project/locations/my-location',
         *   });
         *   console.log(res.data);
         *
         *   // Example response
         *   // {
         *   //   "displayName": "my_displayName",
         *   //   "labels": {},
         *   //   "locationId": "my_locationId",
         *   //   "metadata": {},
         *   //   "name": "my_name"
         *   // }
         * }
         *
         * main().catch(e => {
         *   console.error(e);
         *   throw e;
         * });
         *
         * ```
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        get(params: Params$Resource$Projects$Locations$Get, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        get(params?: Params$Resource$Projects$Locations$Get, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$Location>>;
        get(params: Params$Resource$Projects$Locations$Get, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        get(params: Params$Resource$Projects$Locations$Get, options: MethodOptions | BodyResponseCallback<Schema$Location>, callback: BodyResponseCallback<Schema$Location>): void;
        get(params: Params$Resource$Projects$Locations$Get, callback: BodyResponseCallback<Schema$Location>): void;
        get(callback: BodyResponseCallback<Schema$Location>): void;
        /**
         * Lists information about the supported locations for this service.
         * @example
         * ```js
         * // Before running the sample:
         * // - Enable the API at:
         * //   https://console.developers.google.com/apis/api/config.googleapis.com
         * // - Login into gcloud by running:
         * //   ```sh
         * //   $ gcloud auth application-default login
         * //   ```
         * // - Install the npm module by running:
         * //   ```sh
         * //   $ npm install googleapis
         * //   ```
         *
         * const {google} = require('googleapis');
         * const config = google.config('v1');
         *
         * async function main() {
         *   const auth = new google.auth.GoogleAuth({
         *     // Scopes can be specified either as an array or as a single, space-delimited string.
         *     scopes: ['https://www.googleapis.com/auth/cloud-platform'],
         *   });
         *
         *   // Acquire an auth client, and bind it to all future calls
         *   const authClient = await auth.getClient();
         *   google.options({auth: authClient});
         *
         *   // Do the magic
         *   const res = await config.projects.locations.list({
         *     // Optional. A list of extra location types that should be used as conditions for controlling the visibility of the locations.
         *     extraLocationTypes: 'placeholder-value',
         *     // A filter to narrow down results to a preferred subset. The filtering language accepts strings like `"displayName=tokyo"`, and is documented in more detail in [AIP-160](https://google.aip.dev/160).
         *     filter: 'placeholder-value',
         *     // The resource that owns the locations collection, if applicable.
         *     name: 'projects/my-project',
         *     // The maximum number of results to return. If not set, the service selects a default.
         *     pageSize: 'placeholder-value',
         *     // A page token received from the `next_page_token` field in the response. Send that page token to receive the subsequent page.
         *     pageToken: 'placeholder-value',
         *   });
         *   console.log(res.data);
         *
         *   // Example response
         *   // {
         *   //   "locations": [],
         *   //   "nextPageToken": "my_nextPageToken"
         *   // }
         * }
         *
         * main().catch(e => {
         *   console.error(e);
         *   throw e;
         * });
         *
         * ```
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        list(params: Params$Resource$Projects$Locations$List, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        list(params?: Params$Resource$Projects$Locations$List, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$ListLocationsResponse>>;
        list(params: Params$Resource$Projects$Locations$List, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        list(params: Params$Resource$Projects$Locations$List, options: MethodOptions | BodyResponseCallback<Schema$ListLocationsResponse>, callback: BodyResponseCallback<Schema$ListLocationsResponse>): void;
        list(params: Params$Resource$Projects$Locations$List, callback: BodyResponseCallback<Schema$ListLocationsResponse>): void;
        list(callback: BodyResponseCallback<Schema$ListLocationsResponse>): void;
    }
    export interface Params$Resource$Projects$Locations$Get extends StandardParameters {
        /**
         * Resource name for the location.
         */
        name?: string;
    }
    export interface Params$Resource$Projects$Locations$List extends StandardParameters {
        /**
         * Optional. A list of extra location types that should be used as conditions for controlling the visibility of the locations.
         */
        extraLocationTypes?: string[];
        /**
         * A filter to narrow down results to a preferred subset. The filtering language accepts strings like `"displayName=tokyo"`, and is documented in more detail in [AIP-160](https://google.aip.dev/160).
         */
        filter?: string;
        /**
         * The resource that owns the locations collection, if applicable.
         */
        name?: string;
        /**
         * The maximum number of results to return. If not set, the service selects a default.
         */
        pageSize?: number;
        /**
         * A page token received from the `next_page_token` field in the response. Send that page token to receive the subsequent page.
         */
        pageToken?: string;
    }
    export class Resource$Projects$Locations$Deployments {
        context: APIRequestContext;
        revisions: Resource$Projects$Locations$Deployments$Revisions;
        constructor(context: APIRequestContext);
        /**
         * Creates a Deployment.
         * @example
         * ```js
         * // Before running the sample:
         * // - Enable the API at:
         * //   https://console.developers.google.com/apis/api/config.googleapis.com
         * // - Login into gcloud by running:
         * //   ```sh
         * //   $ gcloud auth application-default login
         * //   ```
         * // - Install the npm module by running:
         * //   ```sh
         * //   $ npm install googleapis
         * //   ```
         *
         * const {google} = require('googleapis');
         * const config = google.config('v1');
         *
         * async function main() {
         *   const auth = new google.auth.GoogleAuth({
         *     // Scopes can be specified either as an array or as a single, space-delimited string.
         *     scopes: ['https://www.googleapis.com/auth/cloud-platform'],
         *   });
         *
         *   // Acquire an auth client, and bind it to all future calls
         *   const authClient = await auth.getClient();
         *   google.options({auth: authClient});
         *
         *   // Do the magic
         *   const res = await config.projects.locations.deployments.create({
         *     // Required. The Deployment ID.
         *     deploymentId: 'placeholder-value',
         *     // Required. The parent in whose context the Deployment is created. The parent value is in the format: 'projects/{project_id\}/locations/{location\}'.
         *     parent: 'projects/my-project/locations/my-location',
         *     // Optional. An optional request ID to identify requests. Specify a unique request ID so that if you must retry your request, the server will know to ignore the request if it has already been completed. The server will guarantee that for at least 60 minutes since the first request. For example, consider a situation where you make an initial request and the request times out. If you make the request again with the same request ID, the server can check if original operation with the same request ID was received, and if so, will ignore the second request. This prevents clients from accidentally creating duplicate commitments. The request ID must be a valid UUID with the exception that zero UUID is not supported (00000000-0000-0000-0000-000000000000).
         *     requestId: 'placeholder-value',
         *
         *     // Request body metadata
         *     requestBody: {
         *       // request body parameters
         *       // {
         *       //   "annotations": {},
         *       //   "artifactsGcsBucket": "my_artifactsGcsBucket",
         *       //   "createTime": "my_createTime",
         *       //   "deleteBuild": "my_deleteBuild",
         *       //   "deleteLogs": "my_deleteLogs",
         *       //   "deleteResults": {},
         *       //   "errorCode": "my_errorCode",
         *       //   "errorLogs": "my_errorLogs",
         *       //   "importExistingResources": false,
         *       //   "labels": {},
         *       //   "latestRevision": "my_latestRevision",
         *       //   "lockState": "my_lockState",
         *       //   "name": "my_name",
         *       //   "quotaValidation": "my_quotaValidation",
         *       //   "serviceAccount": "my_serviceAccount",
         *       //   "state": "my_state",
         *       //   "stateDetail": "my_stateDetail",
         *       //   "terraformBlueprint": {},
         *       //   "tfErrors": [],
         *       //   "tfVersion": "my_tfVersion",
         *       //   "tfVersionConstraint": "my_tfVersionConstraint",
         *       //   "updateTime": "my_updateTime",
         *       //   "workerPool": "my_workerPool"
         *       // }
         *     },
         *   });
         *   console.log(res.data);
         *
         *   // Example response
         *   // {
         *   //   "done": false,
         *   //   "error": {},
         *   //   "metadata": {},
         *   //   "name": "my_name",
         *   //   "response": {}
         *   // }
         * }
         *
         * main().catch(e => {
         *   console.error(e);
         *   throw e;
         * });
         *
         * ```
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        create(params: Params$Resource$Projects$Locations$Deployments$Create, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        create(params?: Params$Resource$Projects$Locations$Deployments$Create, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$Operation>>;
        create(params: Params$Resource$Projects$Locations$Deployments$Create, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        create(params: Params$Resource$Projects$Locations$Deployments$Create, options: MethodOptions | BodyResponseCallback<Schema$Operation>, callback: BodyResponseCallback<Schema$Operation>): void;
        create(params: Params$Resource$Projects$Locations$Deployments$Create, callback: BodyResponseCallback<Schema$Operation>): void;
        create(callback: BodyResponseCallback<Schema$Operation>): void;
        /**
         * Deletes a Deployment.
         * @example
         * ```js
         * // Before running the sample:
         * // - Enable the API at:
         * //   https://console.developers.google.com/apis/api/config.googleapis.com
         * // - Login into gcloud by running:
         * //   ```sh
         * //   $ gcloud auth application-default login
         * //   ```
         * // - Install the npm module by running:
         * //   ```sh
         * //   $ npm install googleapis
         * //   ```
         *
         * const {google} = require('googleapis');
         * const config = google.config('v1');
         *
         * async function main() {
         *   const auth = new google.auth.GoogleAuth({
         *     // Scopes can be specified either as an array or as a single, space-delimited string.
         *     scopes: ['https://www.googleapis.com/auth/cloud-platform'],
         *   });
         *
         *   // Acquire an auth client, and bind it to all future calls
         *   const authClient = await auth.getClient();
         *   google.options({auth: authClient});
         *
         *   // Do the magic
         *   const res = await config.projects.locations.deployments.delete({
         *     // Optional. Policy on how resources actuated by the deployment should be deleted. If unspecified, the default behavior is to delete the underlying resources.
         *     deletePolicy: 'placeholder-value',
         *     // Optional. If set to true, any revisions for this deployment will also be deleted. (Otherwise, the request will only work if the deployment has no revisions.)
         *     force: 'placeholder-value',
         *     // Required. The name of the Deployment in the format: 'projects/{project_id\}/locations/{location\}/deployments/{deployment\}'.
         *     name: 'projects/my-project/locations/my-location/deployments/my-deployment',
         *     // Optional. An optional request ID to identify requests. Specify a unique request ID so that if you must retry your request, the server will know to ignore the request if it has already been completed. The server will guarantee that for at least 60 minutes after the first request. For example, consider a situation where you make an initial request and the request times out. If you make the request again with the same request ID, the server can check if original operation with the same request ID was received, and if so, will ignore the second request. This prevents clients from accidentally creating duplicate commitments. The request ID must be a valid UUID with the exception that zero UUID is not supported (00000000-0000-0000-0000-000000000000).
         *     requestId: 'placeholder-value',
         *   });
         *   console.log(res.data);
         *
         *   // Example response
         *   // {
         *   //   "done": false,
         *   //   "error": {},
         *   //   "metadata": {},
         *   //   "name": "my_name",
         *   //   "response": {}
         *   // }
         * }
         *
         * main().catch(e => {
         *   console.error(e);
         *   throw e;
         * });
         *
         * ```
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        delete(params: Params$Resource$Projects$Locations$Deployments$Delete, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        delete(params?: Params$Resource$Projects$Locations$Deployments$Delete, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$Operation>>;
        delete(params: Params$Resource$Projects$Locations$Deployments$Delete, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        delete(params: Params$Resource$Projects$Locations$Deployments$Delete, options: MethodOptions | BodyResponseCallback<Schema$Operation>, callback: BodyResponseCallback<Schema$Operation>): void;
        delete(params: Params$Resource$Projects$Locations$Deployments$Delete, callback: BodyResponseCallback<Schema$Operation>): void;
        delete(callback: BodyResponseCallback<Schema$Operation>): void;
        /**
         * Deletes Terraform state file in a given deployment.
         * @example
         * ```js
         * // Before running the sample:
         * // - Enable the API at:
         * //   https://console.developers.google.com/apis/api/config.googleapis.com
         * // - Login into gcloud by running:
         * //   ```sh
         * //   $ gcloud auth application-default login
         * //   ```
         * // - Install the npm module by running:
         * //   ```sh
         * //   $ npm install googleapis
         * //   ```
         *
         * const {google} = require('googleapis');
         * const config = google.config('v1');
         *
         * async function main() {
         *   const auth = new google.auth.GoogleAuth({
         *     // Scopes can be specified either as an array or as a single, space-delimited string.
         *     scopes: ['https://www.googleapis.com/auth/cloud-platform'],
         *   });
         *
         *   // Acquire an auth client, and bind it to all future calls
         *   const authClient = await auth.getClient();
         *   google.options({auth: authClient});
         *
         *   // Do the magic
         *   const res = await config.projects.locations.deployments.deleteState({
         *     // Required. The name of the deployment in the format: 'projects/{project_id\}/locations/{location\}/deployments/{deployment\}'.
         *     name: 'projects/my-project/locations/my-location/deployments/my-deployment',
         *
         *     // Request body metadata
         *     requestBody: {
         *       // request body parameters
         *       // {
         *       //   "lockId": "my_lockId"
         *       // }
         *     },
         *   });
         *   console.log(res.data);
         *
         *   // Example response
         *   // {}
         * }
         *
         * main().catch(e => {
         *   console.error(e);
         *   throw e;
         * });
         *
         * ```
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        deleteState(params: Params$Resource$Projects$Locations$Deployments$Deletestate, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        deleteState(params?: Params$Resource$Projects$Locations$Deployments$Deletestate, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$Empty>>;
        deleteState(params: Params$Resource$Projects$Locations$Deployments$Deletestate, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        deleteState(params: Params$Resource$Projects$Locations$Deployments$Deletestate, options: MethodOptions | BodyResponseCallback<Schema$Empty>, callback: BodyResponseCallback<Schema$Empty>): void;
        deleteState(params: Params$Resource$Projects$Locations$Deployments$Deletestate, callback: BodyResponseCallback<Schema$Empty>): void;
        deleteState(callback: BodyResponseCallback<Schema$Empty>): void;
        /**
         * Exports the lock info on a locked deployment.
         * @example
         * ```js
         * // Before running the sample:
         * // - Enable the API at:
         * //   https://console.developers.google.com/apis/api/config.googleapis.com
         * // - Login into gcloud by running:
         * //   ```sh
         * //   $ gcloud auth application-default login
         * //   ```
         * // - Install the npm module by running:
         * //   ```sh
         * //   $ npm install googleapis
         * //   ```
         *
         * const {google} = require('googleapis');
         * const config = google.config('v1');
         *
         * async function main() {
         *   const auth = new google.auth.GoogleAuth({
         *     // Scopes can be specified either as an array or as a single, space-delimited string.
         *     scopes: ['https://www.googleapis.com/auth/cloud-platform'],
         *   });
         *
         *   // Acquire an auth client, and bind it to all future calls
         *   const authClient = await auth.getClient();
         *   google.options({auth: authClient});
         *
         *   // Do the magic
         *   const res = await config.projects.locations.deployments.exportLock({
         *     // Required. The name of the deployment in the format: 'projects/{project_id\}/locations/{location\}/deployments/{deployment\}'.
         *     name: 'projects/my-project/locations/my-location/deployments/my-deployment',
         *   });
         *   console.log(res.data);
         *
         *   // Example response
         *   // {
         *   //   "createTime": "my_createTime",
         *   //   "info": "my_info",
         *   //   "lockId": "my_lockId",
         *   //   "operation": "my_operation",
         *   //   "version": "my_version",
         *   //   "who": "my_who"
         *   // }
         * }
         *
         * main().catch(e => {
         *   console.error(e);
         *   throw e;
         * });
         *
         * ```
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        exportLock(params: Params$Resource$Projects$Locations$Deployments$Exportlock, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        exportLock(params?: Params$Resource$Projects$Locations$Deployments$Exportlock, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$LockInfo>>;
        exportLock(params: Params$Resource$Projects$Locations$Deployments$Exportlock, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        exportLock(params: Params$Resource$Projects$Locations$Deployments$Exportlock, options: MethodOptions | BodyResponseCallback<Schema$LockInfo>, callback: BodyResponseCallback<Schema$LockInfo>): void;
        exportLock(params: Params$Resource$Projects$Locations$Deployments$Exportlock, callback: BodyResponseCallback<Schema$LockInfo>): void;
        exportLock(callback: BodyResponseCallback<Schema$LockInfo>): void;
        /**
         * Exports Terraform state file from a given deployment.
         * @example
         * ```js
         * // Before running the sample:
         * // - Enable the API at:
         * //   https://console.developers.google.com/apis/api/config.googleapis.com
         * // - Login into gcloud by running:
         * //   ```sh
         * //   $ gcloud auth application-default login
         * //   ```
         * // - Install the npm module by running:
         * //   ```sh
         * //   $ npm install googleapis
         * //   ```
         *
         * const {google} = require('googleapis');
         * const config = google.config('v1');
         *
         * async function main() {
         *   const auth = new google.auth.GoogleAuth({
         *     // Scopes can be specified either as an array or as a single, space-delimited string.
         *     scopes: ['https://www.googleapis.com/auth/cloud-platform'],
         *   });
         *
         *   // Acquire an auth client, and bind it to all future calls
         *   const authClient = await auth.getClient();
         *   google.options({auth: authClient});
         *
         *   // Do the magic
         *   const res = await config.projects.locations.deployments.exportState({
         *     // Required. The parent in whose context the statefile is listed. The parent value is in the format: 'projects/{project_id\}/locations/{location\}/deployments/{deployment\}'.
         *     parent:
         *       'projects/my-project/locations/my-location/deployments/my-deployment',
         *
         *     // Request body metadata
         *     requestBody: {
         *       // request body parameters
         *       // {
         *       //   "draft": false
         *       // }
         *     },
         *   });
         *   console.log(res.data);
         *
         *   // Example response
         *   // {
         *   //   "signedUri": "my_signedUri"
         *   // }
         * }
         *
         * main().catch(e => {
         *   console.error(e);
         *   throw e;
         * });
         *
         * ```
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        exportState(params: Params$Resource$Projects$Locations$Deployments$Exportstate, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        exportState(params?: Params$Resource$Projects$Locations$Deployments$Exportstate, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$Statefile>>;
        exportState(params: Params$Resource$Projects$Locations$Deployments$Exportstate, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        exportState(params: Params$Resource$Projects$Locations$Deployments$Exportstate, options: MethodOptions | BodyResponseCallback<Schema$Statefile>, callback: BodyResponseCallback<Schema$Statefile>): void;
        exportState(params: Params$Resource$Projects$Locations$Deployments$Exportstate, callback: BodyResponseCallback<Schema$Statefile>): void;
        exportState(callback: BodyResponseCallback<Schema$Statefile>): void;
        /**
         * Gets details about a Deployment.
         * @example
         * ```js
         * // Before running the sample:
         * // - Enable the API at:
         * //   https://console.developers.google.com/apis/api/config.googleapis.com
         * // - Login into gcloud by running:
         * //   ```sh
         * //   $ gcloud auth application-default login
         * //   ```
         * // - Install the npm module by running:
         * //   ```sh
         * //   $ npm install googleapis
         * //   ```
         *
         * const {google} = require('googleapis');
         * const config = google.config('v1');
         *
         * async function main() {
         *   const auth = new google.auth.GoogleAuth({
         *     // Scopes can be specified either as an array or as a single, space-delimited string.
         *     scopes: ['https://www.googleapis.com/auth/cloud-platform'],
         *   });
         *
         *   // Acquire an auth client, and bind it to all future calls
         *   const authClient = await auth.getClient();
         *   google.options({auth: authClient});
         *
         *   // Do the magic
         *   const res = await config.projects.locations.deployments.get({
         *     // Required. The name of the deployment. Format: 'projects/{project_id\}/locations/{location\}/deployments/{deployment\}'.
         *     name: 'projects/my-project/locations/my-location/deployments/my-deployment',
         *   });
         *   console.log(res.data);
         *
         *   // Example response
         *   // {
         *   //   "annotations": {},
         *   //   "artifactsGcsBucket": "my_artifactsGcsBucket",
         *   //   "createTime": "my_createTime",
         *   //   "deleteBuild": "my_deleteBuild",
         *   //   "deleteLogs": "my_deleteLogs",
         *   //   "deleteResults": {},
         *   //   "errorCode": "my_errorCode",
         *   //   "errorLogs": "my_errorLogs",
         *   //   "importExistingResources": false,
         *   //   "labels": {},
         *   //   "latestRevision": "my_latestRevision",
         *   //   "lockState": "my_lockState",
         *   //   "name": "my_name",
         *   //   "quotaValidation": "my_quotaValidation",
         *   //   "serviceAccount": "my_serviceAccount",
         *   //   "state": "my_state",
         *   //   "stateDetail": "my_stateDetail",
         *   //   "terraformBlueprint": {},
         *   //   "tfErrors": [],
         *   //   "tfVersion": "my_tfVersion",
         *   //   "tfVersionConstraint": "my_tfVersionConstraint",
         *   //   "updateTime": "my_updateTime",
         *   //   "workerPool": "my_workerPool"
         *   // }
         * }
         *
         * main().catch(e => {
         *   console.error(e);
         *   throw e;
         * });
         *
         * ```
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        get(params: Params$Resource$Projects$Locations$Deployments$Get, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        get(params?: Params$Resource$Projects$Locations$Deployments$Get, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$Deployment>>;
        get(params: Params$Resource$Projects$Locations$Deployments$Get, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        get(params: Params$Resource$Projects$Locations$Deployments$Get, options: MethodOptions | BodyResponseCallback<Schema$Deployment>, callback: BodyResponseCallback<Schema$Deployment>): void;
        get(params: Params$Resource$Projects$Locations$Deployments$Get, callback: BodyResponseCallback<Schema$Deployment>): void;
        get(callback: BodyResponseCallback<Schema$Deployment>): void;
        /**
         * Gets the access control policy for a resource. Returns an empty policy if the resource exists and does not have a policy set.
         * @example
         * ```js
         * // Before running the sample:
         * // - Enable the API at:
         * //   https://console.developers.google.com/apis/api/config.googleapis.com
         * // - Login into gcloud by running:
         * //   ```sh
         * //   $ gcloud auth application-default login
         * //   ```
         * // - Install the npm module by running:
         * //   ```sh
         * //   $ npm install googleapis
         * //   ```
         *
         * const {google} = require('googleapis');
         * const config = google.config('v1');
         *
         * async function main() {
         *   const auth = new google.auth.GoogleAuth({
         *     // Scopes can be specified either as an array or as a single, space-delimited string.
         *     scopes: ['https://www.googleapis.com/auth/cloud-platform'],
         *   });
         *
         *   // Acquire an auth client, and bind it to all future calls
         *   const authClient = await auth.getClient();
         *   google.options({auth: authClient});
         *
         *   // Do the magic
         *   const res = await config.projects.locations.deployments.getIamPolicy({
         *     // Optional. The maximum policy version that will be used to format the policy. Valid values are 0, 1, and 3. Requests specifying an invalid value will be rejected. Requests for policies with any conditional role bindings must specify version 3. Policies with no conditional role bindings may specify any valid value or leave the field unset. The policy in the response might use the policy version that you specified, or it might use a lower policy version. For example, if you specify version 3, but the policy has no conditional role bindings, the response uses version 1. To learn which resources support conditions in their IAM policies, see the [IAM documentation](https://cloud.google.com/iam/help/conditions/resource-policies).
         *     'options.requestedPolicyVersion': 'placeholder-value',
         *     // REQUIRED: The resource for which the policy is being requested. See [Resource names](https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.
         *     resource:
         *       'projects/my-project/locations/my-location/deployments/my-deployment',
         *   });
         *   console.log(res.data);
         *
         *   // Example response
         *   // {
         *   //   "auditConfigs": [],
         *   //   "bindings": [],
         *   //   "etag": "my_etag",
         *   //   "version": 0
         *   // }
         * }
         *
         * main().catch(e => {
         *   console.error(e);
         *   throw e;
         * });
         *
         * ```
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        getIamPolicy(params: Params$Resource$Projects$Locations$Deployments$Getiampolicy, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        getIamPolicy(params?: Params$Resource$Projects$Locations$Deployments$Getiampolicy, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$Policy>>;
        getIamPolicy(params: Params$Resource$Projects$Locations$Deployments$Getiampolicy, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        getIamPolicy(params: Params$Resource$Projects$Locations$Deployments$Getiampolicy, options: MethodOptions | BodyResponseCallback<Schema$Policy>, callback: BodyResponseCallback<Schema$Policy>): void;
        getIamPolicy(params: Params$Resource$Projects$Locations$Deployments$Getiampolicy, callback: BodyResponseCallback<Schema$Policy>): void;
        getIamPolicy(callback: BodyResponseCallback<Schema$Policy>): void;
        /**
         * Imports Terraform state file in a given deployment. The state file does not take effect until the Deployment has been unlocked.
         * @example
         * ```js
         * // Before running the sample:
         * // - Enable the API at:
         * //   https://console.developers.google.com/apis/api/config.googleapis.com
         * // - Login into gcloud by running:
         * //   ```sh
         * //   $ gcloud auth application-default login
         * //   ```
         * // - Install the npm module by running:
         * //   ```sh
         * //   $ npm install googleapis
         * //   ```
         *
         * const {google} = require('googleapis');
         * const config = google.config('v1');
         *
         * async function main() {
         *   const auth = new google.auth.GoogleAuth({
         *     // Scopes can be specified either as an array or as a single, space-delimited string.
         *     scopes: ['https://www.googleapis.com/auth/cloud-platform'],
         *   });
         *
         *   // Acquire an auth client, and bind it to all future calls
         *   const authClient = await auth.getClient();
         *   google.options({auth: authClient});
         *
         *   // Do the magic
         *   const res = await config.projects.locations.deployments.importState({
         *     // Required. The parent in whose context the statefile is listed. The parent value is in the format: 'projects/{project_id\}/locations/{location\}/deployments/{deployment\}'.
         *     parent:
         *       'projects/my-project/locations/my-location/deployments/my-deployment',
         *
         *     // Request body metadata
         *     requestBody: {
         *       // request body parameters
         *       // {
         *       //   "lockId": "my_lockId"
         *       // }
         *     },
         *   });
         *   console.log(res.data);
         *
         *   // Example response
         *   // {
         *   //   "signedUri": "my_signedUri"
         *   // }
         * }
         *
         * main().catch(e => {
         *   console.error(e);
         *   throw e;
         * });
         *
         * ```
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        importState(params: Params$Resource$Projects$Locations$Deployments$Importstate, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        importState(params?: Params$Resource$Projects$Locations$Deployments$Importstate, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$Statefile>>;
        importState(params: Params$Resource$Projects$Locations$Deployments$Importstate, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        importState(params: Params$Resource$Projects$Locations$Deployments$Importstate, options: MethodOptions | BodyResponseCallback<Schema$Statefile>, callback: BodyResponseCallback<Schema$Statefile>): void;
        importState(params: Params$Resource$Projects$Locations$Deployments$Importstate, callback: BodyResponseCallback<Schema$Statefile>): void;
        importState(callback: BodyResponseCallback<Schema$Statefile>): void;
        /**
         * Lists Deployments in a given project and location.
         * @example
         * ```js
         * // Before running the sample:
         * // - Enable the API at:
         * //   https://console.developers.google.com/apis/api/config.googleapis.com
         * // - Login into gcloud by running:
         * //   ```sh
         * //   $ gcloud auth application-default login
         * //   ```
         * // - Install the npm module by running:
         * //   ```sh
         * //   $ npm install googleapis
         * //   ```
         *
         * const {google} = require('googleapis');
         * const config = google.config('v1');
         *
         * async function main() {
         *   const auth = new google.auth.GoogleAuth({
         *     // Scopes can be specified either as an array or as a single, space-delimited string.
         *     scopes: ['https://www.googleapis.com/auth/cloud-platform'],
         *   });
         *
         *   // Acquire an auth client, and bind it to all future calls
         *   const authClient = await auth.getClient();
         *   google.options({auth: authClient});
         *
         *   // Do the magic
         *   const res = await config.projects.locations.deployments.list({
         *     // Lists the Deployments that match the filter expression. A filter expression filters the resources listed in the response. The expression must be of the form '{field\} {operator\} {value\}' where operators: '<', '\>', '<=', '\>=', '!=', '=', ':' are supported (colon ':' represents a HAS operator which is roughly synonymous with equality). {field\} can refer to a proto or JSON field, or a synthetic field. Field names can be camelCase or snake_case. Examples: - Filter by name: name = "projects/foo/locations/us-central1/deployments/bar - Filter by labels: - Resources that have a key called 'foo' labels.foo:* - Resources that have a key called 'foo' whose value is 'bar' labels.foo = bar - Filter by state: - Deployments in CREATING state. state=CREATING
         *     filter: 'placeholder-value',
         *     // Field to use to sort the list.
         *     orderBy: 'placeholder-value',
         *     // When requesting a page of resources, 'page_size' specifies number of resources to return. If unspecified, at most 500 will be returned. The maximum value is 1000.
         *     pageSize: 'placeholder-value',
         *     // Token returned by previous call to 'ListDeployments' which specifies the position in the list from where to continue listing the resources.
         *     pageToken: 'placeholder-value',
         *     // Required. The parent in whose context the Deployments are listed. The parent value is in the format: 'projects/{project_id\}/locations/{location\}'.
         *     parent: 'projects/my-project/locations/my-location',
         *   });
         *   console.log(res.data);
         *
         *   // Example response
         *   // {
         *   //   "deployments": [],
         *   //   "nextPageToken": "my_nextPageToken",
         *   //   "unreachable": []
         *   // }
         * }
         *
         * main().catch(e => {
         *   console.error(e);
         *   throw e;
         * });
         *
         * ```
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        list(params: Params$Resource$Projects$Locations$Deployments$List, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        list(params?: Params$Resource$Projects$Locations$Deployments$List, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$ListDeploymentsResponse>>;
        list(params: Params$Resource$Projects$Locations$Deployments$List, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        list(params: Params$Resource$Projects$Locations$Deployments$List, options: MethodOptions | BodyResponseCallback<Schema$ListDeploymentsResponse>, callback: BodyResponseCallback<Schema$ListDeploymentsResponse>): void;
        list(params: Params$Resource$Projects$Locations$Deployments$List, callback: BodyResponseCallback<Schema$ListDeploymentsResponse>): void;
        list(callback: BodyResponseCallback<Schema$ListDeploymentsResponse>): void;
        /**
         * Locks a deployment.
         * @example
         * ```js
         * // Before running the sample:
         * // - Enable the API at:
         * //   https://console.developers.google.com/apis/api/config.googleapis.com
         * // - Login into gcloud by running:
         * //   ```sh
         * //   $ gcloud auth application-default login
         * //   ```
         * // - Install the npm module by running:
         * //   ```sh
         * //   $ npm install googleapis
         * //   ```
         *
         * const {google} = require('googleapis');
         * const config = google.config('v1');
         *
         * async function main() {
         *   const auth = new google.auth.GoogleAuth({
         *     // Scopes can be specified either as an array or as a single, space-delimited string.
         *     scopes: ['https://www.googleapis.com/auth/cloud-platform'],
         *   });
         *
         *   // Acquire an auth client, and bind it to all future calls
         *   const authClient = await auth.getClient();
         *   google.options({auth: authClient});
         *
         *   // Do the magic
         *   const res = await config.projects.locations.deployments.lock({
         *     // Required. The name of the deployment in the format: 'projects/{project_id\}/locations/{location\}/deployments/{deployment\}'.
         *     name: 'projects/my-project/locations/my-location/deployments/my-deployment',
         *
         *     // Request body metadata
         *     requestBody: {
         *       // request body parameters
         *       // {}
         *     },
         *   });
         *   console.log(res.data);
         *
         *   // Example response
         *   // {
         *   //   "done": false,
         *   //   "error": {},
         *   //   "metadata": {},
         *   //   "name": "my_name",
         *   //   "response": {}
         *   // }
         * }
         *
         * main().catch(e => {
         *   console.error(e);
         *   throw e;
         * });
         *
         * ```
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        lock(params: Params$Resource$Projects$Locations$Deployments$Lock, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        lock(params?: Params$Resource$Projects$Locations$Deployments$Lock, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$Operation>>;
        lock(params: Params$Resource$Projects$Locations$Deployments$Lock, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        lock(params: Params$Resource$Projects$Locations$Deployments$Lock, options: MethodOptions | BodyResponseCallback<Schema$Operation>, callback: BodyResponseCallback<Schema$Operation>): void;
        lock(params: Params$Resource$Projects$Locations$Deployments$Lock, callback: BodyResponseCallback<Schema$Operation>): void;
        lock(callback: BodyResponseCallback<Schema$Operation>): void;
        /**
         * Updates a Deployment.
         * @example
         * ```js
         * // Before running the sample:
         * // - Enable the API at:
         * //   https://console.developers.google.com/apis/api/config.googleapis.com
         * // - Login into gcloud by running:
         * //   ```sh
         * //   $ gcloud auth application-default login
         * //   ```
         * // - Install the npm module by running:
         * //   ```sh
         * //   $ npm install googleapis
         * //   ```
         *
         * const {google} = require('googleapis');
         * const config = google.config('v1');
         *
         * async function main() {
         *   const auth = new google.auth.GoogleAuth({
         *     // Scopes can be specified either as an array or as a single, space-delimited string.
         *     scopes: ['https://www.googleapis.com/auth/cloud-platform'],
         *   });
         *
         *   // Acquire an auth client, and bind it to all future calls
         *   const authClient = await auth.getClient();
         *   google.options({auth: authClient});
         *
         *   // Do the magic
         *   const res = await config.projects.locations.deployments.patch({
         *     // Identifier. Resource name of the deployment. Format: `projects/{project\}/locations/{location\}/deployments/{deployment\}`
         *     name: 'projects/my-project/locations/my-location/deployments/my-deployment',
         *     // Optional. An optional request ID to identify requests. Specify a unique request ID so that if you must retry your request, the server will know to ignore the request if it has already been completed. The server will guarantee that for at least 60 minutes since the first request. For example, consider a situation where you make an initial request and the request times out. If you make the request again with the same request ID, the server can check if original operation with the same request ID was received, and if so, will ignore the second request. This prevents clients from accidentally creating duplicate commitments. The request ID must be a valid UUID with the exception that zero UUID is not supported (00000000-0000-0000-0000-000000000000).
         *     requestId: 'placeholder-value',
         *     // Optional. Field mask used to specify the fields to be overwritten in the Deployment resource by the update. The fields specified in the update_mask are relative to the resource, not the full request. A field will be overwritten if it is in the mask. If the user does not provide a mask then all fields will be overwritten.
         *     updateMask: 'placeholder-value',
         *
         *     // Request body metadata
         *     requestBody: {
         *       // request body parameters
         *       // {
         *       //   "annotations": {},
         *       //   "artifactsGcsBucket": "my_artifactsGcsBucket",
         *       //   "createTime": "my_createTime",
         *       //   "deleteBuild": "my_deleteBuild",
         *       //   "deleteLogs": "my_deleteLogs",
         *       //   "deleteResults": {},
         *       //   "errorCode": "my_errorCode",
         *       //   "errorLogs": "my_errorLogs",
         *       //   "importExistingResources": false,
         *       //   "labels": {},
         *       //   "latestRevision": "my_latestRevision",
         *       //   "lockState": "my_lockState",
         *       //   "name": "my_name",
         *       //   "quotaValidation": "my_quotaValidation",
         *       //   "serviceAccount": "my_serviceAccount",
         *       //   "state": "my_state",
         *       //   "stateDetail": "my_stateDetail",
         *       //   "terraformBlueprint": {},
         *       //   "tfErrors": [],
         *       //   "tfVersion": "my_tfVersion",
         *       //   "tfVersionConstraint": "my_tfVersionConstraint",
         *       //   "updateTime": "my_updateTime",
         *       //   "workerPool": "my_workerPool"
         *       // }
         *     },
         *   });
         *   console.log(res.data);
         *
         *   // Example response
         *   // {
         *   //   "done": false,
         *   //   "error": {},
         *   //   "metadata": {},
         *   //   "name": "my_name",
         *   //   "response": {}
         *   // }
         * }
         *
         * main().catch(e => {
         *   console.error(e);
         *   throw e;
         * });
         *
         * ```
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        patch(params: Params$Resource$Projects$Locations$Deployments$Patch, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        patch(params?: Params$Resource$Projects$Locations$Deployments$Patch, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$Operation>>;
        patch(params: Params$Resource$Projects$Locations$Deployments$Patch, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        patch(params: Params$Resource$Projects$Locations$Deployments$Patch, options: MethodOptions | BodyResponseCallback<Schema$Operation>, callback: BodyResponseCallback<Schema$Operation>): void;
        patch(params: Params$Resource$Projects$Locations$Deployments$Patch, callback: BodyResponseCallback<Schema$Operation>): void;
        patch(callback: BodyResponseCallback<Schema$Operation>): void;
        /**
         * Sets the access control policy on the specified resource. Replaces any existing policy. Can return `NOT_FOUND`, `INVALID_ARGUMENT`, and `PERMISSION_DENIED` errors.
         * @example
         * ```js
         * // Before running the sample:
         * // - Enable the API at:
         * //   https://console.developers.google.com/apis/api/config.googleapis.com
         * // - Login into gcloud by running:
         * //   ```sh
         * //   $ gcloud auth application-default login
         * //   ```
         * // - Install the npm module by running:
         * //   ```sh
         * //   $ npm install googleapis
         * //   ```
         *
         * const {google} = require('googleapis');
         * const config = google.config('v1');
         *
         * async function main() {
         *   const auth = new google.auth.GoogleAuth({
         *     // Scopes can be specified either as an array or as a single, space-delimited string.
         *     scopes: ['https://www.googleapis.com/auth/cloud-platform'],
         *   });
         *
         *   // Acquire an auth client, and bind it to all future calls
         *   const authClient = await auth.getClient();
         *   google.options({auth: authClient});
         *
         *   // Do the magic
         *   const res = await config.projects.locations.deployments.setIamPolicy({
         *     // REQUIRED: The resource for which the policy is being specified. See [Resource names](https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.
         *     resource:
         *       'projects/my-project/locations/my-location/deployments/my-deployment',
         *
         *     // Request body metadata
         *     requestBody: {
         *       // request body parameters
         *       // {
         *       //   "policy": {},
         *       //   "updateMask": "my_updateMask"
         *       // }
         *     },
         *   });
         *   console.log(res.data);
         *
         *   // Example response
         *   // {
         *   //   "auditConfigs": [],
         *   //   "bindings": [],
         *   //   "etag": "my_etag",
         *   //   "version": 0
         *   // }
         * }
         *
         * main().catch(e => {
         *   console.error(e);
         *   throw e;
         * });
         *
         * ```
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        setIamPolicy(params: Params$Resource$Projects$Locations$Deployments$Setiampolicy, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        setIamPolicy(params?: Params$Resource$Projects$Locations$Deployments$Setiampolicy, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$Policy>>;
        setIamPolicy(params: Params$Resource$Projects$Locations$Deployments$Setiampolicy, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        setIamPolicy(params: Params$Resource$Projects$Locations$Deployments$Setiampolicy, options: MethodOptions | BodyResponseCallback<Schema$Policy>, callback: BodyResponseCallback<Schema$Policy>): void;
        setIamPolicy(params: Params$Resource$Projects$Locations$Deployments$Setiampolicy, callback: BodyResponseCallback<Schema$Policy>): void;
        setIamPolicy(callback: BodyResponseCallback<Schema$Policy>): void;
        /**
         * Returns permissions that a caller has on the specified resource. If the resource does not exist, this will return an empty set of permissions, not a `NOT_FOUND` error. Note: This operation is designed to be used for building permission-aware UIs and command-line tools, not for authorization checking. This operation may "fail open" without warning.
         * @example
         * ```js
         * // Before running the sample:
         * // - Enable the API at:
         * //   https://console.developers.google.com/apis/api/config.googleapis.com
         * // - Login into gcloud by running:
         * //   ```sh
         * //   $ gcloud auth application-default login
         * //   ```
         * // - Install the npm module by running:
         * //   ```sh
         * //   $ npm install googleapis
         * //   ```
         *
         * const {google} = require('googleapis');
         * const config = google.config('v1');
         *
         * async function main() {
         *   const auth = new google.auth.GoogleAuth({
         *     // Scopes can be specified either as an array or as a single, space-delimited string.
         *     scopes: ['https://www.googleapis.com/auth/cloud-platform'],
         *   });
         *
         *   // Acquire an auth client, and bind it to all future calls
         *   const authClient = await auth.getClient();
         *   google.options({auth: authClient});
         *
         *   // Do the magic
         *   const res = await config.projects.locations.deployments.testIamPermissions({
         *     // REQUIRED: The resource for which the policy detail is being requested. See [Resource names](https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.
         *     resource:
         *       'projects/my-project/locations/my-location/deployments/my-deployment',
         *
         *     // Request body metadata
         *     requestBody: {
         *       // request body parameters
         *       // {
         *       //   "permissions": []
         *       // }
         *     },
         *   });
         *   console.log(res.data);
         *
         *   // Example response
         *   // {
         *   //   "permissions": []
         *   // }
         * }
         *
         * main().catch(e => {
         *   console.error(e);
         *   throw e;
         * });
         *
         * ```
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        testIamPermissions(params: Params$Resource$Projects$Locations$Deployments$Testiampermissions, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        testIamPermissions(params?: Params$Resource$Projects$Locations$Deployments$Testiampermissions, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$TestIamPermissionsResponse>>;
        testIamPermissions(params: Params$Resource$Projects$Locations$Deployments$Testiampermissions, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        testIamPermissions(params: Params$Resource$Projects$Locations$Deployments$Testiampermissions, options: MethodOptions | BodyResponseCallback<Schema$TestIamPermissionsResponse>, callback: BodyResponseCallback<Schema$TestIamPermissionsResponse>): void;
        testIamPermissions(params: Params$Resource$Projects$Locations$Deployments$Testiampermissions, callback: BodyResponseCallback<Schema$TestIamPermissionsResponse>): void;
        testIamPermissions(callback: BodyResponseCallback<Schema$TestIamPermissionsResponse>): void;
        /**
         * Unlocks a locked deployment.
         * @example
         * ```js
         * // Before running the sample:
         * // - Enable the API at:
         * //   https://console.developers.google.com/apis/api/config.googleapis.com
         * // - Login into gcloud by running:
         * //   ```sh
         * //   $ gcloud auth application-default login
         * //   ```
         * // - Install the npm module by running:
         * //   ```sh
         * //   $ npm install googleapis
         * //   ```
         *
         * const {google} = require('googleapis');
         * const config = google.config('v1');
         *
         * async function main() {
         *   const auth = new google.auth.GoogleAuth({
         *     // Scopes can be specified either as an array or as a single, space-delimited string.
         *     scopes: ['https://www.googleapis.com/auth/cloud-platform'],
         *   });
         *
         *   // Acquire an auth client, and bind it to all future calls
         *   const authClient = await auth.getClient();
         *   google.options({auth: authClient});
         *
         *   // Do the magic
         *   const res = await config.projects.locations.deployments.unlock({
         *     // Required. The name of the deployment in the format: 'projects/{project_id\}/locations/{location\}/deployments/{deployment\}'.
         *     name: 'projects/my-project/locations/my-location/deployments/my-deployment',
         *
         *     // Request body metadata
         *     requestBody: {
         *       // request body parameters
         *       // {
         *       //   "lockId": "my_lockId"
         *       // }
         *     },
         *   });
         *   console.log(res.data);
         *
         *   // Example response
         *   // {
         *   //   "done": false,
         *   //   "error": {},
         *   //   "metadata": {},
         *   //   "name": "my_name",
         *   //   "response": {}
         *   // }
         * }
         *
         * main().catch(e => {
         *   console.error(e);
         *   throw e;
         * });
         *
         * ```
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        unlock(params: Params$Resource$Projects$Locations$Deployments$Unlock, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        unlock(params?: Params$Resource$Projects$Locations$Deployments$Unlock, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$Operation>>;
        unlock(params: Params$Resource$Projects$Locations$Deployments$Unlock, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        unlock(params: Params$Resource$Projects$Locations$Deployments$Unlock, options: MethodOptions | BodyResponseCallback<Schema$Operation>, callback: BodyResponseCallback<Schema$Operation>): void;
        unlock(params: Params$Resource$Projects$Locations$Deployments$Unlock, callback: BodyResponseCallback<Schema$Operation>): void;
        unlock(callback: BodyResponseCallback<Schema$Operation>): void;
    }
    export interface Params$Resource$Projects$Locations$Deployments$Create extends StandardParameters {
        /**
         * Required. The Deployment ID.
         */
        deploymentId?: string;
        /**
         * Required. The parent in whose context the Deployment is created. The parent value is in the format: 'projects/{project_id\}/locations/{location\}'.
         */
        parent?: string;
        /**
         * Optional. An optional request ID to identify requests. Specify a unique request ID so that if you must retry your request, the server will know to ignore the request if it has already been completed. The server will guarantee that for at least 60 minutes since the first request. For example, consider a situation where you make an initial request and the request times out. If you make the request again with the same request ID, the server can check if original operation with the same request ID was received, and if so, will ignore the second request. This prevents clients from accidentally creating duplicate commitments. The request ID must be a valid UUID with the exception that zero UUID is not supported (00000000-0000-0000-0000-000000000000).
         */
        requestId?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$Deployment;
    }
    export interface Params$Resource$Projects$Locations$Deployments$Delete extends StandardParameters {
        /**
         * Optional. Policy on how resources actuated by the deployment should be deleted. If unspecified, the default behavior is to delete the underlying resources.
         */
        deletePolicy?: string;
        /**
         * Optional. If set to true, any revisions for this deployment will also be deleted. (Otherwise, the request will only work if the deployment has no revisions.)
         */
        force?: boolean;
        /**
         * Required. The name of the Deployment in the format: 'projects/{project_id\}/locations/{location\}/deployments/{deployment\}'.
         */
        name?: string;
        /**
         * Optional. An optional request ID to identify requests. Specify a unique request ID so that if you must retry your request, the server will know to ignore the request if it has already been completed. The server will guarantee that for at least 60 minutes after the first request. For example, consider a situation where you make an initial request and the request times out. If you make the request again with the same request ID, the server can check if original operation with the same request ID was received, and if so, will ignore the second request. This prevents clients from accidentally creating duplicate commitments. The request ID must be a valid UUID with the exception that zero UUID is not supported (00000000-0000-0000-0000-000000000000).
         */
        requestId?: string;
    }
    export interface Params$Resource$Projects$Locations$Deployments$Deletestate extends StandardParameters {
        /**
         * Required. The name of the deployment in the format: 'projects/{project_id\}/locations/{location\}/deployments/{deployment\}'.
         */
        name?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$DeleteStatefileRequest;
    }
    export interface Params$Resource$Projects$Locations$Deployments$Exportlock extends StandardParameters {
        /**
         * Required. The name of the deployment in the format: 'projects/{project_id\}/locations/{location\}/deployments/{deployment\}'.
         */
        name?: string;
    }
    export interface Params$Resource$Projects$Locations$Deployments$Exportstate extends StandardParameters {
        /**
         * Required. The parent in whose context the statefile is listed. The parent value is in the format: 'projects/{project_id\}/locations/{location\}/deployments/{deployment\}'.
         */
        parent?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$ExportDeploymentStatefileRequest;
    }
    export interface Params$Resource$Projects$Locations$Deployments$Get extends StandardParameters {
        /**
         * Required. The name of the deployment. Format: 'projects/{project_id\}/locations/{location\}/deployments/{deployment\}'.
         */
        name?: string;
    }
    export interface Params$Resource$Projects$Locations$Deployments$Getiampolicy extends StandardParameters {
        /**
         * Optional. The maximum policy version that will be used to format the policy. Valid values are 0, 1, and 3. Requests specifying an invalid value will be rejected. Requests for policies with any conditional role bindings must specify version 3. Policies with no conditional role bindings may specify any valid value or leave the field unset. The policy in the response might use the policy version that you specified, or it might use a lower policy version. For example, if you specify version 3, but the policy has no conditional role bindings, the response uses version 1. To learn which resources support conditions in their IAM policies, see the [IAM documentation](https://cloud.google.com/iam/help/conditions/resource-policies).
         */
        'options.requestedPolicyVersion'?: number;
        /**
         * REQUIRED: The resource for which the policy is being requested. See [Resource names](https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.
         */
        resource?: string;
    }
    export interface Params$Resource$Projects$Locations$Deployments$Importstate extends StandardParameters {
        /**
         * Required. The parent in whose context the statefile is listed. The parent value is in the format: 'projects/{project_id\}/locations/{location\}/deployments/{deployment\}'.
         */
        parent?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$ImportStatefileRequest;
    }
    export interface Params$Resource$Projects$Locations$Deployments$List extends StandardParameters {
        /**
         * Lists the Deployments that match the filter expression. A filter expression filters the resources listed in the response. The expression must be of the form '{field\} {operator\} {value\}' where operators: '<', '\>', '<=', '\>=', '!=', '=', ':' are supported (colon ':' represents a HAS operator which is roughly synonymous with equality). {field\} can refer to a proto or JSON field, or a synthetic field. Field names can be camelCase or snake_case. Examples: - Filter by name: name = "projects/foo/locations/us-central1/deployments/bar - Filter by labels: - Resources that have a key called 'foo' labels.foo:* - Resources that have a key called 'foo' whose value is 'bar' labels.foo = bar - Filter by state: - Deployments in CREATING state. state=CREATING
         */
        filter?: string;
        /**
         * Field to use to sort the list.
         */
        orderBy?: string;
        /**
         * When requesting a page of resources, 'page_size' specifies number of resources to return. If unspecified, at most 500 will be returned. The maximum value is 1000.
         */
        pageSize?: number;
        /**
         * Token returned by previous call to 'ListDeployments' which specifies the position in the list from where to continue listing the resources.
         */
        pageToken?: string;
        /**
         * Required. The parent in whose context the Deployments are listed. The parent value is in the format: 'projects/{project_id\}/locations/{location\}'.
         */
        parent?: string;
    }
    export interface Params$Resource$Projects$Locations$Deployments$Lock extends StandardParameters {
        /**
         * Required. The name of the deployment in the format: 'projects/{project_id\}/locations/{location\}/deployments/{deployment\}'.
         */
        name?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$LockDeploymentRequest;
    }
    export interface Params$Resource$Projects$Locations$Deployments$Patch extends StandardParameters {
        /**
         * Identifier. Resource name of the deployment. Format: `projects/{project\}/locations/{location\}/deployments/{deployment\}`
         */
        name?: string;
        /**
         * Optional. An optional request ID to identify requests. Specify a unique request ID so that if you must retry your request, the server will know to ignore the request if it has already been completed. The server will guarantee that for at least 60 minutes since the first request. For example, consider a situation where you make an initial request and the request times out. If you make the request again with the same request ID, the server can check if original operation with the same request ID was received, and if so, will ignore the second request. This prevents clients from accidentally creating duplicate commitments. The request ID must be a valid UUID with the exception that zero UUID is not supported (00000000-0000-0000-0000-000000000000).
         */
        requestId?: string;
        /**
         * Optional. Field mask used to specify the fields to be overwritten in the Deployment resource by the update. The fields specified in the update_mask are relative to the resource, not the full request. A field will be overwritten if it is in the mask. If the user does not provide a mask then all fields will be overwritten.
         */
        updateMask?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$Deployment;
    }
    export interface Params$Resource$Projects$Locations$Deployments$Setiampolicy extends StandardParameters {
        /**
         * REQUIRED: The resource for which the policy is being specified. See [Resource names](https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.
         */
        resource?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$SetIamPolicyRequest;
    }
    export interface Params$Resource$Projects$Locations$Deployments$Testiampermissions extends StandardParameters {
        /**
         * REQUIRED: The resource for which the policy detail is being requested. See [Resource names](https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.
         */
        resource?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$TestIamPermissionsRequest;
    }
    export interface Params$Resource$Projects$Locations$Deployments$Unlock extends StandardParameters {
        /**
         * Required. The name of the deployment in the format: 'projects/{project_id\}/locations/{location\}/deployments/{deployment\}'.
         */
        name?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$UnlockDeploymentRequest;
    }
    export class Resource$Projects$Locations$Deployments$Revisions {
        context: APIRequestContext;
        resources: Resource$Projects$Locations$Deployments$Revisions$Resources;
        constructor(context: APIRequestContext);
        /**
         * Exports Terraform state file from a given revision.
         * @example
         * ```js
         * // Before running the sample:
         * // - Enable the API at:
         * //   https://console.developers.google.com/apis/api/config.googleapis.com
         * // - Login into gcloud by running:
         * //   ```sh
         * //   $ gcloud auth application-default login
         * //   ```
         * // - Install the npm module by running:
         * //   ```sh
         * //   $ npm install googleapis
         * //   ```
         *
         * const {google} = require('googleapis');
         * const config = google.config('v1');
         *
         * async function main() {
         *   const auth = new google.auth.GoogleAuth({
         *     // Scopes can be specified either as an array or as a single, space-delimited string.
         *     scopes: ['https://www.googleapis.com/auth/cloud-platform'],
         *   });
         *
         *   // Acquire an auth client, and bind it to all future calls
         *   const authClient = await auth.getClient();
         *   google.options({auth: authClient});
         *
         *   // Do the magic
         *   const res = await config.projects.locations.deployments.revisions.exportState(
         *     {
         *       // Required. The parent in whose context the statefile is listed. The parent value is in the format: 'projects/{project_id\}/locations/{location\}/deployments/{deployment\}/revisions/{revision\}'.
         *       parent:
         *         'projects/my-project/locations/my-location/deployments/my-deployment/revisions/my-revision',
         *
         *       // Request body metadata
         *       requestBody: {
         *         // request body parameters
         *         // {}
         *       },
         *     },
         *   );
         *   console.log(res.data);
         *
         *   // Example response
         *   // {
         *   //   "signedUri": "my_signedUri"
         *   // }
         * }
         *
         * main().catch(e => {
         *   console.error(e);
         *   throw e;
         * });
         *
         * ```
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        exportState(params: Params$Resource$Projects$Locations$Deployments$Revisions$Exportstate, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        exportState(params?: Params$Resource$Projects$Locations$Deployments$Revisions$Exportstate, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$Statefile>>;
        exportState(params: Params$Resource$Projects$Locations$Deployments$Revisions$Exportstate, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        exportState(params: Params$Resource$Projects$Locations$Deployments$Revisions$Exportstate, options: MethodOptions | BodyResponseCallback<Schema$Statefile>, callback: BodyResponseCallback<Schema$Statefile>): void;
        exportState(params: Params$Resource$Projects$Locations$Deployments$Revisions$Exportstate, callback: BodyResponseCallback<Schema$Statefile>): void;
        exportState(callback: BodyResponseCallback<Schema$Statefile>): void;
        /**
         * Gets details about a Revision.
         * @example
         * ```js
         * // Before running the sample:
         * // - Enable the API at:
         * //   https://console.developers.google.com/apis/api/config.googleapis.com
         * // - Login into gcloud by running:
         * //   ```sh
         * //   $ gcloud auth application-default login
         * //   ```
         * // - Install the npm module by running:
         * //   ```sh
         * //   $ npm install googleapis
         * //   ```
         *
         * const {google} = require('googleapis');
         * const config = google.config('v1');
         *
         * async function main() {
         *   const auth = new google.auth.GoogleAuth({
         *     // Scopes can be specified either as an array or as a single, space-delimited string.
         *     scopes: ['https://www.googleapis.com/auth/cloud-platform'],
         *   });
         *
         *   // Acquire an auth client, and bind it to all future calls
         *   const authClient = await auth.getClient();
         *   google.options({auth: authClient});
         *
         *   // Do the magic
         *   const res = await config.projects.locations.deployments.revisions.get({
         *     // Required. The name of the Revision in the format: 'projects/{project_id\}/locations/{location\}/deployments/{deployment\}/revisions/{revision\}'.
         *     name: 'projects/my-project/locations/my-location/deployments/my-deployment/revisions/my-revision',
         *   });
         *   console.log(res.data);
         *
         *   // Example response
         *   // {
         *   //   "action": "my_action",
         *   //   "applyResults": {},
         *   //   "build": "my_build",
         *   //   "createTime": "my_createTime",
         *   //   "errorCode": "my_errorCode",
         *   //   "errorLogs": "my_errorLogs",
         *   //   "importExistingResources": false,
         *   //   "logs": "my_logs",
         *   //   "name": "my_name",
         *   //   "quotaValidation": "my_quotaValidation",
         *   //   "quotaValidationResults": "my_quotaValidationResults",
         *   //   "serviceAccount": "my_serviceAccount",
         *   //   "state": "my_state",
         *   //   "stateDetail": "my_stateDetail",
         *   //   "terraformBlueprint": {},
         *   //   "tfErrors": [],
         *   //   "tfVersion": "my_tfVersion",
         *   //   "tfVersionConstraint": "my_tfVersionConstraint",
         *   //   "updateTime": "my_updateTime",
         *   //   "workerPool": "my_workerPool"
         *   // }
         * }
         *
         * main().catch(e => {
         *   console.error(e);
         *   throw e;
         * });
         *
         * ```
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        get(params: Params$Resource$Projects$Locations$Deployments$Revisions$Get, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        get(params?: Params$Resource$Projects$Locations$Deployments$Revisions$Get, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$Revision>>;
        get(params: Params$Resource$Projects$Locations$Deployments$Revisions$Get, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        get(params: Params$Resource$Projects$Locations$Deployments$Revisions$Get, options: MethodOptions | BodyResponseCallback<Schema$Revision>, callback: BodyResponseCallback<Schema$Revision>): void;
        get(params: Params$Resource$Projects$Locations$Deployments$Revisions$Get, callback: BodyResponseCallback<Schema$Revision>): void;
        get(callback: BodyResponseCallback<Schema$Revision>): void;
        /**
         * Lists Revisions of a deployment.
         * @example
         * ```js
         * // Before running the sample:
         * // - Enable the API at:
         * //   https://console.developers.google.com/apis/api/config.googleapis.com
         * // - Login into gcloud by running:
         * //   ```sh
         * //   $ gcloud auth application-default login
         * //   ```
         * // - Install the npm module by running:
         * //   ```sh
         * //   $ npm install googleapis
         * //   ```
         *
         * const {google} = require('googleapis');
         * const config = google.config('v1');
         *
         * async function main() {
         *   const auth = new google.auth.GoogleAuth({
         *     // Scopes can be specified either as an array or as a single, space-delimited string.
         *     scopes: ['https://www.googleapis.com/auth/cloud-platform'],
         *   });
         *
         *   // Acquire an auth client, and bind it to all future calls
         *   const authClient = await auth.getClient();
         *   google.options({auth: authClient});
         *
         *   // Do the magic
         *   const res = await config.projects.locations.deployments.revisions.list({
         *     // Lists the Revisions that match the filter expression. A filter expression filters the resources listed in the response. The expression must be of the form '{field\} {operator\} {value\}' where operators: '<', '\>', '<=', '\>=', '!=', '=', ':' are supported (colon ':' represents a HAS operator which is roughly synonymous with equality). {field\} can refer to a proto or JSON field, or a synthetic field. Field names can be camelCase or snake_case. Examples: - Filter by name: name = "projects/foo/locations/us-central1/deployments/dep/revisions/bar - Filter by labels: - Resources that have a key called 'foo' labels.foo:* - Resources that have a key called 'foo' whose value is 'bar' labels.foo = bar - Filter by state: - Revisions in CREATING state. state=CREATING
         *     filter: 'placeholder-value',
         *     // Field to use to sort the list.
         *     orderBy: 'placeholder-value',
         *     // When requesting a page of resources, `page_size` specifies number of resources to return. If unspecified, at most 500 will be returned. The maximum value is 1000.
         *     pageSize: 'placeholder-value',
         *     // Token returned by previous call to 'ListRevisions' which specifies the position in the list from where to continue listing the resources.
         *     pageToken: 'placeholder-value',
         *     // Required. The parent in whose context the Revisions are listed. The parent value is in the format: 'projects/{project_id\}/locations/{location\}/deployments/{deployment\}'.
         *     parent:
         *       'projects/my-project/locations/my-location/deployments/my-deployment',
         *   });
         *   console.log(res.data);
         *
         *   // Example response
         *   // {
         *   //   "nextPageToken": "my_nextPageToken",
         *   //   "revisions": [],
         *   //   "unreachable": []
         *   // }
         * }
         *
         * main().catch(e => {
         *   console.error(e);
         *   throw e;
         * });
         *
         * ```
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        list(params: Params$Resource$Projects$Locations$Deployments$Revisions$List, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        list(params?: Params$Resource$Projects$Locations$Deployments$Revisions$List, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$ListRevisionsResponse>>;
        list(params: Params$Resource$Projects$Locations$Deployments$Revisions$List, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        list(params: Params$Resource$Projects$Locations$Deployments$Revisions$List, options: MethodOptions | BodyResponseCallback<Schema$ListRevisionsResponse>, callback: BodyResponseCallback<Schema$ListRevisionsResponse>): void;
        list(params: Params$Resource$Projects$Locations$Deployments$Revisions$List, callback: BodyResponseCallback<Schema$ListRevisionsResponse>): void;
        list(callback: BodyResponseCallback<Schema$ListRevisionsResponse>): void;
    }
    export interface Params$Resource$Projects$Locations$Deployments$Revisions$Exportstate extends StandardParameters {
        /**
         * Required. The parent in whose context the statefile is listed. The parent value is in the format: 'projects/{project_id\}/locations/{location\}/deployments/{deployment\}/revisions/{revision\}'.
         */
        parent?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$ExportRevisionStatefileRequest;
    }
    export interface Params$Resource$Projects$Locations$Deployments$Revisions$Get extends StandardParameters {
        /**
         * Required. The name of the Revision in the format: 'projects/{project_id\}/locations/{location\}/deployments/{deployment\}/revisions/{revision\}'.
         */
        name?: string;
    }
    export interface Params$Resource$Projects$Locations$Deployments$Revisions$List extends StandardParameters {
        /**
         * Lists the Revisions that match the filter expression. A filter expression filters the resources listed in the response. The expression must be of the form '{field\} {operator\} {value\}' where operators: '<', '\>', '<=', '\>=', '!=', '=', ':' are supported (colon ':' represents a HAS operator which is roughly synonymous with equality). {field\} can refer to a proto or JSON field, or a synthetic field. Field names can be camelCase or snake_case. Examples: - Filter by name: name = "projects/foo/locations/us-central1/deployments/dep/revisions/bar - Filter by labels: - Resources that have a key called 'foo' labels.foo:* - Resources that have a key called 'foo' whose value is 'bar' labels.foo = bar - Filter by state: - Revisions in CREATING state. state=CREATING
         */
        filter?: string;
        /**
         * Field to use to sort the list.
         */
        orderBy?: string;
        /**
         * When requesting a page of resources, `page_size` specifies number of resources to return. If unspecified, at most 500 will be returned. The maximum value is 1000.
         */
        pageSize?: number;
        /**
         * Token returned by previous call to 'ListRevisions' which specifies the position in the list from where to continue listing the resources.
         */
        pageToken?: string;
        /**
         * Required. The parent in whose context the Revisions are listed. The parent value is in the format: 'projects/{project_id\}/locations/{location\}/deployments/{deployment\}'.
         */
        parent?: string;
    }
    export class Resource$Projects$Locations$Deployments$Revisions$Resources {
        context: APIRequestContext;
        constructor(context: APIRequestContext);
        /**
         * Gets details about a Resource deployed by Infra Manager.
         * @example
         * ```js
         * // Before running the sample:
         * // - Enable the API at:
         * //   https://console.developers.google.com/apis/api/config.googleapis.com
         * // - Login into gcloud by running:
         * //   ```sh
         * //   $ gcloud auth application-default login
         * //   ```
         * // - Install the npm module by running:
         * //   ```sh
         * //   $ npm install googleapis
         * //   ```
         *
         * const {google} = require('googleapis');
         * const config = google.config('v1');
         *
         * async function main() {
         *   const auth = new google.auth.GoogleAuth({
         *     // Scopes can be specified either as an array or as a single, space-delimited string.
         *     scopes: ['https://www.googleapis.com/auth/cloud-platform'],
         *   });
         *
         *   // Acquire an auth client, and bind it to all future calls
         *   const authClient = await auth.getClient();
         *   google.options({auth: authClient});
         *
         *   // Do the magic
         *   const res =
         *     await config.projects.locations.deployments.revisions.resources.get({
         *       // Required. The name of the Resource in the format: 'projects/{project_id\}/locations/{location\}/deployments/{deployment\}/revisions/{revision\}/resource/{resource\}'.
         *       name: 'projects/my-project/locations/my-location/deployments/my-deployment/revisions/my-revision/resources/my-resource',
         *     });
         *   console.log(res.data);
         *
         *   // Example response
         *   // {
         *   //   "caiAssets": {},
         *   //   "intent": "my_intent",
         *   //   "name": "my_name",
         *   //   "state": "my_state",
         *   //   "terraformInfo": {}
         *   // }
         * }
         *
         * main().catch(e => {
         *   console.error(e);
         *   throw e;
         * });
         *
         * ```
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        get(params: Params$Resource$Projects$Locations$Deployments$Revisions$Resources$Get, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        get(params?: Params$Resource$Projects$Locations$Deployments$Revisions$Resources$Get, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$Resource>>;
        get(params: Params$Resource$Projects$Locations$Deployments$Revisions$Resources$Get, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        get(params: Params$Resource$Projects$Locations$Deployments$Revisions$Resources$Get, options: MethodOptions | BodyResponseCallback<Schema$Resource>, callback: BodyResponseCallback<Schema$Resource>): void;
        get(params: Params$Resource$Projects$Locations$Deployments$Revisions$Resources$Get, callback: BodyResponseCallback<Schema$Resource>): void;
        get(callback: BodyResponseCallback<Schema$Resource>): void;
        /**
         * Lists Resources in a given revision.
         * @example
         * ```js
         * // Before running the sample:
         * // - Enable the API at:
         * //   https://console.developers.google.com/apis/api/config.googleapis.com
         * // - Login into gcloud by running:
         * //   ```sh
         * //   $ gcloud auth application-default login
         * //   ```
         * // - Install the npm module by running:
         * //   ```sh
         * //   $ npm install googleapis
         * //   ```
         *
         * const {google} = require('googleapis');
         * const config = google.config('v1');
         *
         * async function main() {
         *   const auth = new google.auth.GoogleAuth({
         *     // Scopes can be specified either as an array or as a single, space-delimited string.
         *     scopes: ['https://www.googleapis.com/auth/cloud-platform'],
         *   });
         *
         *   // Acquire an auth client, and bind it to all future calls
         *   const authClient = await auth.getClient();
         *   google.options({auth: authClient});
         *
         *   // Do the magic
         *   const res =
         *     await config.projects.locations.deployments.revisions.resources.list({
         *       // Lists the Resources that match the filter expression. A filter expression filters the resources listed in the response. The expression must be of the form '{field\} {operator\} {value\}' where operators: '<', '\>', '<=', '\>=', '!=', '=', ':' are supported (colon ':' represents a HAS operator which is roughly synonymous with equality). {field\} can refer to a proto or JSON field, or a synthetic field. Field names can be camelCase or snake_case. Examples: - Filter by name: name = "projects/foo/locations/us-central1/deployments/dep/revisions/bar/resources/baz
         *       filter: 'placeholder-value',
         *       // Field to use to sort the list.
         *       orderBy: 'placeholder-value',
         *       // When requesting a page of resources, 'page_size' specifies number of resources to return. If unspecified, at most 500 will be returned. The maximum value is 1000.
         *       pageSize: 'placeholder-value',
         *       // Token returned by previous call to 'ListResources' which specifies the position in the list from where to continue listing the resources.
         *       pageToken: 'placeholder-value',
         *       // Required. The parent in whose context the Resources are listed. The parent value is in the format: 'projects/{project_id\}/locations/{location\}/deployments/{deployment\}/revisions/{revision\}'.
         *       parent:
         *         'projects/my-project/locations/my-location/deployments/my-deployment/revisions/my-revision',
         *     });
         *   console.log(res.data);
         *
         *   // Example response
         *   // {
         *   //   "nextPageToken": "my_nextPageToken",
         *   //   "resources": [],
         *   //   "unreachable": []
         *   // }
         * }
         *
         * main().catch(e => {
         *   console.error(e);
         *   throw e;
         * });
         *
         * ```
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        list(params: Params$Resource$Projects$Locations$Deployments$Revisions$Resources$List, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        list(params?: Params$Resource$Projects$Locations$Deployments$Revisions$Resources$List, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$ListResourcesResponse>>;
        list(params: Params$Resource$Projects$Locations$Deployments$Revisions$Resources$List, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        list(params: Params$Resource$Projects$Locations$Deployments$Revisions$Resources$List, options: MethodOptions | BodyResponseCallback<Schema$ListResourcesResponse>, callback: BodyResponseCallback<Schema$ListResourcesResponse>): void;
        list(params: Params$Resource$Projects$Locations$Deployments$Revisions$Resources$List, callback: BodyResponseCallback<Schema$ListResourcesResponse>): void;
        list(callback: BodyResponseCallback<Schema$ListResourcesResponse>): void;
    }
    export interface Params$Resource$Projects$Locations$Deployments$Revisions$Resources$Get extends StandardParameters {
        /**
         * Required. The name of the Resource in the format: 'projects/{project_id\}/locations/{location\}/deployments/{deployment\}/revisions/{revision\}/resource/{resource\}'.
         */
        name?: string;
    }
    export interface Params$Resource$Projects$Locations$Deployments$Revisions$Resources$List extends StandardParameters {
        /**
         * Lists the Resources that match the filter expression. A filter expression filters the resources listed in the response. The expression must be of the form '{field\} {operator\} {value\}' where operators: '<', '\>', '<=', '\>=', '!=', '=', ':' are supported (colon ':' represents a HAS operator which is roughly synonymous with equality). {field\} can refer to a proto or JSON field, or a synthetic field. Field names can be camelCase or snake_case. Examples: - Filter by name: name = "projects/foo/locations/us-central1/deployments/dep/revisions/bar/resources/baz
         */
        filter?: string;
        /**
         * Field to use to sort the list.
         */
        orderBy?: string;
        /**
         * When requesting a page of resources, 'page_size' specifies number of resources to return. If unspecified, at most 500 will be returned. The maximum value is 1000.
         */
        pageSize?: number;
        /**
         * Token returned by previous call to 'ListResources' which specifies the position in the list from where to continue listing the resources.
         */
        pageToken?: string;
        /**
         * Required. The parent in whose context the Resources are listed. The parent value is in the format: 'projects/{project_id\}/locations/{location\}/deployments/{deployment\}/revisions/{revision\}'.
         */
        parent?: string;
    }
    export class Resource$Projects$Locations$Operations {
        context: APIRequestContext;
        constructor(context: APIRequestContext);
        /**
         * Starts asynchronous cancellation on a long-running operation. The server makes a best effort to cancel the operation, but success is not guaranteed. If the server doesn't support this method, it returns `google.rpc.Code.UNIMPLEMENTED`. Clients can use Operations.GetOperation or other methods to check whether the cancellation succeeded or whether the operation completed despite cancellation. On successful cancellation, the operation is not deleted; instead, it becomes an operation with an Operation.error value with a google.rpc.Status.code of `1`, corresponding to `Code.CANCELLED`.
         * @example
         * ```js
         * // Before running the sample:
         * // - Enable the API at:
         * //   https://console.developers.google.com/apis/api/config.googleapis.com
         * // - Login into gcloud by running:
         * //   ```sh
         * //   $ gcloud auth application-default login
         * //   ```
         * // - Install the npm module by running:
         * //   ```sh
         * //   $ npm install googleapis
         * //   ```
         *
         * const {google} = require('googleapis');
         * const config = google.config('v1');
         *
         * async function main() {
         *   const auth = new google.auth.GoogleAuth({
         *     // Scopes can be specified either as an array or as a single, space-delimited string.
         *     scopes: ['https://www.googleapis.com/auth/cloud-platform'],
         *   });
         *
         *   // Acquire an auth client, and bind it to all future calls
         *   const authClient = await auth.getClient();
         *   google.options({auth: authClient});
         *
         *   // Do the magic
         *   const res = await config.projects.locations.operations.cancel({
         *     // The name of the operation resource to be cancelled.
         *     name: 'projects/my-project/locations/my-location/operations/my-operation',
         *
         *     // Request body metadata
         *     requestBody: {
         *       // request body parameters
         *       // {}
         *     },
         *   });
         *   console.log(res.data);
         *
         *   // Example response
         *   // {}
         * }
         *
         * main().catch(e => {
         *   console.error(e);
         *   throw e;
         * });
         *
         * ```
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        cancel(params: Params$Resource$Projects$Locations$Operations$Cancel, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        cancel(params?: Params$Resource$Projects$Locations$Operations$Cancel, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$Empty>>;
        cancel(params: Params$Resource$Projects$Locations$Operations$Cancel, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        cancel(params: Params$Resource$Projects$Locations$Operations$Cancel, options: MethodOptions | BodyResponseCallback<Schema$Empty>, callback: BodyResponseCallback<Schema$Empty>): void;
        cancel(params: Params$Resource$Projects$Locations$Operations$Cancel, callback: BodyResponseCallback<Schema$Empty>): void;
        cancel(callback: BodyResponseCallback<Schema$Empty>): void;
        /**
         * Deletes a long-running operation. This method indicates that the client is no longer interested in the operation result. It does not cancel the operation. If the server doesn't support this method, it returns `google.rpc.Code.UNIMPLEMENTED`.
         * @example
         * ```js
         * // Before running the sample:
         * // - Enable the API at:
         * //   https://console.developers.google.com/apis/api/config.googleapis.com
         * // - Login into gcloud by running:
         * //   ```sh
         * //   $ gcloud auth application-default login
         * //   ```
         * // - Install the npm module by running:
         * //   ```sh
         * //   $ npm install googleapis
         * //   ```
         *
         * const {google} = require('googleapis');
         * const config = google.config('v1');
         *
         * async function main() {
         *   const auth = new google.auth.GoogleAuth({
         *     // Scopes can be specified either as an array or as a single, space-delimited string.
         *     scopes: ['https://www.googleapis.com/auth/cloud-platform'],
         *   });
         *
         *   // Acquire an auth client, and bind it to all future calls
         *   const authClient = await auth.getClient();
         *   google.options({auth: authClient});
         *
         *   // Do the magic
         *   const res = await config.projects.locations.operations.delete({
         *     // The name of the operation resource to be deleted.
         *     name: 'projects/my-project/locations/my-location/operations/my-operation',
         *   });
         *   console.log(res.data);
         *
         *   // Example response
         *   // {}
         * }
         *
         * main().catch(e => {
         *   console.error(e);
         *   throw e;
         * });
         *
         * ```
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        delete(params: Params$Resource$Projects$Locations$Operations$Delete, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        delete(params?: Params$Resource$Projects$Locations$Operations$Delete, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$Empty>>;
        delete(params: Params$Resource$Projects$Locations$Operations$Delete, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        delete(params: Params$Resource$Projects$Locations$Operations$Delete, options: MethodOptions | BodyResponseCallback<Schema$Empty>, callback: BodyResponseCallback<Schema$Empty>): void;
        delete(params: Params$Resource$Projects$Locations$Operations$Delete, callback: BodyResponseCallback<Schema$Empty>): void;
        delete(callback: BodyResponseCallback<Schema$Empty>): void;
        /**
         * Gets the latest state of a long-running operation. Clients can use this method to poll the operation result at intervals as recommended by the API service.
         * @example
         * ```js
         * // Before running the sample:
         * // - Enable the API at:
         * //   https://console.developers.google.com/apis/api/config.googleapis.com
         * // - Login into gcloud by running:
         * //   ```sh
         * //   $ gcloud auth application-default login
         * //   ```
         * // - Install the npm module by running:
         * //   ```sh
         * //   $ npm install googleapis
         * //   ```
         *
         * const {google} = require('googleapis');
         * const config = google.config('v1');
         *
         * async function main() {
         *   const auth = new google.auth.GoogleAuth({
         *     // Scopes can be specified either as an array or as a single, space-delimited string.
         *     scopes: ['https://www.googleapis.com/auth/cloud-platform'],
         *   });
         *
         *   // Acquire an auth client, and bind it to all future calls
         *   const authClient = await auth.getClient();
         *   google.options({auth: authClient});
         *
         *   // Do the magic
         *   const res = await config.projects.locations.operations.get({
         *     // The name of the operation resource.
         *     name: 'projects/my-project/locations/my-location/operations/my-operation',
         *   });
         *   console.log(res.data);
         *
         *   // Example response
         *   // {
         *   //   "done": false,
         *   //   "error": {},
         *   //   "metadata": {},
         *   //   "name": "my_name",
         *   //   "response": {}
         *   // }
         * }
         *
         * main().catch(e => {
         *   console.error(e);
         *   throw e;
         * });
         *
         * ```
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        get(params: Params$Resource$Projects$Locations$Operations$Get, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        get(params?: Params$Resource$Projects$Locations$Operations$Get, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$Operation>>;
        get(params: Params$Resource$Projects$Locations$Operations$Get, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        get(params: Params$Resource$Projects$Locations$Operations$Get, options: MethodOptions | BodyResponseCallback<Schema$Operation>, callback: BodyResponseCallback<Schema$Operation>): void;
        get(params: Params$Resource$Projects$Locations$Operations$Get, callback: BodyResponseCallback<Schema$Operation>): void;
        get(callback: BodyResponseCallback<Schema$Operation>): void;
        /**
         * Lists operations that match the specified filter in the request. If the server doesn't support this method, it returns `UNIMPLEMENTED`.
         * @example
         * ```js
         * // Before running the sample:
         * // - Enable the API at:
         * //   https://console.developers.google.com/apis/api/config.googleapis.com
         * // - Login into gcloud by running:
         * //   ```sh
         * //   $ gcloud auth application-default login
         * //   ```
         * // - Install the npm module by running:
         * //   ```sh
         * //   $ npm install googleapis
         * //   ```
         *
         * const {google} = require('googleapis');
         * const config = google.config('v1');
         *
         * async function main() {
         *   const auth = new google.auth.GoogleAuth({
         *     // Scopes can be specified either as an array or as a single, space-delimited string.
         *     scopes: ['https://www.googleapis.com/auth/cloud-platform'],
         *   });
         *
         *   // Acquire an auth client, and bind it to all future calls
         *   const authClient = await auth.getClient();
         *   google.options({auth: authClient});
         *
         *   // Do the magic
         *   const res = await config.projects.locations.operations.list({
         *     // The standard list filter.
         *     filter: 'placeholder-value',
         *     // The name of the operation's parent resource.
         *     name: 'projects/my-project/locations/my-location',
         *     // The standard list page size.
         *     pageSize: 'placeholder-value',
         *     // The standard list page token.
         *     pageToken: 'placeholder-value',
         *   });
         *   console.log(res.data);
         *
         *   // Example response
         *   // {
         *   //   "nextPageToken": "my_nextPageToken",
         *   //   "operations": []
         *   // }
         * }
         *
         * main().catch(e => {
         *   console.error(e);
         *   throw e;
         * });
         *
         * ```
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        list(params: Params$Resource$Projects$Locations$Operations$List, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        list(params?: Params$Resource$Projects$Locations$Operations$List, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$ListOperationsResponse>>;
        list(params: Params$Resource$Projects$Locations$Operations$List, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        list(params: Params$Resource$Projects$Locations$Operations$List, options: MethodOptions | BodyResponseCallback<Schema$ListOperationsResponse>, callback: BodyResponseCallback<Schema$ListOperationsResponse>): void;
        list(params: Params$Resource$Projects$Locations$Operations$List, callback: BodyResponseCallback<Schema$ListOperationsResponse>): void;
        list(callback: BodyResponseCallback<Schema$ListOperationsResponse>): void;
    }
    export interface Params$Resource$Projects$Locations$Operations$Cancel extends StandardParameters {
        /**
         * The name of the operation resource to be cancelled.
         */
        name?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$CancelOperationRequest;
    }
    export interface Params$Resource$Projects$Locations$Operations$Delete extends StandardParameters {
        /**
         * The name of the operation resource to be deleted.
         */
        name?: string;
    }
    export interface Params$Resource$Projects$Locations$Operations$Get extends StandardParameters {
        /**
         * The name of the operation resource.
         */
        name?: string;
    }
    export interface Params$Resource$Projects$Locations$Operations$List extends StandardParameters {
        /**
         * The standard list filter.
         */
        filter?: string;
        /**
         * The name of the operation's parent resource.
         */
        name?: string;
        /**
         * The standard list page size.
         */
        pageSize?: number;
        /**
         * The standard list page token.
         */
        pageToken?: string;
    }
    export class Resource$Projects$Locations$Previews {
        context: APIRequestContext;
        resourceChanges: Resource$Projects$Locations$Previews$Resourcechanges;
        resourceDrifts: Resource$Projects$Locations$Previews$Resourcedrifts;
        constructor(context: APIRequestContext);
        /**
         * Creates a Preview.
         * @example
         * ```js
         * // Before running the sample:
         * // - Enable the API at:
         * //   https://console.developers.google.com/apis/api/config.googleapis.com
         * // - Login into gcloud by running:
         * //   ```sh
         * //   $ gcloud auth application-default login
         * //   ```
         * // - Install the npm module by running:
         * //   ```sh
         * //   $ npm install googleapis
         * //   ```
         *
         * const {google} = require('googleapis');
         * const config = google.config('v1');
         *
         * async function main() {
         *   const auth = new google.auth.GoogleAuth({
         *     // Scopes can be specified either as an array or as a single, space-delimited string.
         *     scopes: ['https://www.googleapis.com/auth/cloud-platform'],
         *   });
         *
         *   // Acquire an auth client, and bind it to all future calls
         *   const authClient = await auth.getClient();
         *   google.options({auth: authClient});
         *
         *   // Do the magic
         *   const res = await config.projects.locations.previews.create({
         *     // Required. The parent in whose context the Preview is created. The parent value is in the format: 'projects/{project_id\}/locations/{location\}'.
         *     parent: 'projects/my-project/locations/my-location',
         *     // Optional. The preview ID.
         *     previewId: 'placeholder-value',
         *     // Optional. An optional request ID to identify requests. Specify a unique request ID so that if you must retry your request, the server will know to ignore the request if it has already been completed. The server will guarantee that for at least 60 minutes since the first request. For example, consider a situation where you make an initial request and the request times out. If you make the request again with the same request ID, the server can check if original operation with the same request ID was received, and if so, will ignore the second request. This prevents clients from accidentally creating duplicate commitments. The request ID must be a valid UUID with the exception that zero UUID is not supported (00000000-0000-0000-0000-000000000000).
         *     requestId: 'placeholder-value',
         *
         *     // Request body metadata
         *     requestBody: {
         *       // request body parameters
         *       // {
         *       //   "annotations": {},
         *       //   "artifactsGcsBucket": "my_artifactsGcsBucket",
         *       //   "build": "my_build",
         *       //   "createTime": "my_createTime",
         *       //   "deployment": "my_deployment",
         *       //   "errorCode": "my_errorCode",
         *       //   "errorLogs": "my_errorLogs",
         *       //   "errorStatus": {},
         *       //   "labels": {},
         *       //   "logs": "my_logs",
         *       //   "name": "my_name",
         *       //   "previewArtifacts": {},
         *       //   "previewMode": "my_previewMode",
         *       //   "serviceAccount": "my_serviceAccount",
         *       //   "state": "my_state",
         *       //   "terraformBlueprint": {},
         *       //   "tfErrors": [],
         *       //   "tfVersion": "my_tfVersion",
         *       //   "tfVersionConstraint": "my_tfVersionConstraint",
         *       //   "workerPool": "my_workerPool"
         *       // }
         *     },
         *   });
         *   console.log(res.data);
         *
         *   // Example response
         *   // {
         *   //   "done": false,
         *   //   "error": {},
         *   //   "metadata": {},
         *   //   "name": "my_name",
         *   //   "response": {}
         *   // }
         * }
         *
         * main().catch(e => {
         *   console.error(e);
         *   throw e;
         * });
         *
         * ```
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        create(params: Params$Resource$Projects$Locations$Previews$Create, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        create(params?: Params$Resource$Projects$Locations$Previews$Create, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$Operation>>;
        create(params: Params$Resource$Projects$Locations$Previews$Create, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        create(params: Params$Resource$Projects$Locations$Previews$Create, options: MethodOptions | BodyResponseCallback<Schema$Operation>, callback: BodyResponseCallback<Schema$Operation>): void;
        create(params: Params$Resource$Projects$Locations$Previews$Create, callback: BodyResponseCallback<Schema$Operation>): void;
        create(callback: BodyResponseCallback<Schema$Operation>): void;
        /**
         * Deletes a Preview.
         * @example
         * ```js
         * // Before running the sample:
         * // - Enable the API at:
         * //   https://console.developers.google.com/apis/api/config.googleapis.com
         * // - Login into gcloud by running:
         * //   ```sh
         * //   $ gcloud auth application-default login
         * //   ```
         * // - Install the npm module by running:
         * //   ```sh
         * //   $ npm install googleapis
         * //   ```
         *
         * const {google} = require('googleapis');
         * const config = google.config('v1');
         *
         * async function main() {
         *   const auth = new google.auth.GoogleAuth({
         *     // Scopes can be specified either as an array or as a single, space-delimited string.
         *     scopes: ['https://www.googleapis.com/auth/cloud-platform'],
         *   });
         *
         *   // Acquire an auth client, and bind it to all future calls
         *   const authClient = await auth.getClient();
         *   google.options({auth: authClient});
         *
         *   // Do the magic
         *   const res = await config.projects.locations.previews.delete({
         *     // Required. The name of the Preview in the format: 'projects/{project_id\}/locations/{location\}/previews/{preview\}'.
         *     name: 'projects/my-project/locations/my-location/previews/my-preview',
         *     // Optional. An optional request ID to identify requests. Specify a unique request ID so that if you must retry your request, the server will know to ignore the request if it has already been completed. The server will guarantee that for at least 60 minutes after the first request. For example, consider a situation where you make an initial request and the request times out. If you make the request again with the same request ID, the server can check if original operation with the same request ID was received, and if so, will ignore the second request. This prevents clients from accidentally creating duplicate commitments. The request ID must be a valid UUID with the exception that zero UUID is not supported (00000000-0000-0000-0000-000000000000).
         *     requestId: 'placeholder-value',
         *   });
         *   console.log(res.data);
         *
         *   // Example response
         *   // {
         *   //   "done": false,
         *   //   "error": {},
         *   //   "metadata": {},
         *   //   "name": "my_name",
         *   //   "response": {}
         *   // }
         * }
         *
         * main().catch(e => {
         *   console.error(e);
         *   throw e;
         * });
         *
         * ```
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        delete(params: Params$Resource$Projects$Locations$Previews$Delete, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        delete(params?: Params$Resource$Projects$Locations$Previews$Delete, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$Operation>>;
        delete(params: Params$Resource$Projects$Locations$Previews$Delete, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        delete(params: Params$Resource$Projects$Locations$Previews$Delete, options: MethodOptions | BodyResponseCallback<Schema$Operation>, callback: BodyResponseCallback<Schema$Operation>): void;
        delete(params: Params$Resource$Projects$Locations$Previews$Delete, callback: BodyResponseCallback<Schema$Operation>): void;
        delete(callback: BodyResponseCallback<Schema$Operation>): void;
        /**
         * Export Preview results.
         * @example
         * ```js
         * // Before running the sample:
         * // - Enable the API at:
         * //   https://console.developers.google.com/apis/api/config.googleapis.com
         * // - Login into gcloud by running:
         * //   ```sh
         * //   $ gcloud auth application-default login
         * //   ```
         * // - Install the npm module by running:
         * //   ```sh
         * //   $ npm install googleapis
         * //   ```
         *
         * const {google} = require('googleapis');
         * const config = google.config('v1');
         *
         * async function main() {
         *   const auth = new google.auth.GoogleAuth({
         *     // Scopes can be specified either as an array or as a single, space-delimited string.
         *     scopes: ['https://www.googleapis.com/auth/cloud-platform'],
         *   });
         *
         *   // Acquire an auth client, and bind it to all future calls
         *   const authClient = await auth.getClient();
         *   google.options({auth: authClient});
         *
         *   // Do the magic
         *   const res = await config.projects.locations.previews.export({
         *     // Required. The preview whose results should be exported. The preview value is in the format: 'projects/{project_id\}/locations/{location\}/previews/{preview\}'.
         *     parent: 'projects/my-project/locations/my-location/previews/my-preview',
         *
         *     // Request body metadata
         *     requestBody: {
         *       // request body parameters
         *       // {}
         *     },
         *   });
         *   console.log(res.data);
         *
         *   // Example response
         *   // {
         *   //   "result": {}
         *   // }
         * }
         *
         * main().catch(e => {
         *   console.error(e);
         *   throw e;
         * });
         *
         * ```
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        export(params: Params$Resource$Projects$Locations$Previews$Export, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        export(params?: Params$Resource$Projects$Locations$Previews$Export, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$ExportPreviewResultResponse>>;
        export(params: Params$Resource$Projects$Locations$Previews$Export, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        export(params: Params$Resource$Projects$Locations$Previews$Export, options: MethodOptions | BodyResponseCallback<Schema$ExportPreviewResultResponse>, callback: BodyResponseCallback<Schema$ExportPreviewResultResponse>): void;
        export(params: Params$Resource$Projects$Locations$Previews$Export, callback: BodyResponseCallback<Schema$ExportPreviewResultResponse>): void;
        export(callback: BodyResponseCallback<Schema$ExportPreviewResultResponse>): void;
        /**
         * Gets details about a Preview.
         * @example
         * ```js
         * // Before running the sample:
         * // - Enable the API at:
         * //   https://console.developers.google.com/apis/api/config.googleapis.com
         * // - Login into gcloud by running:
         * //   ```sh
         * //   $ gcloud auth application-default login
         * //   ```
         * // - Install the npm module by running:
         * //   ```sh
         * //   $ npm install googleapis
         * //   ```
         *
         * const {google} = require('googleapis');
         * const config = google.config('v1');
         *
         * async function main() {
         *   const auth = new google.auth.GoogleAuth({
         *     // Scopes can be specified either as an array or as a single, space-delimited string.
         *     scopes: ['https://www.googleapis.com/auth/cloud-platform'],
         *   });
         *
         *   // Acquire an auth client, and bind it to all future calls
         *   const authClient = await auth.getClient();
         *   google.options({auth: authClient});
         *
         *   // Do the magic
         *   const res = await config.projects.locations.previews.get({
         *     // Required. The name of the preview. Format: 'projects/{project_id\}/locations/{location\}/previews/{preview\}'.
         *     name: 'projects/my-project/locations/my-location/previews/my-preview',
         *   });
         *   console.log(res.data);
         *
         *   // Example response
         *   // {
         *   //   "annotations": {},
         *   //   "artifactsGcsBucket": "my_artifactsGcsBucket",
         *   //   "build": "my_build",
         *   //   "createTime": "my_createTime",
         *   //   "deployment": "my_deployment",
         *   //   "errorCode": "my_errorCode",
         *   //   "errorLogs": "my_errorLogs",
         *   //   "errorStatus": {},
         *   //   "labels": {},
         *   //   "logs": "my_logs",
         *   //   "name": "my_name",
         *   //   "previewArtifacts": {},
         *   //   "previewMode": "my_previewMode",
         *   //   "serviceAccount": "my_serviceAccount",
         *   //   "state": "my_state",
         *   //   "terraformBlueprint": {},
         *   //   "tfErrors": [],
         *   //   "tfVersion": "my_tfVersion",
         *   //   "tfVersionConstraint": "my_tfVersionConstraint",
         *   //   "workerPool": "my_workerPool"
         *   // }
         * }
         *
         * main().catch(e => {
         *   console.error(e);
         *   throw e;
         * });
         *
         * ```
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        get(params: Params$Resource$Projects$Locations$Previews$Get, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        get(params?: Params$Resource$Projects$Locations$Previews$Get, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$Preview>>;
        get(params: Params$Resource$Projects$Locations$Previews$Get, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        get(params: Params$Resource$Projects$Locations$Previews$Get, options: MethodOptions | BodyResponseCallback<Schema$Preview>, callback: BodyResponseCallback<Schema$Preview>): void;
        get(params: Params$Resource$Projects$Locations$Previews$Get, callback: BodyResponseCallback<Schema$Preview>): void;
        get(callback: BodyResponseCallback<Schema$Preview>): void;
        /**
         * Lists Previews in a given project and location.
         * @example
         * ```js
         * // Before running the sample:
         * // - Enable the API at:
         * //   https://console.developers.google.com/apis/api/config.googleapis.com
         * // - Login into gcloud by running:
         * //   ```sh
         * //   $ gcloud auth application-default login
         * //   ```
         * // - Install the npm module by running:
         * //   ```sh
         * //   $ npm install googleapis
         * //   ```
         *
         * const {google} = require('googleapis');
         * const config = google.config('v1');
         *
         * async function main() {
         *   const auth = new google.auth.GoogleAuth({
         *     // Scopes can be specified either as an array or as a single, space-delimited string.
         *     scopes: ['https://www.googleapis.com/auth/cloud-platform'],
         *   });
         *
         *   // Acquire an auth client, and bind it to all future calls
         *   const authClient = await auth.getClient();
         *   google.options({auth: authClient});
         *
         *   // Do the magic
         *   const res = await config.projects.locations.previews.list({
         *     // Optional. Lists the Deployments that match the filter expression. A filter expression filters the resources listed in the response. The expression must be of the form '{field\} {operator\} {value\}' where operators: '<', '\>', '<=', '\>=', '!=', '=', ':' are supported (colon ':' represents a HAS operator which is roughly synonymous with equality). {field\} can refer to a proto or JSON field, or a synthetic field. Field names can be camelCase or snake_case. Examples: - Filter by name: name = "projects/foo/locations/us-central1/deployments/bar - Filter by labels: - Resources that have a key called 'foo' labels.foo:* - Resources that have a key called 'foo' whose value is 'bar' labels.foo = bar - Filter by state: - Deployments in CREATING state. state=CREATING
         *     filter: 'placeholder-value',
         *     // Optional. Field to use to sort the list.
         *     orderBy: 'placeholder-value',
         *     // Optional. When requesting a page of resources, 'page_size' specifies number of resources to return. If unspecified, at most 500 will be returned. The maximum value is 1000.
         *     pageSize: 'placeholder-value',
         *     // Optional. Token returned by previous call to 'ListDeployments' which specifies the position in the list from where to continue listing the resources.
         *     pageToken: 'placeholder-value',
         *     // Required. The parent in whose context the Previews are listed. The parent value is in the format: 'projects/{project_id\}/locations/{location\}'.
         *     parent: 'projects/my-project/locations/my-location',
         *   });
         *   console.log(res.data);
         *
         *   // Example response
         *   // {
         *   //   "nextPageToken": "my_nextPageToken",
         *   //   "previews": [],
         *   //   "unreachable": []
         *   // }
         * }
         *
         * main().catch(e => {
         *   console.error(e);
         *   throw e;
         * });
         *
         * ```
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        list(params: Params$Resource$Projects$Locations$Previews$List, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        list(params?: Params$Resource$Projects$Locations$Previews$List, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$ListPreviewsResponse>>;
        list(params: Params$Resource$Projects$Locations$Previews$List, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        list(params: Params$Resource$Projects$Locations$Previews$List, options: MethodOptions | BodyResponseCallback<Schema$ListPreviewsResponse>, callback: BodyResponseCallback<Schema$ListPreviewsResponse>): void;
        list(params: Params$Resource$Projects$Locations$Previews$List, callback: BodyResponseCallback<Schema$ListPreviewsResponse>): void;
        list(callback: BodyResponseCallback<Schema$ListPreviewsResponse>): void;
    }
    export interface Params$Resource$Projects$Locations$Previews$Create extends StandardParameters {
        /**
         * Required. The parent in whose context the Preview is created. The parent value is in the format: 'projects/{project_id\}/locations/{location\}'.
         */
        parent?: string;
        /**
         * Optional. The preview ID.
         */
        previewId?: string;
        /**
         * Optional. An optional request ID to identify requests. Specify a unique request ID so that if you must retry your request, the server will know to ignore the request if it has already been completed. The server will guarantee that for at least 60 minutes since the first request. For example, consider a situation where you make an initial request and the request times out. If you make the request again with the same request ID, the server can check if original operation with the same request ID was received, and if so, will ignore the second request. This prevents clients from accidentally creating duplicate commitments. The request ID must be a valid UUID with the exception that zero UUID is not supported (00000000-0000-0000-0000-000000000000).
         */
        requestId?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$Preview;
    }
    export interface Params$Resource$Projects$Locations$Previews$Delete extends StandardParameters {
        /**
         * Required. The name of the Preview in the format: 'projects/{project_id\}/locations/{location\}/previews/{preview\}'.
         */
        name?: string;
        /**
         * Optional. An optional request ID to identify requests. Specify a unique request ID so that if you must retry your request, the server will know to ignore the request if it has already been completed. The server will guarantee that for at least 60 minutes after the first request. For example, consider a situation where you make an initial request and the request times out. If you make the request again with the same request ID, the server can check if original operation with the same request ID was received, and if so, will ignore the second request. This prevents clients from accidentally creating duplicate commitments. The request ID must be a valid UUID with the exception that zero UUID is not supported (00000000-0000-0000-0000-000000000000).
         */
        requestId?: string;
    }
    export interface Params$Resource$Projects$Locations$Previews$Export extends StandardParameters {
        /**
         * Required. The preview whose results should be exported. The preview value is in the format: 'projects/{project_id\}/locations/{location\}/previews/{preview\}'.
         */
        parent?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$ExportPreviewResultRequest;
    }
    export interface Params$Resource$Projects$Locations$Previews$Get extends StandardParameters {
        /**
         * Required. The name of the preview. Format: 'projects/{project_id\}/locations/{location\}/previews/{preview\}'.
         */
        name?: string;
    }
    export interface Params$Resource$Projects$Locations$Previews$List extends StandardParameters {
        /**
         * Optional. Lists the Deployments that match the filter expression. A filter expression filters the resources listed in the response. The expression must be of the form '{field\} {operator\} {value\}' where operators: '<', '\>', '<=', '\>=', '!=', '=', ':' are supported (colon ':' represents a HAS operator which is roughly synonymous with equality). {field\} can refer to a proto or JSON field, or a synthetic field. Field names can be camelCase or snake_case. Examples: - Filter by name: name = "projects/foo/locations/us-central1/deployments/bar - Filter by labels: - Resources that have a key called 'foo' labels.foo:* - Resources that have a key called 'foo' whose value is 'bar' labels.foo = bar - Filter by state: - Deployments in CREATING state. state=CREATING
         */
        filter?: string;
        /**
         * Optional. Field to use to sort the list.
         */
        orderBy?: string;
        /**
         * Optional. When requesting a page of resources, 'page_size' specifies number of resources to return. If unspecified, at most 500 will be returned. The maximum value is 1000.
         */
        pageSize?: number;
        /**
         * Optional. Token returned by previous call to 'ListDeployments' which specifies the position in the list from where to continue listing the resources.
         */
        pageToken?: string;
        /**
         * Required. The parent in whose context the Previews are listed. The parent value is in the format: 'projects/{project_id\}/locations/{location\}'.
         */
        parent?: string;
    }
    export class Resource$Projects$Locations$Previews$Resourcechanges {
        context: APIRequestContext;
        constructor(context: APIRequestContext);
        /**
         * Get a ResourceChange for a given preview.
         * @example
         * ```js
         * // Before running the sample:
         * // - Enable the API at:
         * //   https://console.developers.google.com/apis/api/config.googleapis.com
         * // - Login into gcloud by running:
         * //   ```sh
         * //   $ gcloud auth application-default login
         * //   ```
         * // - Install the npm module by running:
         * //   ```sh
         * //   $ npm install googleapis
         * //   ```
         *
         * const {google} = require('googleapis');
         * const config = google.config('v1');
         *
         * async function main() {
         *   const auth = new google.auth.GoogleAuth({
         *     // Scopes can be specified either as an array or as a single, space-delimited string.
         *     scopes: ['https://www.googleapis.com/auth/cloud-platform'],
         *   });
         *
         *   // Acquire an auth client, and bind it to all future calls
         *   const authClient = await auth.getClient();
         *   google.options({auth: authClient});
         *
         *   // Do the magic
         *   const res = await config.projects.locations.previews.resourceChanges.get({
         *     // Required. The name of the resource change to retrieve. Format: 'projects/{project_id\}/locations/{location\}/previews/{preview\}/resourceChanges/{resource_change\}'.
         *     name: 'projects/my-project/locations/my-location/previews/my-preview/resourceChanges/my-resourceChange',
         *   });
         *   console.log(res.data);
         *
         *   // Example response
         *   // {
         *   //   "intent": "my_intent",
         *   //   "name": "my_name",
         *   //   "propertyChanges": [],
         *   //   "terraformInfo": {}
         *   // }
         * }
         *
         * main().catch(e => {
         *   console.error(e);
         *   throw e;
         * });
         *
         * ```
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        get(params: Params$Resource$Projects$Locations$Previews$Resourcechanges$Get, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        get(params?: Params$Resource$Projects$Locations$Previews$Resourcechanges$Get, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$ResourceChange>>;
        get(params: Params$Resource$Projects$Locations$Previews$Resourcechanges$Get, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        get(params: Params$Resource$Projects$Locations$Previews$Resourcechanges$Get, options: MethodOptions | BodyResponseCallback<Schema$ResourceChange>, callback: BodyResponseCallback<Schema$ResourceChange>): void;
        get(params: Params$Resource$Projects$Locations$Previews$Resourcechanges$Get, callback: BodyResponseCallback<Schema$ResourceChange>): void;
        get(callback: BodyResponseCallback<Schema$ResourceChange>): void;
        /**
         * Lists ResourceChanges for a given preview.
         * @example
         * ```js
         * // Before running the sample:
         * // - Enable the API at:
         * //   https://console.developers.google.com/apis/api/config.googleapis.com
         * // - Login into gcloud by running:
         * //   ```sh
         * //   $ gcloud auth application-default login
         * //   ```
         * // - Install the npm module by running:
         * //   ```sh
         * //   $ npm install googleapis
         * //   ```
         *
         * const {google} = require('googleapis');
         * const config = google.config('v1');
         *
         * async function main() {
         *   const auth = new google.auth.GoogleAuth({
         *     // Scopes can be specified either as an array or as a single, space-delimited string.
         *     scopes: ['https://www.googleapis.com/auth/cloud-platform'],
         *   });
         *
         *   // Acquire an auth client, and bind it to all future calls
         *   const authClient = await auth.getClient();
         *   google.options({auth: authClient});
         *
         *   // Do the magic
         *   const res = await config.projects.locations.previews.resourceChanges.list({
         *     // Optional. Lists the resource changes that match the filter expression. A filter expression filters the resource changes listed in the response. The expression must be of the form '{field\} {operator\} {value\}' where operators: '<', '\>', '<=', '\>=', '!=', '=', ':' are supported (colon ':' represents a HAS operator which is roughly synonymous with equality). {field\} can refer to a proto or JSON field, or a synthetic field. Field names can be camelCase or snake_case. Examples: - Filter by name: name = "projects/foo/locations/us-central1/previews/dep/resourceChanges/baz
         *     filter: 'placeholder-value',
         *     // Optional. Field to use to sort the list.
         *     orderBy: 'placeholder-value',
         *     // Optional. When requesting a page of resource changes, 'page_size' specifies number of resource changes to return. If unspecified, at most 500 will be returned. The maximum value is 1000.
         *     pageSize: 'placeholder-value',
         *     // Optional. Token returned by previous call to 'ListResourceChanges' which specifies the position in the list from where to continue listing the resource changes.
         *     pageToken: 'placeholder-value',
         *     // Required. The parent in whose context the ResourceChanges are listed. The parent value is in the format: 'projects/{project_id\}/locations/{location\}/previews/{preview\}'.
         *     parent: 'projects/my-project/locations/my-location/previews/my-preview',
         *   });
         *   console.log(res.data);
         *
         *   // Example response
         *   // {
         *   //   "nextPageToken": "my_nextPageToken",
         *   //   "resourceChanges": [],
         *   //   "unreachable": []
         *   // }
         * }
         *
         * main().catch(e => {
         *   console.error(e);
         *   throw e;
         * });
         *
         * ```
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        list(params: Params$Resource$Projects$Locations$Previews$Resourcechanges$List, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        list(params?: Params$Resource$Projects$Locations$Previews$Resourcechanges$List, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$ListResourceChangesResponse>>;
        list(params: Params$Resource$Projects$Locations$Previews$Resourcechanges$List, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        list(params: Params$Resource$Projects$Locations$Previews$Resourcechanges$List, options: MethodOptions | BodyResponseCallback<Schema$ListResourceChangesResponse>, callback: BodyResponseCallback<Schema$ListResourceChangesResponse>): void;
        list(params: Params$Resource$Projects$Locations$Previews$Resourcechanges$List, callback: BodyResponseCallback<Schema$ListResourceChangesResponse>): void;
        list(callback: BodyResponseCallback<Schema$ListResourceChangesResponse>): void;
    }
    export interface Params$Resource$Projects$Locations$Previews$Resourcechanges$Get extends StandardParameters {
        /**
         * Required. The name of the resource change to retrieve. Format: 'projects/{project_id\}/locations/{location\}/previews/{preview\}/resourceChanges/{resource_change\}'.
         */
        name?: string;
    }
    export interface Params$Resource$Projects$Locations$Previews$Resourcechanges$List extends StandardParameters {
        /**
         * Optional. Lists the resource changes that match the filter expression. A filter expression filters the resource changes listed in the response. The expression must be of the form '{field\} {operator\} {value\}' where operators: '<', '\>', '<=', '\>=', '!=', '=', ':' are supported (colon ':' represents a HAS operator which is roughly synonymous with equality). {field\} can refer to a proto or JSON field, or a synthetic field. Field names can be camelCase or snake_case. Examples: - Filter by name: name = "projects/foo/locations/us-central1/previews/dep/resourceChanges/baz
         */
        filter?: string;
        /**
         * Optional. Field to use to sort the list.
         */
        orderBy?: string;
        /**
         * Optional. When requesting a page of resource changes, 'page_size' specifies number of resource changes to return. If unspecified, at most 500 will be returned. The maximum value is 1000.
         */
        pageSize?: number;
        /**
         * Optional. Token returned by previous call to 'ListResourceChanges' which specifies the position in the list from where to continue listing the resource changes.
         */
        pageToken?: string;
        /**
         * Required. The parent in whose context the ResourceChanges are listed. The parent value is in the format: 'projects/{project_id\}/locations/{location\}/previews/{preview\}'.
         */
        parent?: string;
    }
    export class Resource$Projects$Locations$Previews$Resourcedrifts {
        context: APIRequestContext;
        constructor(context: APIRequestContext);
        /**
         * Get a ResourceDrift for a given preview.
         * @example
         * ```js
         * // Before running the sample:
         * // - Enable the API at:
         * //   https://console.developers.google.com/apis/api/config.googleapis.com
         * // - Login into gcloud by running:
         * //   ```sh
         * //   $ gcloud auth application-default login
         * //   ```
         * // - Install the npm module by running:
         * //   ```sh
         * //   $ npm install googleapis
         * //   ```
         *
         * const {google} = require('googleapis');
         * const config = google.config('v1');
         *
         * async function main() {
         *   const auth = new google.auth.GoogleAuth({
         *     // Scopes can be specified either as an array or as a single, space-delimited string.
         *     scopes: ['https://www.googleapis.com/auth/cloud-platform'],
         *   });
         *
         *   // Acquire an auth client, and bind it to all future calls
         *   const authClient = await auth.getClient();
         *   google.options({auth: authClient});
         *
         *   // Do the magic
         *   const res = await config.projects.locations.previews.resourceDrifts.get({
         *     // Required. The name of the resource drift to retrieve. Format: 'projects/{project_id\}/locations/{location\}/previews/{preview\}/resourceDrifts/{resource_drift\}'.
         *     name: 'projects/my-project/locations/my-location/previews/my-preview/resourceDrifts/my-resourceDrift',
         *   });
         *   console.log(res.data);
         *
         *   // Example response
         *   // {
         *   //   "name": "my_name",
         *   //   "propertyDrifts": [],
         *   //   "terraformInfo": {}
         *   // }
         * }
         *
         * main().catch(e => {
         *   console.error(e);
         *   throw e;
         * });
         *
         * ```
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        get(params: Params$Resource$Projects$Locations$Previews$Resourcedrifts$Get, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        get(params?: Params$Resource$Projects$Locations$Previews$Resourcedrifts$Get, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$ResourceDrift>>;
        get(params: Params$Resource$Projects$Locations$Previews$Resourcedrifts$Get, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        get(params: Params$Resource$Projects$Locations$Previews$Resourcedrifts$Get, options: MethodOptions | BodyResponseCallback<Schema$ResourceDrift>, callback: BodyResponseCallback<Schema$ResourceDrift>): void;
        get(params: Params$Resource$Projects$Locations$Previews$Resourcedrifts$Get, callback: BodyResponseCallback<Schema$ResourceDrift>): void;
        get(callback: BodyResponseCallback<Schema$ResourceDrift>): void;
        /**
         * List ResourceDrifts for a given preview.
         * @example
         * ```js
         * // Before running the sample:
         * // - Enable the API at:
         * //   https://console.developers.google.com/apis/api/config.googleapis.com
         * // - Login into gcloud by running:
         * //   ```sh
         * //   $ gcloud auth application-default login
         * //   ```
         * // - Install the npm module by running:
         * //   ```sh
         * //   $ npm install googleapis
         * //   ```
         *
         * const {google} = require('googleapis');
         * const config = google.config('v1');
         *
         * async function main() {
         *   const auth = new google.auth.GoogleAuth({
         *     // Scopes can be specified either as an array or as a single, space-delimited string.
         *     scopes: ['https://www.googleapis.com/auth/cloud-platform'],
         *   });
         *
         *   // Acquire an auth client, and bind it to all future calls
         *   const authClient = await auth.getClient();
         *   google.options({auth: authClient});
         *
         *   // Do the magic
         *   const res = await config.projects.locations.previews.resourceDrifts.list({
         *     // Optional. Lists the resource drifts that match the filter expression. A filter expression filters the resource drifts listed in the response. The expression must be of the form '{field\} {operator\} {value\}' where operators: '<', '\>', '<=', '\>=', '!=', '=', ':' are supported (colon ':' represents a HAS operator which is roughly synonymous with equality). {field\} can refer to a proto or JSON field, or a synthetic field. Field names can be camelCase or snake_case. Examples: - Filter by name: name = "projects/foo/locations/us-central1/previews/dep/resourceDrifts/baz
         *     filter: 'placeholder-value',
         *     // Optional. Field to use to sort the list.
         *     orderBy: 'placeholder-value',
         *     // Optional. When requesting a page of resource drifts, 'page_size' specifies number of resource drifts to return. If unspecified, at most 500 will be returned. The maximum value is 1000.
         *     pageSize: 'placeholder-value',
         *     // Optional. Token returned by previous call to 'ListResourceDrifts' which specifies the position in the list from where to continue listing the resource drifts.
         *     pageToken: 'placeholder-value',
         *     // Required. The parent in whose context the ResourceDrifts are listed. The parent value is in the format: 'projects/{project_id\}/locations/{location\}/previews/{preview\}'.
         *     parent: 'projects/my-project/locations/my-location/previews/my-preview',
         *   });
         *   console.log(res.data);
         *
         *   // Example response
         *   // {
         *   //   "nextPageToken": "my_nextPageToken",
         *   //   "resourceDrifts": [],
         *   //   "unreachable": []
         *   // }
         * }
         *
         * main().catch(e => {
         *   console.error(e);
         *   throw e;
         * });
         *
         * ```
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        list(params: Params$Resource$Projects$Locations$Previews$Resourcedrifts$List, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        list(params?: Params$Resource$Projects$Locations$Previews$Resourcedrifts$List, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$ListResourceDriftsResponse>>;
        list(params: Params$Resource$Projects$Locations$Previews$Resourcedrifts$List, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        list(params: Params$Resource$Projects$Locations$Previews$Resourcedrifts$List, options: MethodOptions | BodyResponseCallback<Schema$ListResourceDriftsResponse>, callback: BodyResponseCallback<Schema$ListResourceDriftsResponse>): void;
        list(params: Params$Resource$Projects$Locations$Previews$Resourcedrifts$List, callback: BodyResponseCallback<Schema$ListResourceDriftsResponse>): void;
        list(callback: BodyResponseCallback<Schema$ListResourceDriftsResponse>): void;
    }
    export interface Params$Resource$Projects$Locations$Previews$Resourcedrifts$Get extends StandardParameters {
        /**
         * Required. The name of the resource drift to retrieve. Format: 'projects/{project_id\}/locations/{location\}/previews/{preview\}/resourceDrifts/{resource_drift\}'.
         */
        name?: string;
    }
    export interface Params$Resource$Projects$Locations$Previews$Resourcedrifts$List extends StandardParameters {
        /**
         * Optional. Lists the resource drifts that match the filter expression. A filter expression filters the resource drifts listed in the response. The expression must be of the form '{field\} {operator\} {value\}' where operators: '<', '\>', '<=', '\>=', '!=', '=', ':' are supported (colon ':' represents a HAS operator which is roughly synonymous with equality). {field\} can refer to a proto or JSON field, or a synthetic field. Field names can be camelCase or snake_case. Examples: - Filter by name: name = "projects/foo/locations/us-central1/previews/dep/resourceDrifts/baz
         */
        filter?: string;
        /**
         * Optional. Field to use to sort the list.
         */
        orderBy?: string;
        /**
         * Optional. When requesting a page of resource drifts, 'page_size' specifies number of resource drifts to return. If unspecified, at most 500 will be returned. The maximum value is 1000.
         */
        pageSize?: number;
        /**
         * Optional. Token returned by previous call to 'ListResourceDrifts' which specifies the position in the list from where to continue listing the resource drifts.
         */
        pageToken?: string;
        /**
         * Required. The parent in whose context the ResourceDrifts are listed. The parent value is in the format: 'projects/{project_id\}/locations/{location\}/previews/{preview\}'.
         */
        parent?: string;
    }
    export class Resource$Projects$Locations$Terraformversions {
        context: APIRequestContext;
        constructor(context: APIRequestContext);
        /**
         * Gets details about a TerraformVersion.
         * @example
         * ```js
         * // Before running the sample:
         * // - Enable the API at:
         * //   https://console.developers.google.com/apis/api/config.googleapis.com
         * // - Login into gcloud by running:
         * //   ```sh
         * //   $ gcloud auth application-default login
         * //   ```
         * // - Install the npm module by running:
         * //   ```sh
         * //   $ npm install googleapis
         * //   ```
         *
         * const {google} = require('googleapis');
         * const config = google.config('v1');
         *
         * async function main() {
         *   const auth = new google.auth.GoogleAuth({
         *     // Scopes can be specified either as an array or as a single, space-delimited string.
         *     scopes: ['https://www.googleapis.com/auth/cloud-platform'],
         *   });
         *
         *   // Acquire an auth client, and bind it to all future calls
         *   const authClient = await auth.getClient();
         *   google.options({auth: authClient});
         *
         *   // Do the magic
         *   const res = await config.projects.locations.terraformVersions.get({
         *     // Required. The name of the TerraformVersion. Format: 'projects/{project_id\}/locations/{location\}/terraformVersions/{terraform_version\}'
         *     name: 'projects/my-project/locations/my-location/terraformVersions/my-terraformVersion',
         *   });
         *   console.log(res.data);
         *
         *   // Example response
         *   // {
         *   //   "deprecateTime": "my_deprecateTime",
         *   //   "name": "my_name",
         *   //   "obsoleteTime": "my_obsoleteTime",
         *   //   "state": "my_state",
         *   //   "supportTime": "my_supportTime"
         *   // }
         * }
         *
         * main().catch(e => {
         *   console.error(e);
         *   throw e;
         * });
         *
         * ```
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        get(params: Params$Resource$Projects$Locations$Terraformversions$Get, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        get(params?: Params$Resource$Projects$Locations$Terraformversions$Get, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$TerraformVersion>>;
        get(params: Params$Resource$Projects$Locations$Terraformversions$Get, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        get(params: Params$Resource$Projects$Locations$Terraformversions$Get, options: MethodOptions | BodyResponseCallback<Schema$TerraformVersion>, callback: BodyResponseCallback<Schema$TerraformVersion>): void;
        get(params: Params$Resource$Projects$Locations$Terraformversions$Get, callback: BodyResponseCallback<Schema$TerraformVersion>): void;
        get(callback: BodyResponseCallback<Schema$TerraformVersion>): void;
        /**
         * Lists TerraformVersions in a given project and location.
         * @example
         * ```js
         * // Before running the sample:
         * // - Enable the API at:
         * //   https://console.developers.google.com/apis/api/config.googleapis.com
         * // - Login into gcloud by running:
         * //   ```sh
         * //   $ gcloud auth application-default login
         * //   ```
         * // - Install the npm module by running:
         * //   ```sh
         * //   $ npm install googleapis
         * //   ```
         *
         * const {google} = require('googleapis');
         * const config = google.config('v1');
         *
         * async function main() {
         *   const auth = new google.auth.GoogleAuth({
         *     // Scopes can be specified either as an array or as a single, space-delimited string.
         *     scopes: ['https://www.googleapis.com/auth/cloud-platform'],
         *   });
         *
         *   // Acquire an auth client, and bind it to all future calls
         *   const authClient = await auth.getClient();
         *   google.options({auth: authClient});
         *
         *   // Do the magic
         *   const res = await config.projects.locations.terraformVersions.list({
         *     // Optional. Lists the TerraformVersions that match the filter expression. A filter expression filters the resources listed in the response. The expression must be of the form '{field\} {operator\} {value\}' where operators: '<', '\>', '<=', '\>=', '!=', '=', ':' are supported (colon ':' represents a HAS operator which is roughly synonymous with equality). {field\} can refer to a proto or JSON field, or a synthetic field. Field names can be camelCase or snake_case.
         *     filter: 'placeholder-value',
         *     // Optional. Field to use to sort the list.
         *     orderBy: 'placeholder-value',
         *     // Optional. When requesting a page of terraform versions, 'page_size' specifies number of terraform versions to return. If unspecified, at most 500 will be returned. The maximum value is 1000.
         *     pageSize: 'placeholder-value',
         *     // Optional. Token returned by previous call to 'ListTerraformVersions' which specifies the position in the list from where to continue listing the terraform versions.
         *     pageToken: 'placeholder-value',
         *     // Required. The parent in whose context the TerraformVersions are listed. The parent value is in the format: 'projects/{project_id\}/locations/{location\}'.
         *     parent: 'projects/my-project/locations/my-location',
         *   });
         *   console.log(res.data);
         *
         *   // Example response
         *   // {
         *   //   "nextPageToken": "my_nextPageToken",
         *   //   "terraformVersions": [],
         *   //   "unreachable": []
         *   // }
         * }
         *
         * main().catch(e => {
         *   console.error(e);
         *   throw e;
         * });
         *
         * ```
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        list(params: Params$Resource$Projects$Locations$Terraformversions$List, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        list(params?: Params$Resource$Projects$Locations$Terraformversions$List, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$ListTerraformVersionsResponse>>;
        list(params: Params$Resource$Projects$Locations$Terraformversions$List, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        list(params: Params$Resource$Projects$Locations$Terraformversions$List, options: MethodOptions | BodyResponseCallback<Schema$ListTerraformVersionsResponse>, callback: BodyResponseCallback<Schema$ListTerraformVersionsResponse>): void;
        list(params: Params$Resource$Projects$Locations$Terraformversions$List, callback: BodyResponseCallback<Schema$ListTerraformVersionsResponse>): void;
        list(callback: BodyResponseCallback<Schema$ListTerraformVersionsResponse>): void;
    }
    export interface Params$Resource$Projects$Locations$Terraformversions$Get extends StandardParameters {
        /**
         * Required. The name of the TerraformVersion. Format: 'projects/{project_id\}/locations/{location\}/terraformVersions/{terraform_version\}'
         */
        name?: string;
    }
    export interface Params$Resource$Projects$Locations$Terraformversions$List extends StandardParameters {
        /**
         * Optional. Lists the TerraformVersions that match the filter expression. A filter expression filters the resources listed in the response. The expression must be of the form '{field\} {operator\} {value\}' where operators: '<', '\>', '<=', '\>=', '!=', '=', ':' are supported (colon ':' represents a HAS operator which is roughly synonymous with equality). {field\} can refer to a proto or JSON field, or a synthetic field. Field names can be camelCase or snake_case.
         */
        filter?: string;
        /**
         * Optional. Field to use to sort the list.
         */
        orderBy?: string;
        /**
         * Optional. When requesting a page of terraform versions, 'page_size' specifies number of terraform versions to return. If unspecified, at most 500 will be returned. The maximum value is 1000.
         */
        pageSize?: number;
        /**
         * Optional. Token returned by previous call to 'ListTerraformVersions' which specifies the position in the list from where to continue listing the terraform versions.
         */
        pageToken?: string;
        /**
         * Required. The parent in whose context the TerraformVersions are listed. The parent value is in the format: 'projects/{project_id\}/locations/{location\}'.
         */
        parent?: string;
    }
    export {};
}
