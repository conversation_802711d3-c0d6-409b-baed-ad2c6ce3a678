import React, { useState, useEffect, useCallback } from "react";
import { motion, AnimatePresence } from "framer-motion";
import {
  AlertTriangle,
  X,
  <PERSON><PERSON>hart3,
  CheckCircle,
  Eye,
  Music,
  RefreshCw,
  Trash2,
  XCircle,
} from "lucide-react";
import { toast } from "react-hot-toast";
import apiService from "@/services/api";

// Definindo tipos de forma mais clara e reutilizável
enum Performance {
  Excellent = "excellent",
  Good = "good",
  Average = "average",
  Poor = "poor",
  Terrible = "terrible",
}

enum Recommendation {
  Keep = "keep",
  Monitor = "monitor",
  Remove = "remove",
  Blacklist = "blacklist",
}

interface ProblematicTrack {
  id: string;
  title: string;
  artist: string;
  youtubeVideoId: string;
  totalVotes: number;
  upvotes: number;
  downvotes: number;
  score: number;
  negativeVoteRatio: number;
  playCount: number;
  skipCount: number;
  completionRate: number;
  performance: Performance;
  recommendation: Recommendation;
}

interface ProblematicTracksReport {
  problematicTracks: ProblematicTrack[];
  recommendations: string[];
  healthScore: number;
}

const ProblematicTracksAlert: React.FC = () => {
  const [report, setReport] = useState<ProblematicTracksReport | null>(null);
  const [loading, setLoading] = useState(true);
  const [showAlert, setShowAlert] = useState(false);
  const [dismissed, setDismissed] = useState(false);
  const restaurantId = "demo-restaurant";

  // Usar useCallback para evitar recriação desnecessária da função
  const loadProblematicTracksReport = useCallback(async () => {
    try {
      setLoading(true);
      const response = await apiService.client.get(
        `/playback/${restaurantId}/problematic-report`
      );
      setReport(response.data.report);
    } catch (error) {
      console.error("Erro ao carregar relatório de músicas problemáticas:", error);
      setReport({
        problematicTracks: [],
        recommendations: [],
        healthScore: 85,
      });
      toast.error("Falha ao carregar relatório");
    } finally {
      setLoading(false);
    }
  }, [restaurantId]);

  // Função para lidar com remoção de música
  const handleRemoveTrack = useCallback(
    async (trackId: string) => {
      try {
        await apiService.client.delete(
          `/restaurants/${restaurantId}/playlist/${trackId}`
        );
        toast.success("Música removida com sucesso");
        await loadProblematicTracksReport();
      } catch (error) {
        console.error("Erro ao remover música:", error);
        toast.error("Erro ao remover música");
      }
    },
    [restaurantId, loadProblematicTracksReport]
  );

  // Função para dispensar alerta
  const handleDismissAlert = useCallback(() => {
    setShowAlert(false);
    setDismissed(true);
    setTimeout(() => setDismissed(false), 60 * 60 * 1000); // 1 hora
  }, []);

  // Carregar relatório e configurar intervalo
  useEffect(() => {
    loadProblematicTracksReport();
    const interval = setInterval(loadProblematicTracksReport, 5 * 60 * 1000);
    return () => clearInterval(interval);
  }, [loadProblematicTracksReport]);

  // Controlar exibição do alerta
  useEffect(() => {
    if (report?.problematicTracks.length && !dismissed) {
      setShowAlert(true);
    }
  }, [report, dismissed]);

  // Função para determinar cor do score de saúde
  const getHealthColor = (score: number): string => {
    if (score >= 80) return "text-green-600";
    if (score >= 60) return "text-yellow-600";
    if (score >= 40) return "text-orange-600";
    return "text-red-600";
  };

  // Função para determinar cor de fundo do score de saúde
  const getHealthBgColor = (score: number): string => {
    if (score >= 80) return "bg-green-50 border-green-200";
    if (score >= 60) return "bg-yellow-50 border-yellow-200";
    if (score >= 40) return "bg-orange-50 border-orange-200";
    return "bg-red-50 border-red-200";
  };

  // Função para determinar cor da recomendação
  const getRecommendationColor = (recommendation: Recommendation): string => {
    switch (recommendation) {
      case Recommendation.Keep:
        return "text-green-600 bg-green-100";
      case Recommendation.Monitor:
        return "text-yellow-600 bg-yellow-100";
      case Recommendation.Remove:
        return "text-red-600 bg-red-100";
      case Recommendation.Blacklist:
        return "text-red-800 bg-red-200";
      default:
        return "text-gray-600 bg-gray-100";
    }
  };

  // Função para determinar ícone da recomendação
  const getRecommendationIcon = (recommendation: Recommendation) => {
    switch (recommendation) {
      case Recommendation.Keep:
        return <CheckCircle className="w-4 h-4" />;
      case Recommendation.Monitor:
        return <Eye className="w-4 h-4" />;
      case Recommendation.Remove:
        return <XCircle className="w-4 h-4" />;
      case Recommendation.Blacklist:
        return <Trash2 className="w-4 h-4" />;
      default:
        return <Music className="w-4 h-4" />;
    }
  };

  if (loading) {
    return (
      <div className="flex items-center space-x-2 text-gray-600">
        <RefreshCw className="w-4 h-4 animate-spin" />
        <span className="text-sm">Verificando músicas problemáticas...</span>
      </div>
    );
  }

  if (!report || !report.problematicTracks.length) {
    return (
      <div className="flex items-center space-x-2 text-green-600">
        <CheckCircle className="w-4 h-4" />
        <span className="text-sm">
          Playlist saudável - nenhuma música problemática
        </span>
      </div>
    );
  }

  return (
    <>
      {/* Alerta flutuante */}
      <AnimatePresence>
        {showAlert && (
          <motion.div
            initial={{ opacity: 0, y: -50, scale: 0.9 }}
            animate={{ opacity: 1, y: 0, scale: 1 }}
            exit={{ opacity: 0, y: -50, scale: 0.9 }}
            className="fixed top-4 right-4 z-50 max-w-md"
          >
            <div
              className={`rounded-lg border-2 p-4 shadow-lg ${getHealthBgColor(
                report.healthScore
              )}`}
            >
              <div className="flex items-start justify-between">
                <div className="flex items-start space-x-3">
                  <AlertTriangle
                    className={`w-6 h-6 mt-0.5 ${getHealthColor(
                      report.healthScore
                    )}`}
                  />
                  <div className="flex-1">
                    <h3 className="font-semibold text-gray-900">
                      🚨 Músicas Problemáticas Detectadas
                    </h3>
                    <p className="text-sm text-gray-700 mt-1">
                      {report.problematicTracks.length} música(s) com alta taxa de
                      rejeição
                    </p>
                    <div className="mt-2">
                      <div className="flex items-center space-x-2 text-sm">
                        <BarChart3 className="w-4 h-4" />
                        <span
                          className={`font-medium ${getHealthColor(
                            report.healthScore
                          )}`}
                        >
                          Score de Saúde: {report.healthScore}/100
                        </span>
                      </div>
                    </div>
                  </div>
                </div>
                <button
                  onClick={handleDismissAlert}
                  className="text-gray-400 hover:text-gray-600 transition-colors"
                  aria-label="Fechar alerta"
                >
                  <X className="w-5 h-5" />
                </button>
              </div>
              <div className="mt-3 flex space-x-2">
                <button
                  onClick={() => setShowAlert(false)}
                  className="text-sm px-3 py-1 bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors"
                >
                  Ver Detalhes
                </button>
                <button
                  onClick={handleDismissAlert}
                  className="text-sm px-3 py-1 bg-gray-200 text-gray-700 rounded hover:bg-gray-300 transition-colors"
                >
                  Dispensar
                </button>
              </div>
            </div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Painel de detalhes */}
      {!showAlert && (
        <div className="space-y-4">
          <div
            className={`rounded-lg border p-4 ${getHealthBgColor(
              report.healthScore
            )}`}
          >
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-3">
                <AlertTriangle
                  className={`w-6 h-6 ${getHealthColor(report.healthScore)}`}
                />
                <div>
                  <h3 className="font-semibold text-gray-900">
                    Saúde da Playlist
                  </h3>
                  <p className="text-sm text-gray-700">
                    Score:{" "}
                    <span
                      className={`font-medium ${getHealthColor(
                        report.healthScore
                      )}`}
                    >
                      {report.healthScore}/100
                    </span>
                  </p>
                </div>
              </div>
              <button
                onClick={loadProblematicTracksReport}
                className="p-2 text-gray-600 hover:text-gray-800 transition-colors"
                aria-label="Atualizar relatório"
              >
                <RefreshCw className="w-4 h-4" />
              </button>
            </div>

            {report.recommendations.length > 0 && (
              <div className="mt-3">
                <h4 className="text-sm font-medium text-gray-900 mb-2">
                  Recomendações:
                </h4>
                <ul className="space-y-1">
                  {report.recommendations.map((rec, index) => (
                    <li
                      key={index}
                      className="text-sm text-gray-700 flex items-start space-x-2"
                    >
                      <span className="text-gray-400 mt-0.5">•</span>
                      <span>{rec}</span>
                    </li>
                  ))}
                </ul>
              </div>
            )}
          </div>

          <div className="space-y-3">
            <h4 className="font-medium text-gray-900">
              Músicas Problemáticas ({report.problematicTracks.length})
            </h4>
            {report.problematicTracks.map((track) => (
              <div
                key={track.id}
                className="bg-white border border-gray-200 rounded-lg p-4 shadow-sm"
              >
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <h5 className="font-medium text-gray-900">{track.title}</h5>
                    <p className="text-sm text-gray-600">{track.artist}</p>
                    <div className="mt-2 grid grid-cols-2 md:grid-cols-4 gap-3 text-sm">
                      <div className="text-center">
                        <div className="text-red-600 font-medium">
                          {(track.negativeVoteRatio * 100).toFixed(1)}%
                        </div>
                        <div className="text-gray-500">Rejeição</div>
                      </div>
                      <div className="text-center">
                        <div className="text-blue-600 font-medium">
                          {track.score}
                        </div>
                        <div className="text-gray-500">Score</div>
                      </div>
                      <div className="text-center">
                        <div className="text-purple-600 font-medium">
                          {track.playCount}
                        </div>
                        <div className="text-gray-500">Reproduções</div>
                      </div>
                      <div className="text-center">
                        <div className="text-green-600 font-medium">
                          {(track.completionRate * 100).toFixed(1)}%
                        </div>
                        <div className="text-gray-500">Conclusão</div>
                      </div>
                    </div>
                  </div>
                  <div className="flex items-center space-x-2 ml-4">
                    <span
                      className={`px-2 py-1 rounded-full text-xs font-medium flex items-center space-x-1 ${getRecommendationColor(
                        track.recommendation
                      )}`}
                    >
                      {getRecommendationIcon(track.recommendation)}
                      <span className="capitalize">{track.recommendation}</span>
                    </span>
                    {(track.recommendation === Recommendation.Remove ||
                      track.recommendation === Recommendation.Blacklist) && (
                      <button
                        onClick={() => handleRemoveTrack(track.id)}
                        className="p-2 text-red-600 hover:bg-red-50 rounded-lg transition-colors"
                        aria-label={`Remover ${track.title} da playlist`}
                      >
                        <Trash2 className="w-4 h-4" />
                      </button>
                    )}
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}
    </>
  );
};

export default ProblematicTracksAlert;
