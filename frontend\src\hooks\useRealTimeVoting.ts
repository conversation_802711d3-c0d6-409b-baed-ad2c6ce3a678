import { useState, useEffect, useCallback } from "react";
import { toast } from "react-hot-toast";
import { wsService } from "@/services/websocket";
import { apiService } from "@/services/api";

interface VoteState {
  [suggestionId: string]: {
    upvotes: number;
    downvotes: number;
    voteCount: number;
    userVote?: "up" | "down" | null;
    isVoting: boolean;
  };
}

interface VoteUpdate {
  suggestionId: string;
  upvotes: number;
  downvotes: number;
  voteCount: number;
  voteType?: "up" | "down";
}

export const useRealTimeVoting = (restaurantId: string) => {
  const [voteStates, setVoteStates] = useState<VoteState>({});
  const [isConnected, setIsConnected] = useState(false);

  // Inicializar estados de voto para sugestões
  const initializeVoteStates = useCallback((suggestions: any[]) => {
    const initialStates: VoteState = {};

    suggestions.forEach((suggestion) => {
      initialStates[suggestion.id] = {
        upvotes: suggestion.upvotes || 0,
        downvotes: suggestion.downvotes || 0,
        voteCount: suggestion.voteCount || 0,
        userVote: suggestion.userVote || null,
        isVoting: false,
      };
    });

    setVoteStates(initialStates);
  }, []);

  // Atualizar estado de voto específico
  const updateVoteState = useCallback(
    (suggestionId: string, updates: Partial<VoteState[string]>) => {
      setVoteStates((prev) => ({
        ...prev,
        [suggestionId]: {
          ...prev[suggestionId],
          ...updates,
        },
      }));
    },
    []
  );

  // Função para votar
  const vote = useCallback(
    async (suggestionId: string, voteType: "up" | "down") => {
      const currentState = voteStates[suggestionId];
      if (!currentState || currentState.isVoting) return;

      // Otimistic update
      updateVoteState(suggestionId, { isVoting: true });

      try {
        const response = await fetch(
          `http://localhost:8001/api/v1/suggestions/${suggestionId}/vote`,
          {
            method: "POST",
            headers: {
              "Content-Type": "application/json",
            },
            body: JSON.stringify({
              type: voteType,
              restaurantId,
              sessionId: localStorage.getItem("sessionId") || "anonymous",
            }),
          }
        );

        if (!response.ok) {
          throw new Error("Erro ao votar");
        }

        const data = await response.json();

        // Atualizar estado com resposta do servidor
        updateVoteState(suggestionId, {
          upvotes: data.upvotes,
          downvotes: data.downvotes,
          voteCount: data.voteCount,
          userVote: voteType,
          isVoting: false,
        });

        toast.success(
          `Voto ${voteType === "up" ? "positivo" : "negativo"} registrado!`
        );
      } catch (error) {
        console.error("Erro ao votar:", error);
        toast.error("Erro ao registrar voto");

        // Reverter otimistic update
        updateVoteState(suggestionId, { isVoting: false });
      }
    },
    [voteStates, restaurantId, updateVoteState]
  );

  // Escutar atualizações de voto via WebSocket
  useEffect(() => {
    const handleVoteUpdate = (data: VoteUpdate) => {
      updateVoteState(data.suggestionId, {
        upvotes: data.upvotes,
        downvotes: data.downvotes,
        voteCount: data.voteCount,
      });
    };

    const handleConnectionChange = (connected: boolean) => {
      setIsConnected(connected);
    };

    // Conectar ao WebSocket
    wsService.connect();
    wsService.joinRestaurant(restaurantId);

    // Escutar eventos
    wsService.on("suggestion:vote", handleVoteUpdate);
    wsService.on("connected", () => handleConnectionChange(true));
    wsService.on("disconnected", () => handleConnectionChange(false));

    return () => {
      wsService.off("suggestion:vote", handleVoteUpdate);
      wsService.off("connected");
      wsService.off("disconnected");
    };
  }, [restaurantId, updateVoteState]);

  // Obter estado de voto para uma sugestão específica
  const getVoteState = useCallback(
    (suggestionId: string) => {
      return (
        voteStates[suggestionId] || {
          upvotes: 0,
          downvotes: 0,
          voteCount: 0,
          userVote: null,
          isVoting: false,
        }
      );
    },
    [voteStates]
  );

  // Verificar se o usuário pode votar
  const canVote = useCallback(
    (suggestionId: string) => {
      const state = getVoteState(suggestionId);
      return !state.isVoting && isConnected;
    },
    [getVoteState, isConnected]
  );

  return {
    voteStates,
    vote,
    getVoteState,
    canVote,
    isConnected,
    initializeVoteStates,
    updateVoteState,
  };
};
