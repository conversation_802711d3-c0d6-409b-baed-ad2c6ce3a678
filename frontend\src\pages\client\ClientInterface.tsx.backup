import React, { useState, useEffect } from "react";
import { useParams, useSearchParams } from "react-router-dom";
import { motion, AnimatePresence } from "framer-motion";
import {
  Music,
  ThumbsUp,
  ThumbsDown,
  Search,
  Play,
  Clock,
  Users,
  Heart,
  Star,
  TrendingUp,
  Headphones,
  RefreshC<PERSON>,
  User,
  CreditCard,
  Mic,
  Trophy,
} from "lucide-react";
import { toast } from "react-hot-toast";
import sessionService, {
  ClientSession,
  UserStats,
} from "@/services/sessionService";
import { useWebSocket } from "@/services/websocket";
import MusicFilters from "@/components/music/MusicFilters";
import PaymentModal from "@/components/client/PaymentModal";
import KaraokePlayer from "@/components/client/KaraokePlayer";
import VotingWidget from "@/components/client/VotingWidget";
import PlaybackQueue from "@/components/client/PlaybackQueue";
import RewardsPanel from "@/components/client/RewardsPanel";

interface Song {
  id: string;
  title: string;
  artist: string;
  duration: number;
  formattedDuration: string;
  thumbnailUrl: string;
  channelName: string;
  viewCount: number;
  publishedAt: string;
}

interface Suggestion {
  id: string;
  title: string;
  artist: string;
  youtubeVideoId: string;
  status: "pending" | "approved" | "rejected";
  upvotes: number;
  downvotes: number;
  score: number;
  createdAt: string;
  isPaid?: boolean;
  clientSessionId?: string;
}

const ClientInterface: React.FC = () => {
  const { restaurantId } = useParams<{ restaurantId: string }>();
  const [searchParams] = useSearchParams();
  const tableNumber = searchParams.get("table");

  const [restaurant, setRestaurant] = useState<any>(null);
  const [searchQuery, setSearchQuery] = useState("");
  const [searchResults, setSearchResults] = useState<Song[]>([]);
  const [suggestions, setSuggestions] = useState<Suggestion[]>([]);
  const [currentlyPlaying, setCurrentlyPlaying] = useState<any>(null);
  const [loading, setLoading] = useState(false);
  const [searchLoading, setSearchLoading] = useState(false);
  const [userVotes, setUserVotes] = useState<{ [key: string]: "up" | "down" }>(
    {}
  );
  const [activeFilters, setActiveFilters] = useState<string[]>([]);
  const [playlistPage, setPlaylistPage] = useState(1);
  const [playlistLoading, setPlaylistLoading] = useState(false);
  const [hasMorePlaylist, setHasMorePlaylist] = useState(true);
  const [totalPlaylistSongs, setTotalPlaylistSongs] = useState(0);

  // Session and Gamification
  const [session, setSession] = useState<ClientSession | null>(null);
  const [userStats, setUserStats] = useState<UserStats>({
    points: 0,
    level: 1,
    badges: [] as string[],
    suggestionsCount: 0,
    votesCount: 0,
    streak: 0,
  });
  const [showLevelUp, setShowLevelUp] = useState(false);
  const [showBadgeEarned, setShowBadgeEarned] = useState<string | null>(null);
  const [showNameInput, setShowNameInput] = useState(false);
  const [clientName, setClientName] = useState("");

  // Payment and Karaoke states
  const [showPaymentModal, setShowPaymentModal] = useState(false);
  const [selectedSongForPayment, setSelectedSongForPayment] =
    useState<Song | null>(null);
  const [showKaraoke, setShowKaraoke] = useState(false);
  const [karaokeData, setKaraokeData] = useState<any>(null);

  // Queue states
  const [showQueue, setShowQueue] = useState(true);
  const [queueCollapsed, setQueueCollapsed] = useState(false);

  // Rewards states
  const [showRewards, setShowRewards] = useState(false);

  // WebSocket connection
  const {
    service: wsService,
    isConnected,
    joinRestaurant,
    on,
    off,
  } = useWebSocket();

  useEffect(() => {
    console.log("🚀 useEffect triggered. RestaurantId:", restaurantId);
    if (restaurantId) {
      console.log("🚀 Iniciando carregamento das funcionalidades...");
      initializeSession();
      loadRestaurantInfo();
      loadSuggestions();
      loadCurrentlyPlaying();
      loadAvailableMusic();
      joinRestaurant(restaurantId);
      setupWebSocketListeners();
    }

    return () => {
      cleanupWebSocketListeners();
    };
  }, [restaurantId]);

  // Reload suggestions and playlist when filters change
  useEffect(() => {
    if (restaurantId) {
      loadSuggestions();
      setPlaylistPage(1);
      loadAvailableMusic(1);
    }
  }, [activeFilters, restaurantId]);

  const initializeSession = async () => {
    try {
      const newSession = await sessionService.forceNewSession(
        restaurantId!,
        tableNumber || undefined,
        clientName || undefined
      );
      setSession(newSession);
      setUserStats({
        points: newSession.points || 0,
        level: newSession.level || 1,
        badges: newSession.badges || [],
        suggestionsCount: newSession.suggestionsCount || 0,
        votesCount: newSession.votesCount || 0,
        streak: newSession.streak || 0,
      });
      if (!newSession.clientName && !clientName) {
        setShowNameInput(true);
      }
    } catch (error) {
      console.error("Error initializing session:", error);
      toast.error("Erro ao inicializar sessão");
    }
  };

  const setupWebSocketListeners = () => {
    on("new-suggestion", (data: any) => {
      console.log("Nova sugestão recebida via WebSocket:", data);
      loadSuggestions();
      toast.success(`Nova música: ${data.title}`, { duration: 3000 });
    });
    on("vote-update", (data: any) => {
      console.log("Atualização de votos via WebSocket:", data);
      loadSuggestions();
    });
    on("queue-update", (data: any) => {
      console.log("Fila atualizada via WebSocket:", data);
      loadSuggestions();
    });
    on("suggestion-approved", (data: any) => {
      console.log("Sugestão aprovada via WebSocket:", data);
      loadSuggestions();
      toast.success(`✅ Música aprovada: ${data.title}`, { duration: 4000 });
    });
    on("now-playing", (data: any) => {
      console.log("Tocando agora via WebSocket:", data);
      setCurrentlyPlaying(data.suggestion);
      toast(`🎵 Tocando: ${data.suggestion.title}`, { duration: 5000 });
    });
  };

  const cleanupWebSocketListeners = () => {
    off("new-suggestion", () => {});
    off("vote-update", () => {});
    off("queue-update", () => {});
    off("suggestion-approved", () => {});
    off("now-playing", () => {});
  };

  const loadRestaurantInfo = async () => {
    try {
      const response = await fetch(
        `http://localhost:8001/api/v1/restaurants/${restaurantId}`
      );
      if (response.ok) {
        const data = await response.json();
        setRestaurant(data.restaurant);
      } else {
        setRestaurant({
          id: restaurantId,
          name: "Restaurante Demo",
          description: "Ambiente acolhedor com música interativa",
          isActive: true,
          isOpen: true,
        });
      }
    } catch (error) {
      console.error("Erro ao carregar informações do restaurante:", error);
      setRestaurant({
        id: restaurantId,
        name: "Restaurante Demo",
        description: "Ambiente acolhedor com música interativa",
        isActive: true,
        isOpen: true,
      });
    }
  };

  const loadSuggestions = async () => {
    try {
      const params = new URLSearchParams();
      params.append("status", "approved");
      if (activeFilters.length > 0) {
        const genreFilters = activeFilters.filter((filter) =>
          ["rock", "pop", "sertanejo", "mpb", "eletronica", "funk"].includes(
            filter
          )
        );
        if (genreFilters.length > 0) {
          params.append("genres", genreFilters.join(","));
        }
        const moodFilters = activeFilters.filter((filter) =>
          ["happy", "sad", "energetic", "calm"].includes(filter)
        );
        if (moodFilters.length > 0) {
          params.append("moods", moodFilters.join(","));
        }
      }
      const response = await fetch(
        `http://localhost:8001/api/v1/suggestions/${restaurantId}?${params.toString()}`
      );
      if (response.ok) {
        const data = await response.json();
        setSuggestions(data.suggestions || []);
      } else {
        setSuggestions([
          {
            id: "1",
            title: "Bohemian Rhapsody",
            artist: "Queen",
            youtubeVideoId: "fJ9rUzIMcZQ",
            status: "approved" as const,
            upvotes: 15,
            downvotes: 2,
            score: 13,
            createdAt: new Date().toISOString(),
          },
          {
            id: "2",
            title: "Hotel California",
            artist: "Eagles",
            youtubeVideoId: "BciS5krYL80",
            status: "approved" as const,
            upvotes: 12,
            downvotes: 1,
            score: 11,
            createdAt: new Date().toISOString(),
          },
          {
            id: "3",
            title: "Imagine",
            artist: "John Lennon",
            youtubeVideoId: "YkgkThdzX-8",
            status: "approved" as const,
            upvotes: 10,
            downvotes: 0,
            score: 10,
            createdAt: new Date().toISOString(),
          },
        ]);
      }
    } catch (error) {
      console.error("Erro ao carregar sugestões:", error);
      setSuggestions([
        {
          id: "fJ9rUzIMcZQ",
          title: "Bohemian Rhapsody",
          artist: "Queen",
          youtubeVideoId: "fJ9rUzIMcZQ",
          status: "approved" as const,
          upvotes: 15,
          downvotes: 2,
          score: 13,
          createdAt: new Date().toISOString(),
        },
        {
          id: "BciS5krYL80",
          title: "Hotel California",
          artist: "Eagles",
          youtubeVideoId: "BciS5krYL80",
          status: "approved" as const,
          upvotes: 12,
          downvotes: 1,
          score: 11,
          createdAt: new Date().toISOString(),
        },
      ]);
    }
  };

  const loadCurrentlyPlaying = async () => {
    try {
      const response = await fetch(
        `http://localhost:8001/api/v1/playback/${restaurantId}/state`
      );
      if (response.ok) {
        const data = await response.json();
        setCurrentlyPlaying(data.state?.currentTrack);
      }
    } catch (error) {
      console.error("Erro ao carregar música atual:", error);
    }
  };

  const loadAvailableMusic = async (
    page: number = 1,
    loadMore: boolean = false
  ) => {
    try {
      setPlaylistLoading(true);
      console.log(
        `🎵 Carregando músicas disponíveis das playlists - Página ${page}...`
      );
      const limit = 24;
      const params = new URLSearchParams({
        page: page.toString(),
        limit: limit.toString(),
      });
      if (activeFilters.length > 0) {
        const genreFilters = activeFilters.filter((filter) =>
          ["rock", "pop", "sertanejo", "mpb", "eletronica", "funk"].includes(
            filter
          )
        );
        if (genreFilters.length > 0) {
          params.append("genres", genreFilters.join(","));
        }
        const moodFilters = activeFilters.filter((filter) =>
          ["happy", "sad", "energetic", "calm"].includes(filter)
        );
        if (moodFilters.length > 0) {
          params.append("moods", moodFilters.join(","));
        }
      }
      const url = `http://localhost:8001/api/v1/restaurants/${restaurantId}/playlist?${params.toString()}`;
      console.log("🔍 URL da requisição:", url);

      const response = await fetch(url);
      if (response.ok) {
        const data = await response.json();
        console.log("📊 Dados recebidos:", data);

        if (data.success && data.results) {
          const transformedResults = data.results.map((track: any) => ({
            id: track.youtubeVideoId || track.id,
            title: track.title,
            artist: track.artist,
            duration: track.duration,
            formattedDuration: track.formattedDuration,
            thumbnailUrl: track.thumbnailUrl,
            youtubeVideoId: track.youtubeVideoId || track.id,
            channelName: track.artist,
            viewCount: 0,
            publishedAt: track.addedAt || new Date().toISOString(),
          }));

          console.log(
            `✅ ${transformedResults.length} músicas carregadas da página ${page}`
          );
          if (loadMore) {
            setSearchResults((prev) => [...prev, ...transformedResults]);
          } else {
            setSearchResults(transformedResults);
          }
          setTotalPlaylistSongs(data.total || data.results.length);
          setHasMorePlaylist(transformedResults.length === limit);
          setPlaylistPage(page);
          console.log(
            `📊 Total de músicas na playlist: ${data.total || "desconhecido"}`
          );
          console.log(
            `🔄 Há mais páginas? ${transformedResults.length === limit}`
          );
        } else {
          console.log("⚠️ Nenhuma música encontrada nas playlists");
          if (!loadMore) {
            setSearchResults([]);
          }
        }
      } else {
        console.error(
          "❌ Erro ao carregar músicas das playlists:",
          response.status
        );
        toast.error("Erro ao carregar playlist do restaurante");
      }
    } catch (error) {
      console.error("❌ Erro ao carregar músicas das playlists:", error);
      toast.error("Erro ao carregar playlist do restaurante");
    } finally {
      setPlaylistLoading(false);
    }
  };

  const loadMorePlaylist = () => {
    if (!playlistLoading && hasMorePlaylist) {
      loadAvailableMusic(playlistPage + 1, true);
    }
  };

  const searchSongs = async () => {
    if (!searchQuery.trim()) return;

    try {
      setSearchLoading(true);
      console.log("🔍 DEBUG: RestaurantId usado na busca:", restaurantId);
      const params = new URLSearchParams({
        q: encodeURIComponent(searchQuery),
      });
      if (activeFilters.length > 0) {
        const genreFilters = activeFilters.filter((filter) =>
          ["rock", "pop", "sertanejo", "mpb", "eletronica", "funk"].includes(
            filter
          )
        );
        if (genreFilters.length > 0) {
          params.append("genres", genreFilters.join(","));
        }
        const moodFilters = activeFilters.filter((filter) =>
          ["happy", "sad", "energetic", "calm"].includes(filter)
        );
        if (moodFilters.length > 0) {
          params.append("moods", moodFilters.join(","));
        }
      }
      const url = `http://localhost:8001/api/v1/restaurants/${restaurantId}/playlist?${params.toString()}`;
      console.log("🔍 DEBUG: URL da requisição:", url);

      const response = await fetch(url);
      if (response.ok) {
        const data = await response.json();
        console.log("🔍 DEBUG: Dados da API de busca:", data);
        if (data.success) {
          const transformedResults = data.results.map((track: any) => ({
            id: track.youtubeVideoId || track.id,
            title: track.title,
            artist: track.artist,
            duration: track.duration,
            formattedDuration: track.formattedDuration,
            thumbnailUrl: track.thumbnailUrl,
            youtubeVideoId: track.youtubeVideoId || track.id,
            channelName: track.artist,
            viewCount: 0,
            publishedAt: track.addedAt || new Date().toISOString(),
          }));
          setSearchResults(transformedResults);
          setTotalPlaylistSongs(data.total || data.results.length);
          setHasMorePlaylist(false); // Busca não suporta paginação adicional
          setPlaylistPage(1);
          if (transformedResults.length === 0) {
            toast.info(`Nenhuma música encontrada para "${searchQuery}"`);
          } else {
            toast.success(
              `${transformedResults.length} música(s) encontrada(s)`
            );
          }
        } else {
          toast.error(data.message || "Erro ao buscar na playlist");
          setSearchResults([]);
        }
      } else {
        toast.error("Erro ao buscar na playlist do restaurante");
        setSearchResults([]);
      }
    } catch (error) {
      console.error("Erro ao buscar na playlist:", error);
      toast.error("Erro ao buscar na playlist do restaurante");
      setSearchResults([]);
    } finally {
      setSearchLoading(false);
    }
  };

  const calculateLevel = (points: number) => Math.floor(points / 100) + 1;

  const getBadges = (stats: any) => {
    const badges = [];
    if (stats.suggestionsCount >= 1) badges.push("🎵 Primeira Sugestão");
    if (stats.suggestionsCount >= 5) badges.push("🎶 Melomaníaco");
    if (stats.suggestionsCount >= 10) badges.push("🎸 DJ Amador");
    if (stats.votesCount >= 10) badges.push("👍 Crítico Musical");
    if (stats.votesCount >= 25) badges.push("⭐ Especialista");
    if (stats.streak >= 3) badges.push("🔥 Em Chamas");
    if (stats.points >= 500) badges.push("🏆 Lenda");
    return badges;
  };

  const checkForNewBadges = () => {
    if (!session) return;
    const currentStats = sessionService.getSession();
    if (!currentStats) return;
    const newBadges = getBadges(currentStats);
    const earnedBadge = newBadges.find(
      (badge) => !userStats.badges.includes(badge)
    );
    if (earnedBadge) {
      sessionService.awardBadge(earnedBadge);
      setShowBadgeEarned(earnedBadge);
      setTimeout(() => setShowBadgeEarned(null), 3000);
      toast.success(`🏆 Nova conquista: ${earnedBadge}!`);
    }
    const newLevel = calculateLevel(currentStats.points);
    if (newLevel > userStats.level) {
      setShowLevelUp(true);
      setTimeout(() => setShowLevelUp(false), 3000);
      toast.success(`🎉 Level Up! Agora você é nível ${newLevel}!`);
    }
  };

  const saveClientName = async () => {
    if (!clientName.trim()) {
      toast.error("Por favor, digite seu nome");
      return;
    }
    try {
      const updatedSession = await sessionService.createSession(
        restaurantId!,
        tableNumber || undefined,
        clientName
      );
      setSession(updatedSession);
      setShowNameInput(false);
      toast.success(`Bem-vindo, ${clientName}! 🎵`);
    } catch (error) {
      console.error("Error saving client name:", error);
      toast.error("Erro ao salvar nome");
    }
  };

  const awardPoints = (action: "suggest" | "vote", amount: number = 10) => {
    setUserStats((prev) => {
      const newStats = {
        ...prev,
        points: prev.points + amount,
        suggestionsCount:
          action === "suggest"
            ? prev.suggestionsCount + 1
            : prev.suggestionsCount,
        votesCount: action === "vote" ? prev.votesCount + 1 : prev.votesCount,
        streak: prev.streak + 1,
      };
      const newLevel = calculateLevel(newStats.points);
      const newBadges = getBadges(newStats);
      if (newLevel > prev.level) {
        setShowLevelUp(true);
        setTimeout(() => setShowLevelUp(false), 3000);
      }
      const earnedBadge = newBadges.find(
        (badge) => !prev.badges.includes(badge)
      );
      if (earnedBadge) {
        setShowBadgeEarned(earnedBadge);
        setTimeout(() => setShowBadgeEarned(null), 3000);
      }
      return {
        ...newStats,
        level: newLevel,
        badges: newBadges,
      };
    });
  };

  const suggestSong = async (song: Song) => {
    console.log("🎵 DEBUG: suggestSong chamado com:", song);
    console.log("🔗 DEBUG: restaurantId:", restaurantId, "session:", session);
    if (!session) {
      toast.error("Sessão não encontrada. Recarregue a página.");
      return;
    }
    try {
      setLoading(true);
      const requestData = {
        restaurantId,
        youtubeVideoId: song.youtubeVideoId || song.id,
      };
      console.log("📤 DEBUG: Enviando dados:", requestData);
      console.log("📤 DEBUG: Session token:", session?.sessionToken);
      const response = await fetch(`http://localhost:8001/api/v1/suggestions`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "X-Session-ID": session.sessionToken || "",
        },
        body: JSON.stringify(requestData),
      });
      console.log(
        "📥 DEBUG: Response status:",
        response.status,
        response.statusText
      );
      if (response.ok) {
        const responseData = await response.json();
        console.log("✅ DEBUG: Response data:", responseData);
        toast.success("Música sugerida com sucesso! 🎵");
        loadSuggestions();
        awardPoints("suggest", 10);
        checkForNewBadges();
        setSearchQuery("");
        if (searchQuery.trim()) {
          loadAvailableMusic(1); // Recarregar playlist após busca
        }
      } else {
        const errorData = await response.json();
        console.error("❌ DEBUG: Erro response:", errorData);
        toast.error(errorData.message || "Erro ao sugerir música");
      }
    } catch (error) {
      console.error("❌ DEBUG: Erro na requisição:", error);
      toast.error("Erro ao sugerir música");
    } finally {
      setLoading(false);
    }
  };

  const suggestSongWithPayment = async (song: Song) => {
    console.log("🎵 DEBUG: suggestSongWithPayment chamado com:", song);
    setSelectedSongForPayment(song);
    setShowPaymentModal(true);
    console.log("✅ DEBUG: Estados setados:", {
      showPaymentModal: true,
      selectedSongForPayment: song,
    });
  };

  const handlePaymentSuccess = async (paymentId: string) => {
    if (!selectedSongForPayment) return;
    try {
      toast.success(
        "Pagamento aprovado! Música adicionada à fila prioritária! 🎵"
      );
      loadSuggestions();
      awardPoints("suggest", 25);
      checkForNewBadges();
      setSearchQuery("");
      if (searchQuery.trim()) {
        loadAvailableMusic(1);
      }
      setShowPaymentModal(false);
      setKaraokeData({
        id: selectedSongForPayment.id,
        title: selectedSongForPayment.title,
        artist: selectedSongForPayment.artist,
        thumbnailUrl: selectedSongForPayment.thumbnailUrl,
        duration: selectedSongForPayment.duration,
        youtubeVideoId: selectedSongForPayment.id,
      });
      setSelectedSongForPayment(null);
      setTimeout(() => {
        toast(
          (t) => (
            <div className="flex flex-col space-y-2">
              <span>🎤 Quer cantar junto? Ative o "Cante Comigo"!</span>
              <div className="flex space-x-2">
                <button
                  onClick={() => {
                    setShowKaraoke(true);
                    toast.dismiss(t.id);
                  }}
                  className="px-3 py-1 bg-blue-600 text-white rounded text-sm"
                >
                  Sim, vamos cantar!
                </button>
                <button
                  onClick={() => toast.dismiss(t.id)}
                  className="px-3 py-1 bg-gray-600 text-white rounded text-sm"
                >
                  Não, obrigado
                </button>
              </div>
            </div>
          ),
          { duration: 8000 }
        );
      }, 2000);
    } catch (error) {
      console.error("Erro após pagamento:", error);
      toast.error("Erro ao processar pagamento");
    }
  };

  const voteSuggestion = async (
    suggestionId: string,
    voteType: "up" | "down"
  ) => {
    console.log("👍 DEBUG: voteSuggestion chamado:", {
      suggestionId,
      voteType,
    });
    if (!session) {
      console.error("❌ DEBUG: Sessão não encontrada");
      toast.error("Sessão não encontrada. Recarregue a página.");
      return;
    }
    try {
      const sessionToken = sessionService.getSessionToken();
      if (!sessionToken) {
        toast.error("Token de sessão não encontrado");
        return;
      }
      const url = `http://localhost:8001/api/v1/suggestions/${suggestionId}/vote`;
      const requestData = { voteType };
      console.log("📤 DEBUG: Enviando voto:", { url, requestData });
      const response = await fetch(url, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "X-Session-ID": sessionToken,
        },
        body: JSON.stringify(requestData),
      });
      console.log(
        "📥 DEBUG: Vote response status:",
        response.status,
        response.statusText
      );
      if (response.ok) {
        const data = await response.json();
        console.log("✅ DEBUG: Vote response data:", data);
        setUserVotes((prev) => ({ ...prev, [suggestionId]: voteType }));
        sessionService.incrementVotes();
        const updatedSession = sessionService.getSession();
        if (updatedSession) {
          setUserStats({
            points: updatedSession.points,
            level: updatedSession.level,
            badges: updatedSession.badges,
            suggestionsCount: updatedSession.suggestionsCount,
            votesCount: updatedSession.votesCount,
            streak: updatedSession.streak,
          });
        }
        loadSuggestions();
        toast.success(
          `Voto ${
            voteType === "up" ? "positivo" : "negativo"
          } registrado! +10 pontos!`
        );
        checkForNewBadges();
      } else {
        const errorData = await response.json();
        console.error("❌ DEBUG: Vote error response:", errorData);
        toast.error(errorData.message || "Erro ao votar");
      }
    } catch (error) {
      console.error("❌ DEBUG: Vote error:", error);
      toast.error("Erro ao votar");
    }
  };

  if (!restaurant) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-purple-900 via-blue-900 to-indigo-900 flex items-center justify-center">
        <div className="text-center text-white">
          <RefreshCw className="w-12 h-12 animate-spin mx-auto mb-4" />
          <h2 className="text-xl font-semibold">Carregando...</h2>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-purple-900 via-blue-900 to-indigo-900">
      {/* Header - Mobile First */}
      <div className="bg-black/20 backdrop-blur-sm border-b border-white/10">
        <div className="px-3 py-4 sm:px-4 sm:py-6">
          <div className="text-center">
            <div className="flex items-center justify-center space-x-2 sm:space-x-3 mb-2">
              <div className="w-10 h-10 sm:w-12 sm:h-12 bg-gradient-to-r from-pink-500 to-violet-500 rounded-full flex items-center justify-center">
                <Music className="w-5 h-5 sm:w-6 sm:h-6 text-white" />
              </div>
              <div>
                <h1 className="text-lg sm:text-2xl font-bold text-white">
                  {restaurant.name}
                </h1>
                {tableNumber && (
                  <p className="text-purple-200 text-xs sm:text-sm">
                    Mesa {tableNumber}
                  </p>
                )}
              </div>
            </div>
            <p className="text-purple-200 text-xs sm:text-sm">
              Sugira músicas e vote nas suas favoritas!
            </p>
            <div className="flex items-center justify-center space-x-2 sm:space-x-6 mt-3 sm:mt-4 text-xs sm:text-sm overflow-x-auto">
              <div className="flex items-center space-x-1 flex-shrink-0">
                <Star className="w-3 h-3 sm:w-4 sm:h-4 text-yellow-400" />
                <span className="text-white font-semibold">
                  Nível {userStats.level}
                </span>
              </div>
              <div className="flex items-center space-x-1 flex-shrink-0">
                <TrendingUp className="w-3 h-3 sm:w-4 sm:h-4 text-green-400" />
                <span className="text-white">{userStats.points} pts</span>
              </div>
              <div className="flex items-center space-x-1 flex-shrink-0">
                <Heart className="w-3 h-3 sm:w-4 sm:h-4 text-red-400" />
                <span className="text-white">{userStats.suggestionsCount}</span>
              </div>
              <div className="flex items-center space-x-1 flex-shrink-0">
                <ThumbsUp className="w-3 h-3 sm:w-4 sm:h-4 text-blue-400" />
                <span className="text-white">{userStats.votesCount}</span>
              </div>
              <button
                onClick={() => setShowRewards(true)}
                className="flex items-center space-x-1 px-3 py-1 bg-gradient-to-r from-yellow-500 to-orange-500 text-white rounded-full hover:from-yellow-600 hover:to-orange-600 transition-all duration-200 transform hover:scale-105"
              >
                <Trophy className="w-4 h-4" />
                <span className="font-medium">Prêmios</span>
              </button>
            </div>
            {userStats.badges.length > 0 && (
              <div className="flex flex-wrap justify-center gap-2 mt-3">
                {userStats.badges.slice(0, 3).map((badge, index) => (
                  <span
                    key={index}
                    className="px-2 py-1 bg-white/10 rounded-full text-xs text-white border border-white/20"
                  >
                    {badge}
                  </span>
                ))}
                {userStats.badges.length > 3 && (
                  <span className="px-2 py-1 bg-white/10 rounded-full text-xs text-purple-200 border border-white/20">
                    +{userStats.badges.length - 3} mais
                  </span>
                )}
              </div>
            )}
          </div>
        </div>
      </div>

      <div className="max-w-4xl mx-auto px-3 sm:px-4 py-4 sm:py-6 space-y-4 sm:space-y-6">
        {/* Música Tocando Agora */}
        {currentlyPlaying && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="bg-white/10 backdrop-blur-sm rounded-xl p-4 sm:p-6 border border-white/20"
          >
            <div className="flex items-center space-x-2 mb-3">
              <Play className="w-5 h-5 text-green-400" />
              <span className="text-white font-medium">Tocando Agora</span>
            </div>
            <div className="flex items-center space-x-4">
              <img
                src={currentlyPlaying.thumbnailUrl}
                alt={currentlyPlaying.title}
                className="w-16 h-16 rounded-lg object-cover"
              />
              <div className="flex-1">
                <h3 className="text-white font-semibold">
                  {currentlyPlaying.title}
                </h3>
                <p className="text-purple-200">{currentlyPlaying.artist}</p>
              </div>
              <div className="text-right">
                <div className="text-2xl font-bold text-green-400">
                  {currentlyPlaying.score > 0 ? "+" : ""}
                  {currentlyPlaying.score}
                </div>
                <div className="text-xs text-purple-200">votos</div>
              </div>
            </div>
          </motion.div>
        )}

        {/* Playlist do Restaurante */}
        <div className="bg-white/10 backdrop-blur-sm rounded-xl p-6 border border-white/20">
          <div className="flex items-center justify-between mb-4">
            <h2 className="text-xl font-bold text-white flex items-center space-x-2">
              <Music className="w-5 h-5" />
              <span>Playlist do Restaurante</span>
            </h2>
            {totalPlaylistSongs > 0 && (
              <div className="text-purple-200 text-sm">
                {searchResults.length} de {totalPlaylistSongs} músicas
              </div>
            )}
          </div>

          {searchResults.length === 0 ? (
            <div className="text-center py-8">
              <Headphones className="w-12 h-12 text-purple-400 mx-auto mb-3" />
              <p className="text-purple-200">
                Nenhuma música na playlist ainda.
              </p>
              <p className="text-purple-300 text-sm">
                Use a busca ou filtros para encontrar músicas.
              </p>
            </div>
          ) : (
            <>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-6">
                {searchResults.map((song) => (
                  <motion.div
                    key={song.id}
                    initial={{ opacity: 0, scale: 0.9 }}
                    animate={{ opacity: 1, scale: 1 }}
                    whileHover={{ scale: 1.05 }}
                    className="bg-white/5 rounded-lg p-4 border border-white/10 hover:bg-white/10 transition-all"
                  >
                    <img
                      src={song.thumbnailUrl}
                      alt={song.title}
                      className="w-full h-32 object-cover rounded-lg mb-3"
                    />
                    <h3 className="text-white font-semibold text-sm mb-1 truncate">
                      {song.title}
                    </h3>
                    <p className="text-purple-200 text-xs mb-2 truncate">
                      {song.artist}
                    </p>
                    <div className="flex justify-between items-center text-xs text-purple-300 mb-3">
                      <span>{song.formattedDuration}</span>
                      <span>{song.genre || "N/A"}</span>
                    </div>
                    <div className="flex flex-col space-y-2">
                      <button
                        onClick={() => suggestSong(song)}
                        disabled={loading}
                        className="px-3 py-2 bg-gradient-to-r from-blue-600 to-purple-600 text-white rounded-lg hover:from-blue-700 hover:to-purple-700 disabled:opacity-50 text-xs flex items-center justify-center space-x-1"
                        aria-label={`Adicionar ${song.title} à fila normal`}
                      >
                        <Music className="w-3 h-3" />
                        <span>Fila Normal</span>
                      </button>
                      <button
                        onClick={() => suggestSongWithPayment(song)}
                        disabled={loading}
                        className="px-3 py-2 bg-gradient-to-r from-green-600 to-emerald-600 text-white rounded-lg hover:from-green-700 hover:to-emerald-700 disabled:opacity-50 text-xs flex items-center justify-center space-x-1"
                        aria-label={`Adicionar ${song.title} à fila prioritária por R$ 2`}
                      >
                        <CreditCard className="w-3 h-3" />
                        <span>Prioridade R$ 2</span>
                      </button>
                    </div>
                  </motion.div>
                ))}
              </div>
              {hasMorePlaylist && (
                <div className="text-center">
                  <button
                    onClick={loadMorePlaylist}
                    disabled={playlistLoading}
                    className="px-6 py-3 bg-gradient-to-r from-purple-600 to-pink-600 text-white rounded-lg hover:from-purple-700 hover:to-pink-700 disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-2 mx-auto"
                    aria-label="Carregar mais músicas da playlist"
                  >
                    {playlistLoading ? (
                      <RefreshCw className="w-4 h-4 animate-spin" />
                    ) : (
                      <Music className="w-4 h-4" />
                    )}
                    <span>
                      {playlistLoading
                        ? "Carregando..."
                        : "Carregar Mais Músicas"}
                    </span>
                  </button>
                </div>
              )}
              {!hasMorePlaylist && totalPlaylistSongs > 24 && (
                <div className="text-center text-purple-300 text-sm">
                  🎵 Todas as {totalPlaylistSongs} músicas foram carregadas!
                </div>
              )}
            </>
          )}
        </div>

        {/* Sugestões Aprovadas */}
        <div className="bg-white/10 backdrop-blur-sm rounded-xl p-6 border border-white/20">
          <h2 className="text-xl font-bold text-white mb-4 flex items-center space-x-2">
            <TrendingUp className="w-5 h-5" />
            <span>Fila de Músicas</span>
          </h2>
          {suggestions.length === 0 ? (
            <div className="text-center py-8">
              <Headphones className="w-12 h-12 text-purple-400 mx-auto mb-3" />
              <p className="text-purple-200">Nenhuma música na fila ainda.</p>
              <p className="text-purple-300 text-sm">
                Seja o primeiro a sugerir!
              </p>
            </div>
          ) : (
            <div className="space-y-3">
              {suggestions.map((suggestion, index) => (
                <motion.div
                  key={suggestion.id}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: index * 0.1 }}
                  className="flex items-center space-x-4 p-4 bg-white/5 rounded-lg border border-white/10"
                >
                  <div className="text-center">
                    <div className="text-lg font-bold text-purple-300">
                      #{index + 1}
                    </div>
                  </div>
                  <img
                    src={`https://img.youtube.com/vi/${suggestion.youtubeVideoId}/mqdefault.jpg`}
                    alt={suggestion.title}
                    className="w-16 h-16 rounded-lg object-cover"
                  />
                  <div className="flex-1">
                    <h3 className="text-white font-semibold">
                      {suggestion.title}
                    </h3>
                    <p className="text-purple-200">{suggestion.artist}</p>
                    <div
                      className="flex items-center

System: center space-x-4 mt-1"
                    >
                      <div className="flex items-center space-x-1 text-green-400">
                        <ThumbsUp className="w-3 h-3" />
                        <span className="text-sm">{suggestion.upvotes}</span>
                      </div>
                      <div className="flex items-center space-x-1 text-red-400">
                        <ThumbsDown className="w-3 h-3" />
                        <span className="text-sm">{suggestion.downvotes}</span>
                      </div>
                      <div className="text-purple-300 text-sm">
                        Score: {suggestion.score}
                      </div>
                      {suggestion.isPaid && (
                        <div className="flex items-center space-x-1 text-yellow-400">
                          <CreditCard className="w-3 h-3" />
                          <span className="text-xs">PAGO</span>
                        </div>
                      )}
                    </div>
                  </div>
                  <div className="flex space-x-2">
                    {suggestion.isPaid &&
                      suggestion.clientSessionId === session?.id && (
                        <button
                          onClick={() => {
                            setKaraokeData({
                              id: suggestion.youtubeVideoId,
                              title: suggestion.title,
                              artist: suggestion.artist,
                              thumbnailUrl: `https://img.youtube.com/vi/${suggestion.youtubeVideoId}/mqdefault.jpg`,
                              duration: suggestion.duration,
                              youtubeVideoId: suggestion.youtubeVideoId,
                            });
                            setShowKaraoke(true);
                          }}
                          className="p-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                          aria-label={`Ativar karaokê para ${suggestion.title}`}
                        >
                          <Mic className="w-4 h-4" />
                        </button>
                      )}
                    <button
                      onClick={() => voteSuggestion(suggestion.id, "up")}
                      disabled={userVotes[suggestion.id] === "up"}
                      className={`p-2 rounded-lg transition-colors ${
                        userVotes[suggestion.id] === "up"
                          ? "bg-green-600 text-white"
                          : "bg-white/20 text-green-400 hover:bg-green-600 hover:text-white"
                      }`}
                      aria-label={`Votar positivamente em ${suggestion.title}`}
                    >
                      <ThumbsUp className="w-4 h-4" />
                    </button>
                    <button
                      onClick={() => voteSuggestion(suggestion.id, "down")}
                      disabled={userVotes[suggestion.id] === "down"}
                      className={`p-2 rounded-lg transition-colors ${
                        userVotes[suggestion.id] === "down"
                          ? "bg-red-600 text-white"
                          : "bg-white/20 text-red-400 hover:bg-red-600 hover:text-white"
                      }`}
                      aria-label={`Votar negativamente em ${suggestion.title}`}
                    >
                      <ThumbsDown className="w-4 h-4" />
                    </button>
                  </div>
                </motion.div>
              ))}
            </div>
          )}
        </div>

        {/* Playback Queue */}
        {showQueue && (
          <div className="mb-6">
            <PlaybackQueue
              restaurantId={restaurantId || ""}
              sessionId={session?.id}
              isCollapsed={queueCollapsed}
              onToggleCollapse={() => setQueueCollapsed(!queueCollapsed)}
            />
          </div>
        )}

        {/* Filtros de Música */}
        <div className="bg-white/10 backdrop-blur-sm rounded-xl p-6 border border-white/20">
          <h2 className="text-xl font-bold text-white mb-4 flex items-center space-x-2">
            <Music className="w-5 h-5" />
            <span>Filtros de Música</span>
          </h2>
          <MusicFilters
            activeFilters={activeFilters}
            onFiltersChange={setActiveFilters}
          />
        </div>

        {/* Busca de Músicas */}
        <div className="bg-white/10 backdrop-blur-sm rounded-xl p-6 border border-white/20">
          <h2 className="text-xl font-bold text-white mb-4 flex items-center space-x-2">
            <Search className="w-5 h-5" />
            <span>Sugerir Música</span>
          </h2>
          <div className="flex space-x-3 mb-4">
            <input
              type="text"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              onKeyPress={(e) => e.key === "Enter" && searchSongs()}
              placeholder="Busque por música ou artista..."
              className="flex-1 px-4 py-3 bg-white/20 border border-white/30 rounded-lg text-white placeholder-purple-200 focus:outline-none focus:ring-2 focus:ring-purple-500"
              aria-label="Buscar músicas por título ou artista"
            />
            <button
              onClick={searchSongs}
              disabled={searchLoading || !searchQuery.trim()}
              className="px-6 py-3 bg-gradient-to-r from-purple-600 to-pink-600 text-white rounded-lg hover:from-purple-700 hover:to-pink-700 disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-2"
              aria-label="Buscar músicas"
            >
              {searchLoading ? (
                <RefreshCw className="w-4 h-4 animate-spin" />
              ) : (
                <Search className="w-4 h-4" />
              )}
              <span>Buscar</span>
            </button>
          </div>
        </div>

        {/* Footer */}
        <div className="text-center py-6">
          <p className="text-purple-300 text-sm">
            🎵 Powered by Sistema de Playlist Interativa
          </p>
        </div>
      </div>

      {/* Notificações de Gamificação */}
      <AnimatePresence>
        {showLevelUp && (
          <motion.div
            initial={{ opacity: 0, scale: 0.5, y: 50 }}
            animate={{ opacity: 1, scale: 1, y: 0 }}
            exit={{ opacity: 0, scale: 0.5, y: -50 }}
            className="fixed top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 z-50"
          >
            <div className="bg-gradient-to-r from-yellow-500 to-orange-500 text-white px-8 py-6 rounded-xl shadow-2xl text-center">
              <div className="text-4xl mb-2">🎉</div>
              <h3 className="text-xl font-bold mb-1">LEVEL UP!</h3>
              <p className="text-sm">
                Você alcançou o nível {userStats.level}!
              </p>
            </div>
          </motion.div>
        )}
        {showBadgeEarned && (
          <motion.div
            initial={{ opacity: 0, scale: 0.5, y: 50 }}
            animate={{ opacity: 1, scale: 1, y: 0 }}
            exit={{ opacity: 0, scale: 0.5, y: -50 }}
            className="fixed top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 z-50"
          >
            <div className="bg-gradient-to-r from-purple-500 to-pink-500 text-white px-8 py-6 rounded-xl shadow-2xl text-center">
              <div className="text-4xl mb-2">🏆</div>
              <h3 className="text-xl font-bold mb-1">NOVA CONQUISTA!</h3>
              <p className="text-sm">{showBadgeEarned}</p>
            </div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Name Input Modal */}
      <AnimatePresence>
        {showNameInput && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4"
          >
            <motion.div
              initial={{ opacity: 0, scale: 0.9, y: 20 }}
              animate={{ opacity: 1, scale: 1, y: 0 }}
              exit={{ opacity: 0, scale: 0.9, y: 20 }}
              className="bg-white rounded-xl p-6 max-w-md w-full shadow-2xl"
            >
              <div className="text-center mb-6">
                <User className="w-12 h-12 text-purple-500 mx-auto mb-3" />
                <h3 className="text-xl font-bold text-gray-800 mb-2">
                  Bem-vindo! 🎵
                </h3>
                <p className="text-gray-600 text-sm">
                  Como podemos te chamar? (Opcional)
                </p>
              </div>
              <div className="space-y-4">
                <input
                  type="text"
                  value={clientName}
                  onChange={(e) => setClientName(e.target.value)}
                  placeholder="Digite seu nome..."
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent outline-none"
                  onKeyPress={(e) => e.key === "Enter" && saveClientName()}
                  autoFocus
                  aria-label="Digite seu nome"
                />
                <div className="flex space-x-3">
                  <button
                    onClick={saveClientName}
                    disabled={!clientName.trim()}
                    className="flex-1 bg-purple-500 text-white py-3 rounded-lg font-medium hover:bg-purple-600 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                    aria-label="Confirmar nome"
                  >
                    Continuar
                  </button>
                  <button
                    onClick={() => setShowNameInput(false)}
                    className="flex-1 bg-gray-200 text-gray-700 py-3 rounded-lg font-medium hover:bg-gray-300 transition-colors"
                    aria-label="Pular entrada de nome"
                  >
                    Pular
                  </button>
                </div>
              </div>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Payment Modal */}
      {showPaymentModal && selectedSongForPayment && (
        <PaymentModal
          isOpen={showPaymentModal}
          onClose={() => {
            console.log("🚪 DEBUG: Modal fechando...");
            setShowPaymentModal(false);
            setSelectedSongForPayment(null);
          }}
          suggestion={{
            id: selectedSongForPayment.id,
            title: selectedSongForPayment.title,
            artist: selectedSongForPayment.artist,
            thumbnailUrl: selectedSongForPayment.thumbnailUrl,
            duration: selectedSongForPayment.duration,
          }}
          sessionId={session?.sessionToken || ""}
          restaurantId={restaurantId || ""}
          onPaymentSuccess={handlePaymentSuccess}
        />
      )}

      {/* Karaoke Player */}
      {karaokeData && (
        <KaraokePlayer
          isOpen={showKaraoke}
          onClose={() => {
            setShowKaraoke(false);
            setKaraokeData(null);
          }}
          suggestion={karaokeData}
          sessionId={session?.id || ""}
          onVoteRequest={() => {
            toast.success(
              "Votação solicitada! Outros clientes podem votar agora! 🗳️"
            );
          }}
        />
      )}

      {/* Voting Widget */}
      {session && (
        <VotingWidget
          sessionId={session.id}
          restaurantId={restaurantId || ""}
          onVoteSubmitted={(rating) => {
            awardPoints("vote", 5);
            checkForNewBadges();
          }}
        />
      )}

      {/* Rewards Panel */}
      {showRewards && session && (
        <RewardsPanel
          sessionId={session.id}
          restaurantId={restaurantId || ""}
          isOpen={showRewards}
          onClose={() => setShowRewards(false)}
        />
      )}
    </div>
  );
};

export default ClientInterface;
