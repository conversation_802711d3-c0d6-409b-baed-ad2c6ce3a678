import React, { useState, useEffect } from "react";
import { Routes, Route, Navigate, Link } from "react-router-dom";
import { motion } from "framer-motion";
import {
  BarChart3,
  Music,
  Users,
  Settings,
  PlayCircle,
  TrendingUp,
  Clock,
  Star,
  Plus,
  Trash2,
  Edit,
  Save,
  X,
  Youtube,
  Play,
  Pause,
  AlertCircle,
  QrCode,
  Building2,
  Home,
  LogOut,
  User,
} from "lucide-react";

// Componentes
import LoadingSpinner from "@/components/ui/LoadingSpinner";
import YouTubePlaylistConfig from "@/components/admin/YouTubePlaylistConfig";
import AdvancedModeration from "@/components/admin/AdvancedModeration";
import RestaurantSettings from "@/components/admin/RestaurantSettings";
import QRCodeManager from "@/components/admin/QRCodeManager";
import QueueManager from "@/components/admin/QueueManager";
import AdvancedAnalytics from "@/components/admin/AdvancedAnalytics";
import PlaylistManager from "@/components/admin/PlaylistManager";
import TenantManager from "@/components/admin/TenantManager";
import RestaurantManagement from "@/components/admin/RestaurantManagement";
import SuggestionsManagement from "@/components/admin/SuggestionsManagement";

// Componente de gerenciamento de playlist
const LocalPlaylistManager: React.FC = () => {
  const [playlist, setPlaylist] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [editingId, setEditingId] = useState<string | null>(null);
  const [newSong, setNewSong] = useState({
    title: "",
    artist: "",
    youtubeId: "",
  });

  useEffect(() => {
    fetchPlaylist();
  }, []);

  const fetchPlaylist = async () => {
    try {
      const response = await fetch(
        "http://localhost:8001/api/v1/restaurants/demo-restaurant/playlist"
      );
      const data = await response.json();
      setPlaylist(data.results || []);
    } catch (error) {
      console.error("Error fetching playlist:", error);
    } finally {
      setLoading(false);
    }
  };

  const addSong = async () => {
    if (!newSong.title || !newSong.artist || !newSong.youtubeId) return;

    // Em uma implementação real, isso seria uma API call
    const song = {
      id: newSong.youtubeId,
      title: newSong.title,
      artist: newSong.artist,
      duration: 180,
      formattedDuration: "3:00",
      thumbnailUrl: `https://i.ytimg.com/vi/${newSong.youtubeId}/mqdefault.jpg`,
      channelName: newSong.artist,
      viewCount: 1000000,
      publishedAt: new Date().toISOString(),
    };

    setPlaylist([...playlist, song]);
    setNewSong({ title: "", artist: "", youtubeId: "" });
  };

  const removeSong = (songId: string) => {
    setPlaylist(playlist.filter((song) => song.id !== songId));
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <LoadingSpinner />
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-bold text-gray-900 dark:text-white">
          Gerenciar Playlist
        </h2>
        <div className="text-sm text-gray-600 dark:text-gray-400">
          {playlist.length} músicas na playlist
        </div>
      </div>

      {/* Add New Song */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
        <h3 className="text-lg font-semibold mb-4">Adicionar Nova Música</h3>
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <input
            type="text"
            placeholder="Título da música"
            value={newSong.title}
            onChange={(e) => setNewSong({ ...newSong, title: e.target.value })}
            className="border border-gray-300 dark:border-gray-600 rounded-lg px-3 py-2 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
          />
          <input
            type="text"
            placeholder="Artista"
            value={newSong.artist}
            onChange={(e) => setNewSong({ ...newSong, artist: e.target.value })}
            className="border border-gray-300 dark:border-gray-600 rounded-lg px-3 py-2 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
          />
          <input
            type="text"
            placeholder="YouTube ID"
            value={newSong.youtubeId}
            onChange={(e) =>
              setNewSong({ ...newSong, youtubeId: e.target.value })
            }
            className="border border-gray-300 dark:border-gray-600 rounded-lg px-3 py-2 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
          />
          <button
            onClick={addSong}
            className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 flex items-center justify-center space-x-2"
          >
            <Plus className="w-4 h-4" />
            <span>Adicionar</span>
          </button>
        </div>
      </div>

      {/* Playlist */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow">
        <div className="p-6">
          <div className="space-y-4">
            {playlist.map((song, index) => (
              <div
                key={song.id}
                className="flex items-center justify-between p-4 bg-gray-50 dark:bg-gray-700 rounded-lg"
              >
                <div className="flex items-center space-x-4">
                  <div className="flex-shrink-0 w-8 h-8 bg-blue-600 text-white rounded-full flex items-center justify-center text-sm font-bold">
                    {index + 1}
                  </div>
                  <img
                    src={song.thumbnailUrl}
                    alt={song.title}
                    className="w-16 h-12 object-cover rounded"
                  />
                  <div>
                    <h4 className="font-medium text-gray-900 dark:text-white">
                      {song.title}
                    </h4>
                    <p className="text-sm text-gray-600 dark:text-gray-400">
                      {song.artist}
                    </p>
                    <p className="text-xs text-gray-500 dark:text-gray-500">
                      {song.formattedDuration}
                    </p>
                  </div>
                </div>
                <div className="flex items-center space-x-2">
                  <a
                    href={`https://youtube.com/watch?v=${song.id}`}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="p-2 text-red-600 hover:bg-red-100 dark:hover:bg-red-900/20 rounded-lg"
                  >
                    <Youtube className="w-4 h-4" />
                  </a>
                  <button
                    onClick={() => removeSong(song.id)}
                    className="p-2 text-red-600 hover:bg-red-100 dark:hover:bg-red-900/20 rounded-lg"
                  >
                    <Trash2 className="w-4 h-4" />
                  </button>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};

// Páginas placeholder do dashboard
const DashboardHome: React.FC = () => {
  const stats = [
    {
      title: "Sugestões Hoje",
      value: "24",
      change: "+12%",
      icon: Music,
      color: "text-blue-600 dark:text-blue-400",
      bgColor: "bg-blue-100 dark:bg-blue-900/20",
    },
    {
      title: "Clientes Ativos",
      value: "8",
      change: "+5%",
      icon: Users,
      color: "text-green-600 dark:text-green-400",
      bgColor: "bg-green-100 dark:bg-green-900/20",
    },
    {
      title: "Músicas na Fila",
      value: "12",
      change: "0%",
      icon: PlayCircle,
      color: "text-purple-600 dark:text-purple-400",
      bgColor: "bg-purple-100 dark:bg-purple-900/20",
    },
    {
      title: "Avaliação Média",
      value: "4.8",
      change: "+0.2",
      icon: Star,
      color: "text-yellow-600 dark:text-yellow-400",
      bgColor: "bg-yellow-100 dark:bg-yellow-900/20",
    },
  ];

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
          Dashboard
        </h1>
        <p className="text-gray-600 dark:text-gray-400">
          Visão geral do seu restaurante
        </p>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {stats.map((stat, index) => (
          <motion.div
            key={index}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: index * 0.1 }}
            className="card p-6"
          >
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600 dark:text-gray-400 mb-1">
                  {stat.title}
                </p>
                <p className="text-2xl font-bold text-gray-900 dark:text-white">
                  {stat.value}
                </p>
                <p
                  className={`text-sm ${
                    stat.change.startsWith("+")
                      ? "text-green-600"
                      : "text-gray-600"
                  }`}
                >
                  {stat.change} vs ontem
                </p>
              </div>
              <div
                className={`w-12 h-12 rounded-lg ${stat.bgColor} flex items-center justify-center`}
              >
                <stat.icon className={`w-6 h-6 ${stat.color}`} />
              </div>
            </div>
          </motion.div>
        ))}
      </div>

      {/* Placeholder para gráficos e outras seções */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <div className="card p-6">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
            Atividade Recente
          </h3>
          <div className="space-y-3">
            {[1, 2, 3, 4].map((i) => (
              <div
                key={i}
                className="flex items-center space-x-3 p-3 bg-gray-50 dark:bg-gray-700 rounded-lg"
              >
                <div className="w-8 h-8 bg-primary-100 dark:bg-primary-900 rounded-full flex items-center justify-center">
                  <Music className="w-4 h-4 text-primary-600 dark:text-primary-400" />
                </div>
                <div className="flex-1">
                  <p className="text-sm font-medium text-gray-900 dark:text-white">
                    Nova sugestão: "Bohemian Rhapsody"
                  </p>
                  <p className="text-xs text-gray-500 dark:text-gray-400">
                    há {i} minutos
                  </p>
                </div>
              </div>
            ))}
          </div>
        </div>

        <div className="card p-6">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
            Fila Atual
          </h3>
          <div className="space-y-3">
            {[1, 2, 3, 4].map((i) => (
              <div
                key={i}
                className="flex items-center space-x-3 p-3 bg-gray-50 dark:bg-gray-700 rounded-lg"
              >
                <div className="w-6 h-6 bg-gray-200 dark:bg-gray-600 rounded text-xs flex items-center justify-center font-medium">
                  {i}
                </div>
                <div className="flex-1">
                  <p className="text-sm font-medium text-gray-900 dark:text-white">
                    Música {i}
                  </p>
                  <p className="text-xs text-gray-500 dark:text-gray-400">
                    3:45 • 5 votos
                  </p>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};

const Dashboard: React.FC = () => {
  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      {/* Header */}
      <header className="bg-white dark:bg-gray-800 shadow-sm border-b border-gray-200 dark:border-gray-700">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <Link
              to="/admin"
              className="flex items-center space-x-3 hover:opacity-80 transition-opacity"
            >
              <div className="w-8 h-8 bg-primary-600 rounded-lg flex items-center justify-center">
                <Music className="w-5 h-5 text-white" />
              </div>
              <h1 className="text-xl font-semibold text-gray-900 dark:text-white">
                Restaurant Playlist Admin
              </h1>
            </Link>

            <div className="flex items-center space-x-4">
              <div className="text-sm text-gray-600 dark:text-gray-400">
                Admin
              </div>
              <div className="flex items-center space-x-2">
                <div className="w-8 h-8 bg-gray-300 dark:bg-gray-600 rounded-full flex items-center justify-center">
                  <User className="w-4 h-4 text-gray-600 dark:text-gray-300" />
                </div>
                <Link
                  to="/"
                  className="flex items-center space-x-1 px-3 py-2 text-sm text-white bg-red-500 hover:bg-red-600 rounded-lg transition-colors font-medium"
                  title="Sair do Admin"
                >
                  <LogOut className="w-4 h-4" />
                  <span>Sair</span>
                </Link>
              </div>
            </div>
          </div>
        </div>
      </header>

      {/* Navigation */}
      <nav className="bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex space-x-8">
            {[
              { name: "Playlists", icon: Music, path: "/admin/playlists" },
              { name: "YouTube", icon: Youtube, path: "/admin/youtube" },
              {
                name: "Restaurantes",
                icon: Building2,
                path: "/admin/restaurants",
              },
              { name: "QR Codes", icon: QrCode, path: "/admin/qrcode" },
              { name: "Fila", icon: PlayCircle, path: "/admin/queue" },
              {
                name: "Sugestões",
                icon: TrendingUp,
                path: "/admin/suggestions",
              },
              {
                name: "Moderação",
                icon: AlertCircle,
                path: "/admin/moderation",
              },
              { name: "Analytics", icon: BarChart3, path: "/admin/analytics" },
              {
                name: "Configurações",
                icon: Settings,
                path: "/admin/settings",
              },
              {
                name: "Restaurantes",
                icon: Home,
                path: "/admin/tenants",
              },
            ].map((item) => (
              <Link
                key={item.name}
                to={item.path}
                className="flex items-center space-x-2 py-4 px-1 border-b-2 text-sm font-medium transition-colors border-transparent text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 hover:border-gray-300"
              >
                <item.icon className="w-4 h-4 flex-shrink-0" />
                <span className="truncate">{item.name}</span>
              </Link>
            ))}
          </div>
        </div>
      </nav>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <Routes>
          <Route path="/" element={<DashboardHome />} />
          <Route path="/dashboard" element={<DashboardHome />} />
          <Route path="/playlists" element={<PlaylistManager />} />
          <Route path="/youtube" element={<YouTubePlaylistConfig />} />
          <Route path="/moderation" element={<AdvancedModeration />} />
          <Route path="/qrcode" element={<QRCodeManager />} />
          <Route path="/queue" element={<QueueManager />} />
          <Route path="/analytics" element={<AdvancedAnalytics />} />
          <Route path="/tenants" element={<TenantManager />} />
          <Route path="/restaurants" element={<RestaurantManagement />} />
          <Route path="/suggestions" element={<SuggestionsManagement />} />
          <Route path="/settings" element={<RestaurantSettings />} />
          <Route path="*" element={<DashboardHome />} />
        </Routes>
      </main>
    </div>
  );
};

export default Dashboard;
