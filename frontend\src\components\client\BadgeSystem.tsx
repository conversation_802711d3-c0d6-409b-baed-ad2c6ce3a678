import React, { useState, useEffect } from "react";
import { motion, AnimatePresence } from "framer-motion";
import {
  Award,
  Star,
  Music,
  ThumbsUp,
  Crown,
  Trophy,
  Target,
  Zap,
  Heart,
  TrendingUp,
} from "lucide-react";
import { toast } from "react-hot-toast";
import apiService from "@/services/api";

interface Badge {
  id: string;
  name: string;
  description: string;
  icon: React.ComponentType<any>;
  color: string;
  bgColor: string;
  requirement: number;
  currentProgress: number;
  unlocked: boolean;
  unlockedAt?: string;
  category: "suggestions" | "votes" | "engagement" | "special";
}

interface BadgeSystemProps {
  restaurantId: string;
  sessionId: string;
}

const BadgeSystem: React.FC<BadgeSystemProps> = ({
  restaurantId,
  sessionId,
}) => {
  const [badges, setBadges] = useState<Badge[]>([]);
  const [showBadgeModal, setShowBadgeModal] = useState(false);
  const [newlyUnlockedBadge, setNewlyUnlockedBadge] = useState<Badge | null>(
    null
  );
  const [userStats, setUserStats] = useState({
    totalSuggestions: 0,
    totalVotes: 0,
    approvedSuggestions: 0,
    consecutiveDays: 0,
    topVotedSongs: 0,
  });

  // Definir badges disponíveis
  const availableBadges: Omit<
    Badge,
    "currentProgress" | "unlocked" | "unlockedAt"
  >[] = [
    {
      id: "first_suggestion",
      name: "Primeira Sugestão",
      description: "Sugeriu sua primeira música",
      icon: Music,
      color: "text-blue-600",
      bgColor: "bg-blue-100",
      requirement: 1,
      category: "suggestions",
    },
    {
      id: "music_lover",
      name: "Amante da Música",
      description: "Sugeriu 10 músicas",
      icon: Heart,
      color: "text-red-600",
      bgColor: "bg-red-100",
      requirement: 10,
      category: "suggestions",
    },
    {
      id: "dj_master",
      name: "DJ Master",
      description: "Sugeriu 50 músicas",
      icon: Crown,
      color: "text-purple-600",
      bgColor: "bg-purple-100",
      requirement: 50,
      category: "suggestions",
    },
    {
      id: "first_vote",
      name: "Primeiro Voto",
      description: "Votou pela primeira vez",
      icon: ThumbsUp,
      color: "text-green-600",
      bgColor: "bg-green-100",
      requirement: 1,
      category: "votes",
    },
    {
      id: "active_voter",
      name: "Eleitor Ativo",
      description: "Fez 25 votos",
      icon: Target,
      color: "text-orange-600",
      bgColor: "bg-orange-100",
      requirement: 25,
      category: "votes",
    },
    {
      id: "vote_champion",
      name: "Campeão dos Votos",
      description: "Fez 100 votos",
      icon: Trophy,
      color: "text-yellow-600",
      bgColor: "bg-yellow-100",
      requirement: 100,
      category: "votes",
    },
    {
      id: "hit_maker",
      name: "Criador de Hits",
      description: "Teve 5 sugestões aprovadas",
      icon: Star,
      color: "text-indigo-600",
      bgColor: "bg-indigo-100",
      requirement: 5,
      category: "engagement",
    },
    {
      id: "trending_master",
      name: "Mestre das Tendências",
      description: "Teve uma música no top 3 mais votadas",
      icon: TrendingUp,
      color: "text-pink-600",
      bgColor: "bg-pink-100",
      requirement: 1,
      category: "special",
    },
    {
      id: "speed_demon",
      name: "Demônio da Velocidade",
      description: "Sugeriu 5 músicas em menos de 1 minuto",
      icon: Zap,
      color: "text-cyan-600",
      bgColor: "bg-cyan-100",
      requirement: 5,
      category: "special",
    },
    {
      id: "legend",
      name: "Lenda",
      description: "Desbloqueou todos os outros badges",
      icon: Award,
      color: "text-gradient",
      bgColor: "bg-gradient-to-r from-yellow-400 to-orange-500",
      requirement: 8,
      category: "special",
    },
  ];

  useEffect(() => {
    loadUserStats();
    loadBadges();
  }, [restaurantId, sessionId]);

  const loadUserStats = async () => {
    try {
      const response = await apiService.client.get(
        `/client/${sessionId}/stats`
      );
      setUserStats(response.data.stats);

      // Carregar badges também
      loadBadgesFromAPI();
    } catch (error) {
      console.error("Erro ao carregar estatísticas:", error);
      // Fallback para dados mock
      setUserStats({
        totalSuggestions: 3,
        totalVotes: 12,
        approvedSuggestions: 1,
        consecutiveDays: 2,
        topVotedSongs: 0,
      });
      loadBadges();
    }
  };

  const loadBadgesFromAPI = async () => {
    try {
      const response = await apiService.client.get(
        `/client/${sessionId}/badges`
      );
      const data = response.data;

        // Converter badges da API para o formato do componente
        const convertedBadges = data.badges.map((badge: any) => ({
          ...badge,
          icon: getIconComponent(badge.icon),
          color: `text-${badge.color}-600`,
          bgColor: `bg-${badge.color}-100`,
          currentProgress: badge.currentProgress,
          unlocked: badge.unlocked,
          unlockedAt: badge.unlockedAt,
        }));

        setBadges(convertedBadges);

        // Verificar se há novos badges desbloqueados
        const newlyUnlocked = convertedBadges.find(
          (badge: any) =>
            badge.unlocked &&
            !badges.find((b) => b.id === badge.id && b.unlocked)
        );

        if (newlyUnlocked) {
          setNewlyUnlockedBadge(newlyUnlocked);
          toast.success(`🏆 Badge desbloqueado: ${newlyUnlocked.name}!`, {
            duration: 5000,
          });
        }
      }
    } catch (error) {
      console.error("Erro ao carregar badges:", error);
      loadBadges(); // Fallback
    }
  };

  const getIconComponent = (iconName: string) => {
    const iconMap: { [key: string]: React.ComponentType<any> } = {
      Music,
      Heart,
      Crown,
      ThumbsUp,
      Target,
      Trophy,
      Star,
      TrendingUp,
      Award,
      Zap,
    };
    return iconMap[iconName] || Award;
  };

  const loadBadges = () => {
    // Calcular progresso dos badges baseado nas estatísticas
    const updatedBadges = availableBadges.map((badge) => {
      let currentProgress = 0;
      let unlocked = false;

      switch (badge.id) {
        case "first_suggestion":
        case "music_lover":
        case "dj_master":
          currentProgress = userStats.totalSuggestions;
          break;
        case "first_vote":
        case "active_voter":
        case "vote_champion":
          currentProgress = userStats.totalVotes;
          break;
        case "hit_maker":
          currentProgress = userStats.approvedSuggestions;
          break;
        case "trending_master":
          currentProgress = userStats.topVotedSongs;
          break;
        case "speed_demon":
          // Lógica específica para velocidade (implementar no backend)
          currentProgress = 0;
          break;
        case "legend":
          // Contar badges desbloqueados
          const unlockedCount = availableBadges.filter((b) => {
            if (b.id === "legend") return false;
            let progress = 0;
            switch (b.category) {
              case "suggestions":
                progress = userStats.totalSuggestions;
                break;
              case "votes":
                progress = userStats.totalVotes;
                break;
              case "engagement":
                progress = userStats.approvedSuggestions;
                break;
            }
            return progress >= b.requirement;
          }).length;
          currentProgress = unlockedCount;
          break;
      }

      unlocked = currentProgress >= badge.requirement;

      return {
        ...badge,
        currentProgress,
        unlocked,
        unlockedAt: unlocked ? new Date().toISOString() : undefined,
      };
    });

    setBadges(updatedBadges);

    // Verificar se há novos badges desbloqueados
    const newlyUnlocked = updatedBadges.find(
      (badge) =>
        badge.unlocked && !badges.find((b) => b.id === badge.id && b.unlocked)
    );

    if (newlyUnlocked) {
      setNewlyUnlockedBadge(newlyUnlocked);
      toast.success(`🏆 Badge desbloqueado: ${newlyUnlocked.name}!`, {
        duration: 5000,
      });
    }
  };

  const getBadgesByCategory = (category: Badge["category"]) => {
    return badges.filter((badge) => badge.category === category);
  };

  const getProgressPercentage = (badge: Badge) => {
    return Math.min((badge.currentProgress / badge.requirement) * 100, 100);
  };

  const unlockedBadges = badges.filter((badge) => badge.unlocked);
  const totalBadges = badges.length;

  return (
    <div className="space-y-6">
      {/* Header com estatísticas */}
      <div className="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
            Suas Conquistas
          </h3>
          <div className="text-sm text-gray-600 dark:text-gray-400">
            {unlockedBadges.length}/{totalBadges} badges
          </div>
        </div>

        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
          <div className="text-center">
            <div className="text-2xl font-bold text-blue-600">
              {userStats.totalSuggestions}
            </div>
            <div className="text-sm text-gray-600 dark:text-gray-400">
              Sugestões
            </div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-green-600">
              {userStats.totalVotes}
            </div>
            <div className="text-sm text-gray-600 dark:text-gray-400">
              Votos
            </div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-purple-600">
              {userStats.approvedSuggestions}
            </div>
            <div className="text-sm text-gray-600 dark:text-gray-400">
              Aprovadas
            </div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-orange-600">
              {unlockedBadges.length}
            </div>
            <div className="text-sm text-gray-600 dark:text-gray-400">
              Badges
            </div>
          </div>
        </div>

        {/* Barra de progresso geral */}
        <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
          <div
            className="bg-gradient-to-r from-blue-500 to-purple-600 h-2 rounded-full transition-all duration-300"
            style={{
              width: `${(unlockedBadges.length / totalBadges) * 100}%`,
            }}
          />
        </div>
      </div>

      {/* Badges por categoria */}
      {["suggestions", "votes", "engagement", "special"].map((category) => (
        <div
          key={category}
          className="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm"
        >
          <h4 className="text-md font-medium text-gray-900 dark:text-white mb-4 capitalize">
            {category === "suggestions" && "Sugestões"}
            {category === "votes" && "Votação"}
            {category === "engagement" && "Engajamento"}
            {category === "special" && "Especiais"}
          </h4>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {getBadgesByCategory(category as Badge["category"]).map((badge) => {
              const Icon = badge.icon;
              const progress = getProgressPercentage(badge);

              return (
                <motion.div
                  key={badge.id}
                  className={`relative p-4 rounded-lg border-2 transition-all duration-200 ${
                    badge.unlocked
                      ? "border-green-200 bg-green-50 dark:bg-green-900/20"
                      : "border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-900/20"
                  }`}
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                >
                  {badge.unlocked && (
                    <div className="absolute -top-2 -right-2 w-6 h-6 bg-green-500 rounded-full flex items-center justify-center">
                      <Award className="w-3 h-3 text-white" />
                    </div>
                  )}

                  <div className="flex items-center space-x-3 mb-3">
                    <div
                      className={`w-10 h-10 rounded-lg ${
                        badge.bgColor
                      } flex items-center justify-center ${
                        badge.unlocked ? "" : "opacity-50"
                      }`}
                    >
                      <Icon className={`w-5 h-5 ${badge.color}`} />
                    </div>
                    <div className="flex-1">
                      <h5
                        className={`font-medium ${
                          badge.unlocked
                            ? "text-gray-900 dark:text-white"
                            : "text-gray-500 dark:text-gray-400"
                        }`}
                      >
                        {badge.name}
                      </h5>
                      <p className="text-sm text-gray-600 dark:text-gray-400">
                        {badge.description}
                      </p>
                    </div>
                  </div>

                  {/* Barra de progresso */}
                  <div className="space-y-2">
                    <div className="flex justify-between text-sm">
                      <span className="text-gray-600 dark:text-gray-400">
                        Progresso
                      </span>
                      <span className="text-gray-600 dark:text-gray-400">
                        {badge.currentProgress}/{badge.requirement}
                      </span>
                    </div>
                    <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                      <div
                        className={`h-2 rounded-full transition-all duration-300 ${
                          badge.unlocked ? "bg-green-500" : "bg-blue-500"
                        }`}
                        style={{ width: `${progress}%` }}
                      />
                    </div>
                  </div>
                </motion.div>
              );
            })}
          </div>
        </div>
      ))}

      {/* Modal de badge desbloqueado */}
      <AnimatePresence>
        {newlyUnlockedBadge && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50"
            onClick={() => setNewlyUnlockedBadge(null)}
          >
            <motion.div
              initial={{ scale: 0.5, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              exit={{ scale: 0.5, opacity: 0 }}
              className="bg-white dark:bg-gray-800 rounded-lg p-8 max-w-md mx-4 text-center"
              onClick={(e) => e.stopPropagation()}
            >
              <div className="mb-4">
                <div className="w-20 h-20 mx-auto bg-gradient-to-r from-yellow-400 to-orange-500 rounded-full flex items-center justify-center mb-4">
                  <Award className="w-10 h-10 text-white" />
                </div>
                <h3 className="text-xl font-bold text-gray-900 dark:text-white mb-2">
                  Badge Desbloqueado!
                </h3>
                <h4 className="text-lg font-semibold text-gray-800 dark:text-gray-200">
                  {newlyUnlockedBadge.name}
                </h4>
                <p className="text-gray-600 dark:text-gray-400 mt-2">
                  {newlyUnlockedBadge.description}
                </p>
              </div>
              <button
                onClick={() => setNewlyUnlockedBadge(null)}
                className="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
              >
                Continuar
              </button>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
};

export default BadgeSystem;
