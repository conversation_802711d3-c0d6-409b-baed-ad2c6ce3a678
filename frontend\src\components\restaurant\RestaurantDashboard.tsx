import React, { useState, useEffect, createContext, useContext } from "react";
import {
  Routes,
  Route,
  Navigate,
  Link,
  useNavigate,
  useLocation,
} from "react-router-dom";

// Contexto para forçar restaurantId correto
const RestaurantContext = createContext<{ restaurantId: string }>({
  restaurantId: "demo-restaurant",
});

export const useRestaurantContext = () => useContext(RestaurantContext);
import { motion } from "framer-motion";
import {
  BarChart3,
  Music,
  Users,
  Settings,
  PlayCircle,
  TrendingUp,
  Clock,
  Star,
  Plus,
  Trash2,
  Edit,
  Save,
  Calendar,
  X,
  Youtube,
  Play,
  Pause,
  AlertCircle,
  QrCode,
  Building2,
  Home,
  LogOut,
  User,
} from "lucide-react";

// Componentes
import LoadingSpinner from "@/components/ui/LoadingSpinner";
import { toast } from "react-hot-toast";
import { useAuth } from "@/store";
import apiService from "@/services/api";

// TODOS OS COMPONENTES DO RESTAURANT (ATUALIZADOS E COMPLETOS)
import SuggestionsManager from "@/components/restaurant/SuggestionsManager";
import PlaybackController from "@/components/restaurant/PlaybackController";
import AdvancedPlaylistAnalytics from "@/components/restaurant/AdvancedPlaylistAnalytics";
import ProblematicTracksAlert from "@/components/restaurant/ProblematicTracksAlert";
import RestaurantProfile from "@/components/restaurant/RestaurantProfile";
import QRCodeManager from "@/components/restaurant/QRCodeManager";
import PlaylistManager from "@/components/restaurant/PlaylistManager";
import PlaylistScheduler from "@/components/restaurant/PlaylistScheduler";
import MusicPlayer from "@/components/restaurant/MusicPlayer";
import RestaurantAnalytics from "@/components/restaurant/RestaurantAnalytics";
import CompetitiveAnalytics from "@/components/restaurant/CompetitiveAnalytics";

// Componentes temporários do admin (só até criar no restaurant)
import AdvancedModeration from "@/components/admin/AdvancedModeration";
import RestaurantSettings from "@/components/admin/RestaurantSettings";
import QueueManager from "@/components/admin/QueueManager";

// Dashboard Home com dados reais da API
const DashboardHome: React.FC = () => {
  const [stats, setStats] = useState({
    totalSuggestions: 0,
    totalVotes: 0,
    pendingSuggestions: 0,
    dailyStats: { suggestions: 0, votes: 0 },
    totalPlays: 0,
    activeUsers: 0,
    averageRating: 0,
    growthRate: 0,
    peakHour: "0:00",
    topGenre: "N/A",
  });
  const [loading, setLoading] = useState(true);
  const [recentActivity, setRecentActivity] = useState([]);
  const [currentQueue, setCurrentQueue] = useState([]);

  const restaurantId = "demo-restaurant"; // Em produção, pegar do contexto/auth

  useEffect(() => {
    loadDashboardData();
  }, []);

  const loadDashboardData = async () => {
    try {
      setLoading(true);

      // Carregar estatísticas usando apiService
      try {
        const statsData = await apiService.getAnalytics(restaurantId);
        // A API retorna summary diretamente, não stats
        setStats({
          totalSuggestions: statsData.summary?.totalSuggestions || 0,
          totalVotes: statsData.summary?.totalVotes || 0,
          pendingSuggestions: 0, // Será carregado das sugestões
          dailyStats: {
            suggestions: statsData.summary?.totalSuggestions || 0,
            votes: statsData.summary?.totalVotes || 0,
          },
          totalPlays: statsData.summary?.totalPlays || 0,
          activeUsers: statsData.summary?.activeUsers || 0,
          averageRating: statsData.summary?.averageRating || 0,
          growthRate: statsData.summary?.growthRate || 0,
          peakHour: statsData.summary?.peakHour || "0:00",
          topGenre: statsData.summary?.topGenre || "N/A",
        });
      } catch (error) {
        console.warn("Erro ao carregar estatísticas:", error);
        // Manter valores padrão já definidos no estado inicial
      }

      // Carregar sugestões recentes usando apiService
      try {
        const suggestionsData = await apiService.getSuggestions(restaurantId, {
          limit: 4,
          // Removido status: "all" para evitar erro
        });
        setRecentActivity(suggestionsData.suggestions || []);
      } catch (error) {
        console.warn("Erro ao carregar sugestões:", error);
        // Definir atividade mock para não quebrar a interface
        setRecentActivity([
          {
            id: "demo-1",
            title: "Exemplo de Música",
            artist: "Artista Demo",
            createdAt: new Date().toISOString(),
          },
        ]);
      }

      // Carregar fila atual usando apiService
      try {
        const queueData = await apiService.getPlayQueue(restaurantId);
        setCurrentQueue(queueData.queue?.slice(0, 4) || []);
      } catch (error) {
        console.warn("Erro ao carregar fila:", error);
        // Definir fila mock para não quebrar a interface
        setCurrentQueue([
          {
            id: "queue-demo-1",
            title: "Música na Fila",
            artist: "Artista",
            upvotes: 5,
            downvotes: 1,
          },
        ]);
      }
    } catch (error) {
      console.error("Erro ao carregar dados do dashboard:", error);
      // Não mostrar toast de erro para não ser intrusivo
      console.log("💡 Dashboard carregado com dados de fallback");
    } finally {
      setLoading(false);
    }
  };

  const dashboardStats = [
    {
      title: "Sugestões Hoje",
      value: stats.dailyStats.suggestions.toString(),
      change: "+12%",
      icon: Music,
      color: "text-blue-600 dark:text-blue-400",
      bgColor: "bg-blue-100 dark:bg-blue-900/20",
    },
    {
      title: "Total de Votos",
      value: stats.totalVotes.toString(),
      change: `+${stats.dailyStats.votes}`,
      icon: Users,
      color: "text-green-600 dark:text-green-400",
      bgColor: "bg-green-100 dark:bg-green-900/20",
    },
    {
      title: "Pendentes",
      value: stats.pendingSuggestions.toString(),
      change: "0%",
      icon: Clock,
      color: "text-yellow-600 dark:text-yellow-400",
      bgColor: "bg-yellow-100 dark:bg-yellow-900/20",
    },
    {
      title: "Total Sugestões",
      value: stats.totalSuggestions.toString(),
      change: "+0.2",
      icon: Star,
      color: "text-purple-600 dark:text-purple-400",
      bgColor: "bg-purple-100 dark:bg-purple-900/20",
    },
  ];

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <LoadingSpinner size="lg" />
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
          Dashboard do Restaurante
        </h1>
        <p className="text-gray-600 dark:text-gray-400">
          Visão geral das atividades e estatísticas
        </p>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {dashboardStats.map((stat, index) => (
          <motion.div
            key={index}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: index * 0.1 }}
            className="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm"
          >
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600 dark:text-gray-400 mb-1">
                  {stat.title}
                </p>
                <p className="text-2xl font-bold text-gray-900 dark:text-white">
                  {stat.value}
                </p>
                <p className="text-sm text-gray-600 dark:text-gray-400">
                  {stat.change} hoje
                </p>
              </div>
              <div
                className={`w-12 h-12 rounded-lg ${stat.bgColor} flex items-center justify-center`}
              >
                <stat.icon className={`w-6 h-6 ${stat.color}`} />
              </div>
            </div>
          </motion.div>
        ))}
      </div>

      {/* Alertas de músicas problemáticas */}
      <div className="mb-6">
        <ProblematicTracksAlert />
      </div>

      {/* Seções de atividade */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <div className="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
            Atividade Recente
          </h3>
          <div className="space-y-3">
            {recentActivity.length > 0 ? (
              recentActivity.map((activity, i) => (
                <div
                  key={activity.id || i}
                  className="flex items-center space-x-3 p-3 bg-gray-50 dark:bg-gray-700 rounded-lg"
                >
                  <div className="w-8 h-8 bg-blue-100 dark:bg-blue-900/30 rounded-full flex items-center justify-center">
                    <Music className="w-4 h-4 text-blue-600 dark:text-blue-400" />
                  </div>
                  <div className="flex-1">
                    <p className="text-sm font-medium text-gray-900 dark:text-white">
                      Nova sugestão: "{activity.title}"
                    </p>
                    <p className="text-xs text-gray-500 dark:text-gray-400">
                      {activity.artist} •{" "}
                      {new Date(activity.createdAt).toLocaleTimeString()}
                    </p>
                  </div>
                </div>
              ))
            ) : (
              <p className="text-gray-500 dark:text-gray-400 text-center py-4">
                Nenhuma atividade recente
              </p>
            )}
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
            Fila Atual
          </h3>
          <div className="space-y-3">
            {currentQueue.length > 0 ? (
              currentQueue.map((item, i) => (
                <div
                  key={item.id || i}
                  className="flex items-center space-x-3 p-3 bg-gray-50 dark:bg-gray-700 rounded-lg"
                >
                  <div className="w-6 h-6 bg-gray-200 dark:bg-gray-600 rounded text-xs flex items-center justify-center font-medium">
                    {i + 1}
                  </div>
                  <div className="flex-1">
                    <p className="text-sm font-medium text-gray-900 dark:text-white">
                      {item.title}
                    </p>
                    <p className="text-xs text-gray-500 dark:text-gray-400">
                      {item.artist} • {item.upvotes - item.downvotes} votos
                    </p>
                  </div>
                </div>
              ))
            ) : (
              <p className="text-gray-500 dark:text-gray-400 text-center py-4">
                Fila vazia
              </p>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

// Componente principal do Dashboard (copiado do admin)
const RestaurantDashboard: React.FC = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const { user, setUser, setAuthToken } = useAuth();
  const [restaurantName] = useState("Restaurante Demo");

  // FORÇAR restaurantId correto para evitar conflitos
  const restaurantId = "demo-restaurant";

  const handleLogout = () => {
    setUser(null);
    setAuthToken(null);
    localStorage.removeItem("authToken");
    toast.success("Logout realizado com sucesso!");
    navigate("/");
  };

  useEffect(() => {
    console.log("🏪 RestaurantDashboard carregado!");
    console.log("🏪 RestaurantId fixo:", restaurantId);
    console.log("🏪 Location atual:", location.pathname);
  }, [location.pathname]);

  return (
    <RestaurantContext.Provider value={{ restaurantId }}>
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
        {/* Header */}
        <header className="bg-white dark:bg-gray-800 shadow-sm border-b border-gray-200 dark:border-gray-700">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="flex justify-between items-center h-16">
              <Link
                to="/restaurant/dashboard"
                className="flex items-center space-x-3 hover:opacity-80 transition-all duration-200 hover:scale-105 cursor-pointer"
                title="Voltar ao Dashboard Principal"
              >
                <div className="w-10 h-10 bg-gradient-to-r from-blue-600 to-purple-600 rounded-lg flex items-center justify-center shadow-lg">
                  <Music className="w-6 h-6 text-white" />
                </div>
                <div>
                  <h1 className="text-xl font-semibold text-gray-900 dark:text-white">
                    {restaurantName}
                  </h1>
                  <p className="text-sm text-gray-500 dark:text-gray-400">
                    Dashboard de Gerenciamento
                  </p>
                </div>
              </Link>

              <div className="flex items-center space-x-4">
                <div className="text-sm text-gray-600 dark:text-gray-400">
                  {restaurantName}
                </div>
                <div className="flex items-center space-x-2">
                  <Link
                    to="profile"
                    className="w-8 h-8 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full flex items-center justify-center hover:from-blue-600 hover:to-purple-700 transition-all duration-200 cursor-pointer"
                    title="Perfil do Restaurante"
                  >
                    <User className="w-4 h-4 text-white" />
                  </Link>
                  <button
                    onClick={handleLogout}
                    className="flex items-center space-x-1 px-3 py-2 text-sm text-white bg-red-500 hover:bg-red-600 rounded-lg transition-colors font-medium"
                    title="Sair"
                  >
                    <LogOut className="w-4 h-4" />
                    <span>Sair</span>
                  </button>
                </div>
              </div>
            </div>
          </div>
        </header>

        {/* Navigation */}
        <nav className="bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="flex space-x-8 overflow-x-auto">
              {[
                {
                  name: "Player",
                  icon: PlayCircle,
                  path: "/restaurant/dashboard/player",
                },
                {
                  name: "Playlists",
                  icon: Music,
                  path: "/restaurant/dashboard/playlists",
                },
                {
                  name: "Agendamento",
                  icon: Calendar,
                  path: "/restaurant/dashboard/scheduler",
                },
                {
                  name: "QR Code",
                  icon: QrCode,
                  path: "/restaurant/dashboard/qrcode",
                },
                {
                  name: "Fila",
                  icon: Clock,
                  path: "/restaurant/dashboard/queue",
                },
                {
                  name: "Sugestões",
                  icon: TrendingUp,
                  path: "/restaurant/dashboard/suggestions",
                },
                {
                  name: "Moderação",
                  icon: AlertCircle,
                  path: "/restaurant/dashboard/moderation",
                },
                {
                  name: "Analytics",
                  icon: BarChart3,
                  path: "/restaurant/dashboard/analytics",
                },
                {
                  name: "Configurações",
                  icon: Settings,
                  path: "/restaurant/dashboard/settings",
                },
              ].map((item) => (
                <Link
                  key={item.name}
                  to={item.path}
                  className={`flex items-center space-x-2 py-4 px-1 border-b-2 text-sm font-medium transition-colors whitespace-nowrap ${
                    location.pathname === item.path
                      ? "border-blue-500 text-blue-600 dark:text-blue-400"
                      : "border-transparent text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 hover:border-gray-300"
                  }`}
                >
                  <item.icon className="w-4 h-4 flex-shrink-0" />
                  <span className="truncate">{item.name}</span>
                </Link>
              ))}
            </div>
          </div>
        </nav>

        {/* Main Content */}
        <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <Routes>
            <Route path="/" element={<DashboardHome />} />
            <Route
              path="/player"
              element={
                <div key="player-wrapper">
                  <MusicPlayer />
                </div>
              }
            />
            <Route path="/playlists" element={<PlaylistManager />} />
            <Route path="/scheduler" element={<PlaylistScheduler />} />
            <Route path="/suggestions" element={<SuggestionsManager />} />
            <Route path="/moderation" element={<AdvancedModeration />} />
            <Route path="/qrcode" element={<QRCodeManager />} />
            <Route path="/queue" element={<QueueManager />} />
            <Route path="/analytics" element={<RestaurantAnalytics />} />
            <Route
              path="/advanced-analytics"
              element={<AdvancedPlaylistAnalytics />}
            />
            <Route
              path="/competitive-analytics"
              element={<CompetitiveAnalytics restaurantId={restaurantId} />}
            />
            <Route path="/settings" element={<RestaurantSettings />} />
            <Route path="/profile" element={<RestaurantProfile />} />
            <Route path="*" element={<DashboardHome />} />
          </Routes>
        </main>
      </div>
    </RestaurantContext.Provider>
  );
};

export default RestaurantDashboard;
