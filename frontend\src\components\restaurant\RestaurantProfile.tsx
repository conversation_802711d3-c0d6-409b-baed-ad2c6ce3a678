import React, { useState, useEffect, useCallback } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { useParams } from "react-router-dom";
import {
  User,
  Mail,
  Phone,
  MapPin,
  Building2,
  Clock,
  Save,
  Edit,
  X,
  Check,
  AlertCircle,
  RefreshCw,
  Settings,
  FileImage,
  Radio,
  ExternalLink,
  Users,
} from "lucide-react";
import { toast } from "react-hot-toast";
import { buildApiUrl, API_CONFIG } from "../../config/api";

// Tipos e Interfaces
type TabType = "basic" | "hours" | "settings" | "appearance" | "integrations";

interface BusinessHours {
  open: string;
  close: string;
  isOpen: boolean;
}

interface RestaurantSettings {
  allowSuggestions: boolean;
  moderationRequired: boolean;
  maxSuggestionsPerUser: number;
  autoPlayEnabled: boolean;
  autoSkipDisliked?: boolean;
  darkMode?: boolean;
  primaryColor?: string;
}

interface RestaurantProfile {
  id: string;
  name: string;
  email: string;
  phone: string;
  address: string;
  description: string;
  businessHours: {
    [key: string]: BusinessHours;
  };
  settings: RestaurantSettings;
  logoUrl?: string;
  bannerUrl?: string;
  appearance?: {
    darkMode: boolean;
    primaryColor: string;
    accentColor: string;
    fontFamily: string;
  };
  integrations?: {
    youtubeApiEnabled: boolean;
    spotifyConnected: boolean;
    googleAnalytics: boolean;
  };
}

interface TabProps {
  icon: React.ReactNode;
  label: string;
  id: TabType;
  active: boolean;
  onClick: (id: TabType) => void;
}

// Componente de Tab
const Tab: React.FC<TabProps> = ({ icon, label, id, active, onClick }) => (
  <button
    onClick={() => onClick(id)}
    className={`flex items-center space-x-2 px-4 py-3 rounded-lg transition-colors ${
      active
        ? "bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200 font-medium"
        : "text-gray-600 dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-gray-800"
    }`}
    aria-selected={active}
    role="tab"
    id={`tab-${id}`}
    aria-controls={`panel-${id}`}
  >
    {React.cloneElement(icon as React.ReactElement, {
      className: `w-5 h-5 ${
        active
          ? "text-blue-600 dark:text-blue-400"
          : "text-gray-500 dark:text-gray-400"
      }`,
    })}
    <span>{label}</span>
  </button>
);

// Componente Principal
const RestaurantProfile: React.FC = () => {
  const [profile, setProfile] = useState<RestaurantProfile | null>(null);
  const [loading, setLoading] = useState(true);
  const [editing, setEditing] = useState(false);
  const [saving, setSaving] = useState(false);
  const [editedProfile, setEditedProfile] = useState<RestaurantProfile | null>(
    null
  );
  const [activeTab, setActiveTab] = useState<TabType>("basic");
  const [error, setError] = useState<string | null>(null);

  const { restaurantId } = useParams<{ restaurantId: string }>();
  const finalRestaurantId = restaurantId || "demo-restaurant";

  const daysOfWeek = [
    { key: "monday", label: "Segunda-feira" },
    { key: "tuesday", label: "Terça-feira" },
    { key: "wednesday", label: "Quarta-feira" },
    { key: "thursday", label: "Quinta-feira" },
    { key: "friday", label: "Sexta-feira" },
    { key: "saturday", label: "Sábado" },
    { key: "sunday", label: "Domingo" },
  ];

  const tabs = [
    {
      id: "basic" as TabType,
      label: "Informações Básicas",
      icon: <Building2 />,
    },
    {
      id: "hours" as TabType,
      label: "Horário de Funcionamento",
      icon: <Clock />,
    },
    { id: "settings" as TabType, label: "Configurações", icon: <Settings /> },
    { id: "appearance" as TabType, label: "Aparência", icon: <FileImage /> },
    {
      id: "integrations" as TabType,
      label: "Integrações",
      icon: <ExternalLink />,
    },
  ];

  // Função para carregar o perfil
  const loadProfile = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);
      console.log("🏪 Carregando perfil do restaurante:", restaurantId);

      const url = buildApiUrl(
        `${API_CONFIG.ENDPOINTS.RESTAURANTS}/${restaurantId}/profile`
      );
      console.log("🏪 Fazendo requisição para:", url);

      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 10000);

      const response = await fetch(url, {
        method: "GET",
        headers: {
          "Content-Type": "application/json",
        },
        signal: controller.signal,
      });

      clearTimeout(timeoutId);
      console.log("🏪 Response status:", response.status);

      if (response.ok) {
        const data = await response.json();
        console.log("🏪 Profile loaded:", data);

        if (data.success && data.profile) {
          // Garantir que todas as propriedades existam
          const loadedProfile = {
            ...data.profile,
            appearance: data.profile.appearance || {
              darkMode: false,
              primaryColor: "#3b82f6",
              accentColor: "#10b981",
              fontFamily: "Inter",
            },
            integrations: data.profile.integrations || {
              youtubeApiEnabled: true,
              spotifyConnected: false,
              googleAnalytics: false,
            },
          };

          setProfile(loadedProfile);
          setEditedProfile(loadedProfile);
          toast.success("Perfil carregado com sucesso!");
          return;
        }
      } else {
        console.error(
          "🏪 Response não ok:",
          response.status,
          response.statusText
        );
        const errorText = await response.text().catch(() => "");
        console.error("🏪 Error details:", errorText);
        throw new Error(`API returned status ${response.status}`);
      }
    } catch (error: any) {
      console.error("🏪 Erro ao carregar perfil:", error);
      setError(error.message || "Erro ao carregar dados");

      // Mock data como fallback
      const mockProfile: RestaurantProfile = {
        id: restaurantId,
        name: "Restaurante Demo",
        email: "<EMAIL>",
        phone: "(11) 99999-9999",
        address: "Rua das Flores, 123 - São Paulo, SP",
        description:
          "Um restaurante aconchegante com música ambiente personalizada pelos clientes.",
        businessHours: {
          monday: { open: "11:00", close: "23:00", isOpen: true },
          tuesday: { open: "11:00", close: "23:00", isOpen: true },
          wednesday: { open: "11:00", close: "23:00", isOpen: true },
          thursday: { open: "11:00", close: "23:00", isOpen: true },
          friday: { open: "11:00", close: "24:00", isOpen: true },
          saturday: { open: "11:00", close: "24:00", isOpen: true },
          sunday: { open: "12:00", close: "22:00", isOpen: true },
        },
        settings: {
          allowSuggestions: true,
          moderationRequired: true,
          maxSuggestionsPerUser: 3,
          autoPlayEnabled: true,
          autoSkipDisliked: false,
        },
        logoUrl: "https://placehold.co/400x400?text=Logo",
        bannerUrl: "https://placehold.co/1200x300?text=Banner",
        appearance: {
          darkMode: false,
          primaryColor: "#3b82f6",
          accentColor: "#10b981",
          fontFamily: "Inter",
        },
        integrations: {
          youtubeApiEnabled: true,
          spotifyConnected: false,
          googleAnalytics: false,
        },
      };

      setProfile(mockProfile);
      setEditedProfile(mockProfile);

      if (error.name === "AbortError") {
        toast.error("Timeout - carregando dados de exemplo");
      } else if (error.message?.includes("CONNECTION_REFUSED")) {
        toast.info("Servidor offline - usando dados de exemplo");
      } else {
        toast.info("Carregado em modo demo (dados de exemplo)");
      }
    } finally {
      setLoading(false);
    }
  }, [restaurantId]);

  useEffect(() => {
    loadProfile();
  }, [loadProfile]);

  const handleSave = async () => {
    if (!editedProfile) return;

    try {
      setSaving(true);
      console.log("🏪 Salvando perfil do restaurante:", restaurantId);

      const url = buildApiUrl(
        `${API_CONFIG.ENDPOINTS.RESTAURANTS}/${restaurantId}/profile`
      );
      console.log("🏪 Salvando para:", url);

      const response = await fetch(url, {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(editedProfile),
      });

      if (response.ok) {
        const data = await response.json();
        console.log("🏪 Profile saved:", data);
        setProfile(data.profile || editedProfile);
        setEditing(false);
        toast.success("Perfil atualizado com sucesso!");
      } else {
        console.error(
          "🏪 Erro ao salvar:",
          response.status,
          response.statusText
        );
        const errorText = await response.text().catch(() => "");
        console.error("🏪 Error details:", errorText);
        throw new Error(`API returned status ${response.status}`);
      }
    } catch (error: any) {
      console.error("🏪 Erro ao salvar perfil:", error);

      // Mesmo em caso de erro de conectividade, salvar localmente
      setProfile(editedProfile);
      setEditing(false);

      if (error?.response?.status) {
        toast.error(
          `Erro ${error.response.status}: ${
            error.response.data?.message || "Erro ao salvar perfil"
          }`
        );
      } else {
        toast.warning("Perfil salvo localmente (sem conexão com servidor)");
      }
    } finally {
      setSaving(false);
    }
  };

  const handleCancel = () => {
    setEditedProfile(profile);
    setEditing(false);
  };

  const updateField = (field: string, value: any) => {
    if (!editedProfile) return;
    setEditedProfile({
      ...editedProfile,
      [field]: value,
    });
  };

  const updateBusinessHours = (day: string, field: string, value: any) => {
    if (!editedProfile) return;
    setEditedProfile({
      ...editedProfile,
      businessHours: {
        ...editedProfile.businessHours,
        [day]: {
          ...editedProfile.businessHours[day],
          [field]: value,
        },
      },
    });
  };

  const updateSettings = (field: string, value: any) => {
    if (!editedProfile) return;
    setEditedProfile({
      ...editedProfile,
      settings: {
        ...editedProfile.settings,
        [field]: value,
      },
    });
  };

  const updateAppearance = (field: string, value: any) => {
    if (!editedProfile || !editedProfile.appearance) return;
    setEditedProfile({
      ...editedProfile,
      appearance: {
        ...editedProfile.appearance,
        [field]: value,
      },
    });
  };

  const updateIntegrations = (field: string, value: any) => {
    if (!editedProfile || !editedProfile.integrations) return;
    setEditedProfile({
      ...editedProfile,
      integrations: {
        ...editedProfile.integrations,
        [field]: value,
      },
    });
  };

  // Renderizadores de abas
  const renderBasicInfo = () => (
    <div
      className="space-y-6"
      role="tabpanel"
      id="panel-basic"
      aria-labelledby="tab-basic"
    >
      <div className="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm">
        <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
          Informações Básicas
        </h3>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <label
              htmlFor="restaurant-name"
              className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"
            >
              Nome do Restaurante
            </label>
            {editing ? (
              <input
                id="restaurant-name"
                type="text"
                value={editedProfile?.name || ""}
                onChange={(e) => updateField("name", e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              />
            ) : (
              <div className="flex items-center space-x-2 p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                <Building2
                  className="w-4 h-4 text-gray-500"
                  aria-hidden="true"
                />
                <span>{profile?.name}</span>
              </div>
            )}
          </div>

          <div>
            <label
              htmlFor="email"
              className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"
            >
              Email
            </label>
            {editing ? (
              <input
                id="email"
                type="email"
                value={editedProfile?.email || ""}
                onChange={(e) => updateField("email", e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              />
            ) : (
              <div className="flex items-center space-x-2 p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                <Mail className="w-4 h-4 text-gray-500" aria-hidden="true" />
                <span>{profile?.email}</span>
              </div>
            )}
          </div>

          <div>
            <label
              htmlFor="phone"
              className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"
            >
              Telefone
            </label>
            {editing ? (
              <input
                id="phone"
                type="tel"
                value={editedProfile?.phone || ""}
                onChange={(e) => updateField("phone", e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              />
            ) : (
              <div className="flex items-center space-x-2 p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                <Phone className="w-4 h-4 text-gray-500" aria-hidden="true" />
                <span>{profile?.phone}</span>
              </div>
            )}
          </div>

          <div>
            <label
              htmlFor="address"
              className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"
            >
              Endereço
            </label>
            {editing ? (
              <input
                id="address"
                type="text"
                value={editedProfile?.address || ""}
                onChange={(e) => updateField("address", e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              />
            ) : (
              <div className="flex items-center space-x-2 p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                <MapPin className="w-4 h-4 text-gray-500" aria-hidden="true" />
                <span>{profile?.address}</span>
              </div>
            )}
          </div>
        </div>

        <div className="mt-6">
          <label
            htmlFor="description"
            className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"
          >
            Descrição
          </label>
          {editing ? (
            <textarea
              id="description"
              value={editedProfile?.description || ""}
              onChange={(e) => updateField("description", e.target.value)}
              rows={3}
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            />
          ) : (
            <div className="p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
              <span>{profile?.description}</span>
            </div>
          )}
        </div>
      </div>

      {/* Imagens do Restaurante */}
      <div className="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm">
        <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
          Imagens do Restaurante
        </h3>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Logo
            </label>
            {editing ? (
              <div className="space-y-2">
                <div className="flex items-center justify-center h-40 w-40 mx-auto border-2 border-dashed border-gray-300 dark:border-gray-600 rounded-lg bg-gray-50 dark:bg-gray-700 overflow-hidden">
                  {editedProfile?.logoUrl ? (
                    <img
                      src={editedProfile.logoUrl}
                      alt="Logo do Restaurante"
                      className="max-h-full max-w-full object-contain"
                    />
                  ) : (
                    <div className="text-center p-4">
                      <FileImage
                        className="w-8 h-8 text-gray-400 mx-auto mb-2"
                        aria-hidden="true"
                      />
                      <p className="text-xs text-gray-500">
                        Arraste ou clique para enviar logo
                      </p>
                    </div>
                  )}
                </div>
                <input
                  type="text"
                  placeholder="URL da imagem"
                  value={editedProfile?.logoUrl || ""}
                  onChange={(e) => updateField("logoUrl", e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white text-sm"
                />
              </div>
            ) : (
              <div className="flex items-center justify-center h-40 w-40 mx-auto bg-gray-50 dark:bg-gray-700 rounded-lg overflow-hidden">
                {profile?.logoUrl ? (
                  <img
                    src={profile.logoUrl}
                    alt="Logo do Restaurante"
                    className="max-h-full max-w-full object-contain"
                  />
                ) : (
                  <Building2
                    className="w-12 h-12 text-gray-400"
                    aria-hidden="true"
                  />
                )}
              </div>
            )}
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Banner
            </label>
            {editing ? (
              <div className="space-y-2">
                <div className="flex items-center justify-center h-32 w-full mx-auto border-2 border-dashed border-gray-300 dark:border-gray-600 rounded-lg bg-gray-50 dark:bg-gray-700 overflow-hidden">
                  {editedProfile?.bannerUrl ? (
                    <img
                      src={editedProfile.bannerUrl}
                      alt="Banner do Restaurante"
                      className="max-h-full max-w-full object-cover w-full h-full"
                    />
                  ) : (
                    <div className="text-center p-4">
                      <FileImage
                        className="w-8 h-8 text-gray-400 mx-auto mb-2"
                        aria-hidden="true"
                      />
                      <p className="text-xs text-gray-500">
                        Arraste ou clique para enviar banner
                      </p>
                    </div>
                  )}
                </div>
                <input
                  type="text"
                  placeholder="URL da imagem"
                  value={editedProfile?.bannerUrl || ""}
                  onChange={(e) => updateField("bannerUrl", e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white text-sm"
                />
              </div>
            ) : (
              <div className="flex items-center justify-center h-32 w-full mx-auto bg-gray-50 dark:bg-gray-700 rounded-lg overflow-hidden">
                {profile?.bannerUrl ? (
                  <img
                    src={profile.bannerUrl}
                    alt="Banner do Restaurante"
                    className="max-h-full max-w-full object-cover w-full h-full"
                  />
                ) : (
                  <Building2
                    className="w-12 h-12 text-gray-400"
                    aria-hidden="true"
                  />
                )}
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );

  const renderBusinessHours = () => (
    <div
      className="space-y-6"
      role="tabpanel"
      id="panel-hours"
      aria-labelledby="tab-hours"
    >
      <div className="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm">
        <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4 flex items-center space-x-2">
          <Clock className="w-5 h-5" aria-hidden="true" />
          <span>Horário de Funcionamento</span>
        </h3>

        <div className="space-y-4">
          {daysOfWeek.map((day) => (
            <div
              key={day.key}
              className="flex flex-wrap md:flex-nowrap items-center gap-4 p-3 hover:bg-gray-50 dark:hover:bg-gray-750 rounded-lg transition-colors"
            >
              <div className="w-full md:w-32">
                <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                  {day.label}
                </span>
              </div>

              {editing ? (
                <>
                  <label className="flex items-center space-x-2">
                    <input
                      type="checkbox"
                      checked={
                        editedProfile?.businessHours[day.key]?.isOpen || false
                      }
                      onChange={(e) =>
                        updateBusinessHours(day.key, "isOpen", e.target.checked)
                      }
                      className="rounded text-blue-600 focus:ring-blue-500"
                    />
                    <span className="text-sm">Aberto</span>
                  </label>

                  {editedProfile?.businessHours[day.key]?.isOpen && (
                    <div className="flex flex-wrap md:flex-nowrap items-center gap-2">
                      <label
                        className="text-sm sr-only"
                        htmlFor={`open-${day.key}`}
                      >
                        Horário de abertura
                      </label>
                      <input
                        id={`open-${day.key}`}
                        type="time"
                        value={editedProfile.businessHours[day.key]?.open || ""}
                        onChange={(e) =>
                          updateBusinessHours(day.key, "open", e.target.value)
                        }
                        className="px-2 py-1 border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      />
                      <span className="text-gray-500">às</span>
                      <label
                        className="text-sm sr-only"
                        htmlFor={`close-${day.key}`}
                      >
                        Horário de fechamento
                      </label>
                      <input
                        id={`close-${day.key}`}
                        type="time"
                        value={
                          editedProfile.businessHours[day.key]?.close || ""
                        }
                        onChange={(e) =>
                          updateBusinessHours(day.key, "close", e.target.value)
                        }
                        className="px-2 py-1 border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      />
                    </div>
                  )}
                </>
              ) : (
                <div className="flex items-center space-x-2">
                  {profile?.businessHours[day.key]?.isOpen ? (
                    <>
                      <Check
                        className="w-5 h-5 text-green-600"
                        aria-hidden="true"
                      />
                      <span className="text-sm text-gray-700 dark:text-gray-300">
                        {profile.businessHours[day.key]?.open} às{" "}
                        {profile.businessHours[day.key]?.close}
                      </span>
                    </>
                  ) : (
                    <>
                      <X className="w-5 h-5 text-red-600" aria-hidden="true" />
                      <span className="text-sm text-gray-500">Fechado</span>
                    </>
                  )}
                </div>
              )}
            </div>
          ))}
        </div>
      </div>
    </div>
  );

  const renderSettings = () => (
    <div
      className="space-y-6"
      role="tabpanel"
      id="panel-settings"
      aria-labelledby="tab-settings"
    >
      <div className="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm">
        <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4 flex items-center space-x-2">
          <Settings className="w-5 h-5" aria-hidden="true" />
          <span>Configurações da Playlist</span>
        </h3>

        <div className="space-y-6 divide-y divide-gray-200 dark:divide-gray-700">
          <div className="py-3">
            <div className="flex items-center justify-between">
              <div>
                <label className="text-sm font-medium text-gray-700 dark:text-gray-300">
                  Permitir Sugestões dos Clientes
                </label>
                <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                  Clientes podem sugerir músicas via QR code
                </p>
              </div>
              {editing ? (
                <div className="relative inline-block w-12 mr-2 align-middle select-none">
                  <input
                    type="checkbox"
                    id="allow-suggestions"
                    checked={editedProfile?.settings.allowSuggestions || false}
                    onChange={(e) =>
                      updateSettings("allowSuggestions", e.target.checked)
                    }
                    className="sr-only"
                  />
                  <label
                    htmlFor="allow-suggestions"
                    className={`block overflow-hidden h-6 rounded-full cursor-pointer transition-colors ${
                      editedProfile?.settings.allowSuggestions
                        ? "bg-blue-600"
                        : "bg-gray-300 dark:bg-gray-600"
                    }`}
                  >
                    <span
                      className={`block h-6 w-6 rounded-full bg-white shadow transform transition-transform ${
                        editedProfile?.settings.allowSuggestions
                          ? "translate-x-6"
                          : "translate-x-0"
                      }`}
                    />
                  </label>
                </div>
              ) : (
                <div
                  className={`px-3 py-1 rounded-full text-xs font-medium ${
                    profile?.settings.allowSuggestions
                      ? "bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400"
                      : "bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-400"
                  }`}
                >
                  {profile?.settings.allowSuggestions ? "Ativo" : "Inativo"}
                </div>
              )}
            </div>
          </div>

          <div className="py-3">
            <div className="flex items-center justify-between">
              <div>
                <label className="text-sm font-medium text-gray-700 dark:text-gray-300">
                  Moderação Obrigatória
                </label>
                <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                  Sugestões precisam ser aprovadas antes de tocar
                </p>
              </div>
              {editing ? (
                <div className="relative inline-block w-12 mr-2 align-middle select-none">
                  <input
                    type="checkbox"
                    id="moderation-required"
                    checked={
                      editedProfile?.settings.moderationRequired || false
                    }
                    onChange={(e) =>
                      updateSettings("moderationRequired", e.target.checked)
                    }
                    className="sr-only"
                  />
                  <label
                    htmlFor="moderation-required"
                    className={`block overflow-hidden h-6 rounded-full cursor-pointer transition-colors ${
                      editedProfile?.settings.moderationRequired
                        ? "bg-blue-600"
                        : "bg-gray-300 dark:bg-gray-600"
                    }`}
                  >
                    <span
                      className={`block h-6 w-6 rounded-full bg-white shadow transform transition-transform ${
                        editedProfile?.settings.moderationRequired
                          ? "translate-x-6"
                          : "translate-x-0"
                      }`}
                    />
                  </label>
                </div>
              ) : (
                <div
                  className={`px-3 py-1 rounded-full text-xs font-medium ${
                    profile?.settings.moderationRequired
                      ? "bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-400"
                      : "bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400"
                  }`}
                >
                  {profile?.settings.moderationRequired ? "Ativo" : "Inativo"}
                </div>
              )}
            </div>
          </div>

          <div className="py-3">
            <div className="flex items-center justify-between">
              <div>
                <label className="text-sm font-medium text-gray-700 dark:text-gray-300">
                  Reprodução Automática
                </label>
                <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                  Próxima música inicia automaticamente
                </p>
              </div>
              {editing ? (
                <div className="relative inline-block w-12 mr-2 align-middle select-none">
                  <input
                    type="checkbox"
                    id="auto-play"
                    checked={editedProfile?.settings.autoPlayEnabled || false}
                    onChange={(e) =>
                      updateSettings("autoPlayEnabled", e.target.checked)
                    }
                    className="sr-only"
                  />
                  <label
                    htmlFor="auto-play"
                    className={`block overflow-hidden h-6 rounded-full cursor-pointer transition-colors ${
                      editedProfile?.settings.autoPlayEnabled
                        ? "bg-blue-600"
                        : "bg-gray-300 dark:bg-gray-600"
                    }`}
                  >
                    <span
                      className={`block h-6 w-6 rounded-full bg-white shadow transform transition-transform ${
                        editedProfile?.settings.autoPlayEnabled
                          ? "translate-x-6"
                          : "translate-x-0"
                      }`}
                    />
                  </label>
                </div>
              ) : (
                <div
                  className={`px-3 py-1 rounded-full text-xs font-medium ${
                    profile?.settings.autoPlayEnabled
                      ? "bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400"
                      : "bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-400"
                  }`}
                >
                  {profile?.settings.autoPlayEnabled ? "Ativo" : "Inativo"}
                </div>
              )}
            </div>
          </div>

          <div className="py-3">
            <div className="flex items-center justify-between">
              <div>
                <label className="text-sm font-medium text-gray-700 dark:text-gray-300">
                  Pular Músicas com Muitos Votos Negativos
                </label>
                <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                  Músicas com muitos dislikes serão puladas automaticamente
                </p>
              </div>
              {editing ? (
                <div className="relative inline-block w-12 mr-2 align-middle select-none">
                  <input
                    type="checkbox"
                    id="auto-skip"
                    checked={editedProfile?.settings.autoSkipDisliked || false}
                    onChange={(e) =>
                      updateSettings("autoSkipDisliked", e.target.checked)
                    }
                    className="sr-only"
                  />
                  <label
                    htmlFor="auto-skip"
                    className={`block overflow-hidden h-6 rounded-full cursor-pointer transition-colors ${
                      editedProfile?.settings.autoSkipDisliked
                        ? "bg-blue-600"
                        : "bg-gray-300 dark:bg-gray-600"
                    }`}
                  >
                    <span
                      className={`block h-6 w-6 rounded-full bg-white shadow transform transition-transform ${
                        editedProfile?.settings.autoSkipDisliked
                          ? "translate-x-6"
                          : "translate-x-0"
                      }`}
                    />
                  </label>
                </div>
              ) : (
                <div
                  className={`px-3 py-1 rounded-full text-xs font-medium ${
                    profile?.settings.autoSkipDisliked
                      ? "bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400"
                      : "bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-400"
                  }`}
                >
                  {profile?.settings.autoSkipDisliked ? "Ativo" : "Inativo"}
                </div>
              )}
            </div>
          </div>

          <div className="py-3">
            <div className="flex items-center justify-between">
              <div>
                <label className="text-sm font-medium text-gray-700 dark:text-gray-300">
                  Máximo de Sugestões por Cliente
                </label>
                <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                  Limite de sugestões por cliente por sessão
                </p>
              </div>
              {editing ? (
                <input
                  type="number"
                  min="1"
                  max="10"
                  value={editedProfile?.settings.maxSuggestionsPerUser || 3}
                  onChange={(e) =>
                    updateSettings(
                      "maxSuggestionsPerUser",
                      parseInt(e.target.value)
                    )
                  }
                  className="w-20 px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white text-center focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                />
              ) : (
                <div className="px-3 py-1 bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-400 rounded-full text-xs font-medium">
                  {profile?.settings.maxSuggestionsPerUser}
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );

  const renderAppearance = () => (
    <div
      className="space-y-6"
      role="tabpanel"
      id="panel-appearance"
      aria-labelledby="tab-appearance"
    >
      <div className="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm">
        <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4 flex items-center space-x-2">
          <FileImage className="w-5 h-5" aria-hidden="true" />
          <span>Aparência do Player</span>
        </h3>

        <div className="space-y-6">
          <div className="flex items-center justify-between">
            <div>
              <label className="text-sm font-medium text-gray-700 dark:text-gray-300">
                Modo Escuro
              </label>
              <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                Ativar interface com tema escuro para o player de música
              </p>
            </div>
            {editing ? (
              <div className="relative inline-block w-12 mr-2 align-middle select-none">
                <input
                  type="checkbox"
                  id="dark-mode"
                  checked={editedProfile?.appearance?.darkMode || false}
                  onChange={(e) =>
                    updateAppearance("darkMode", e.target.checked)
                  }
                  className="sr-only"
                />
                <label
                  htmlFor="dark-mode"
                  className={`block overflow-hidden h-6 rounded-full cursor-pointer transition-colors ${
                    editedProfile?.appearance?.darkMode
                      ? "bg-blue-600"
                      : "bg-gray-300 dark:bg-gray-600"
                  }`}
                >
                  <span
                    className={`block h-6 w-6 rounded-full bg-white shadow transform transition-transform ${
                      editedProfile?.appearance?.darkMode
                        ? "translate-x-6"
                        : "translate-x-0"
                    }`}
                  />
                </label>
              </div>
            ) : (
              <div
                className={`px-3 py-1 rounded-full text-xs font-medium ${
                  profile?.appearance?.darkMode
                    ? "bg-indigo-100 text-indigo-800 dark:bg-indigo-900/30 dark:text-indigo-400"
                    : "bg-amber-100 text-amber-800 dark:bg-amber-900/30 dark:text-amber-400"
                }`}
              >
                {profile?.appearance?.darkMode ? "Escuro" : "Claro"}
              </div>
            )}
          </div>

          <div>
            <label
              htmlFor="primary-color"
              className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"
            >
              Cor Primária
            </label>
            <div className="flex items-center space-x-3">
              {editing ? (
                <input
                  id="primary-color"
                  type="color"
                  value={editedProfile?.appearance?.primaryColor || "#3b82f6"}
                  onChange={(e) =>
                    updateAppearance("primaryColor", e.target.value)
                  }
                  className="h-10 w-20 border-0 p-0 rounded"
                />
              ) : (
                <div
                  className="h-6 w-6 rounded-full border border-gray-300 dark:border-gray-600"
                  style={{
                    backgroundColor:
                      profile?.appearance?.primaryColor || "#3b82f6",
                  }}
                ></div>
              )}
              <span className="text-sm text-gray-700 dark:text-gray-300">
                {profile?.appearance?.primaryColor || "#3b82f6"}
              </span>
            </div>
          </div>

          <div>
            <label
              htmlFor="accent-color"
              className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"
            >
              Cor de Destaque
            </label>
            <div className="flex items-center space-x-3">
              {editing ? (
                <input
                  id="accent-color"
                  type="color"
                  value={editedProfile?.appearance?.accentColor || "#10b981"}
                  onChange={(e) =>
                    updateAppearance("accentColor", e.target.value)
                  }
                  className="h-10 w-20 border-0 p-0 rounded"
                />
              ) : (
                <div
                  className="h-6 w-6 rounded-full border border-gray-300 dark:border-gray-600"
                  style={{
                    backgroundColor:
                      profile?.appearance?.accentColor || "#10b981",
                  }}
                ></div>
              )}
              <span className="text-sm text-gray-700 dark:text-gray-300">
                {profile?.appearance?.accentColor || "#10b981"}
              </span>
            </div>
          </div>

          <div>
            <label
              htmlFor="font-family"
              className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"
            >
              Fonte
            </label>
            {editing ? (
              <select
                id="font-family"
                value={editedProfile?.appearance?.fontFamily || "Inter"}
                onChange={(e) => updateAppearance("fontFamily", e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              >
                <option value="Inter">Inter</option>
                <option value="Roboto">Roboto</option>
                <option value="Poppins">Poppins</option>
                <option value="Montserrat">Montserrat</option>
                <option value="Open Sans">Open Sans</option>
              </select>
            ) : (
              <div className="px-3 py-2 bg-gray-50 dark:bg-gray-700 rounded-lg">
                <span
                  style={{
                    fontFamily: profile?.appearance?.fontFamily || "Inter",
                  }}
                >
                  {profile?.appearance?.fontFamily || "Inter"}
                </span>
              </div>
            )}
          </div>

          <div className="p-4 bg-gray-50 dark:bg-gray-750 rounded-lg">
            <h4 className="font-medium text-sm text-gray-700 dark:text-gray-300 mb-3">
              Visualização
            </h4>
            <div
              className="h-36 rounded-lg bg-white dark:bg-gray-850 border border-gray-200 dark:border-gray-700 p-3 flex flex-col"
              style={{
                backgroundColor: editedProfile?.appearance?.darkMode
                  ? "#1f2937"
                  : "#ffffff",
                color: editedProfile?.appearance?.darkMode
                  ? "#f3f4f6"
                  : "#111827",
                fontFamily: editedProfile?.appearance?.fontFamily || "Inter",
              }}
            >
              <div className="text-center mb-2">
                <div
                  className="font-bold"
                  style={{ color: editedProfile?.appearance?.primaryColor }}
                >
                  Agora Tocando
                </div>
                <div className="text-sm">Nome da Música - Artista</div>
              </div>
              <div className="flex-1 flex items-center justify-center">
                <div className="h-2 w-full rounded-full bg-gray-200 dark:bg-gray-600">
                  <div
                    className="h-2 rounded-full"
                    style={{
                      width: "40%",
                      backgroundColor:
                        editedProfile?.appearance?.accentColor || "#10b981",
                    }}
                  ></div>
                </div>
              </div>
              <div className="flex justify-between text-xs mt-2">
                <span>1:30</span>
                <span>3:45</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );

  const renderIntegrations = () => (
    <div
      className="space-y-6"
      role="tabpanel"
      id="panel-integrations"
      aria-labelledby="tab-integrations"
    >
      <div className="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm">
        <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4 flex items-center space-x-2">
          <ExternalLink className="w-5 h-5" aria-hidden="true" />
          <span>Integrações</span>
        </h3>

        <div className="space-y-6 divide-y divide-gray-200 dark:divide-gray-700">
          <div className="py-3">
            <div className="flex items-center justify-between">
              <div>
                <label className="text-sm font-medium text-gray-700 dark:text-gray-300">
                  API do YouTube
                </label>
                <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                  Usar a API oficial do YouTube para buscar músicas
                </p>
              </div>
              {editing ? (
                <div className="relative inline-block w-12 mr-2 align-middle select-none">
                  <input
                    type="checkbox"
                    id="youtube-api"
                    checked={
                      editedProfile?.integrations?.youtubeApiEnabled || false
                    }
                    onChange={(e) =>
                      updateIntegrations("youtubeApiEnabled", e.target.checked)
                    }
                    className="sr-only"
                  />
                  <label
                    htmlFor="youtube-api"
                    className={`block overflow-hidden h-6 rounded-full cursor-pointer transition-colors ${
                      editedProfile?.integrations?.youtubeApiEnabled
                        ? "bg-blue-600"
                        : "bg-gray-300 dark:bg-gray-600"
                    }`}
                  >
                    <span
                      className={`block h-6 w-6 rounded-full bg-white shadow transform transition-transform ${
                        editedProfile?.integrations?.youtubeApiEnabled
                          ? "translate-x-6"
                          : "translate-x-0"
                      }`}
                    />
                  </label>
                </div>
              ) : (
                <div
                  className={`px-3 py-1 rounded-full text-xs font-medium ${
                    profile?.integrations?.youtubeApiEnabled
                      ? "bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400"
                      : "bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-400"
                  }`}
                >
                  {profile?.integrations?.youtubeApiEnabled
                    ? "Conectado"
                    : "Desconectado"}
                </div>
              )}
            </div>
          </div>

          <div className="py-3">
            <div className="flex items-center justify-between">
              <div>
                <label className="text-sm font-medium text-gray-700 dark:text-gray-300">
                  Spotify
                </label>
                <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                  Integrar com sua conta Spotify para playlists
                </p>
              </div>
              {editing ? (
                <div className="relative inline-block w-12 mr-2 align-middle select-none">
                  <input
                    type="checkbox"
                    id="spotify-connected"
                    checked={
                      editedProfile?.integrations?.spotifyConnected || false
                    }
                    onChange={(e) =>
                      updateIntegrations("spotifyConnected", e.target.checked)
                    }
                    className="sr-only"
                  />
                  <label
                    htmlFor="spotify-connected"
                    className={`block overflow-hidden h-6 rounded-full cursor-pointer transition-colors ${
                      editedProfile?.integrations?.spotifyConnected
                        ? "bg-blue-600"
                        : "bg-gray-300 dark:bg-gray-600"
                    }`}
                  >
                    <span
                      className={`block h-6 w-6 rounded-full bg-white shadow transform transition-transform ${
                        editedProfile?.integrations?.spotifyConnected
                          ? "translate-x-6"
                          : "translate-x-0"
                      }`}
                    />
                  </label>
                </div>
              ) : (
                <div
                  className={`px-3 py-1 rounded-full text-xs font-medium ${
                    profile?.integrations?.spotifyConnected
                      ? "bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400"
                      : "bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-400"
                  }`}
                >
                  {profile?.integrations?.spotifyConnected
                    ? "Conectado"
                    : "Desconectado"}
                </div>
              )}
            </div>
          </div>

          <div className="py-3">
            <div className="flex items-center justify-between">
              <div>
                <label className="text-sm font-medium text-gray-700 dark:text-gray-300">
                  Google Analytics
                </label>
                <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                  Monitorar estatísticas de uso do player
                </p>
              </div>
              {editing ? (
                <div className="relative inline-block w-12 mr-2 align-middle select-none">
                  <input
                    type="checkbox"
                    id="google-analytics"
                    checked={
                      editedProfile?.integrations?.googleAnalytics || false
                    }
                    onChange={(e) =>
                      updateIntegrations("googleAnalytics", e.target.checked)
                    }
                    className="sr-only"
                  />
                  <label
                    htmlFor="google-analytics"
                    className={`block overflow-hidden h-6 rounded-full cursor-pointer transition-colors ${
                      editedProfile?.integrations?.googleAnalytics
                        ? "bg-blue-600"
                        : "bg-gray-300 dark:bg-gray-600"
                    }`}
                  >
                    <span
                      className={`block h-6 w-6 rounded-full bg-white shadow transform transition-transform ${
                        editedProfile?.integrations?.googleAnalytics
                          ? "translate-x-6"
                          : "translate-x-0"
                      }`}
                    />
                  </label>
                </div>
              ) : (
                <div
                  className={`px-3 py-1 rounded-full text-xs font-medium ${
                    profile?.integrations?.googleAnalytics
                      ? "bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400"
                      : "bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-400"
                  }`}
                >
                  {profile?.integrations?.googleAnalytics ? "Ativo" : "Inativo"}
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <RefreshCw
          className="w-8 h-8 animate-spin text-blue-600"
          aria-hidden="true"
        />
        <span className="sr-only">Carregando perfil do restaurante</span>
      </div>
    );
  }

  if (!profile) {
    return (
      <div className="text-center py-12 bg-red-50 dark:bg-red-900/20 rounded-lg">
        <AlertCircle
          className="w-12 h-12 text-red-400 mx-auto mb-4"
          aria-hidden="true"
        />
        <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
          Erro ao carregar perfil
        </h3>
        <p className="text-red-500 mb-4">
          {error || "Não foi possível carregar os dados do perfil"}
        </p>
        <button
          onClick={loadProfile}
          className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 dark:focus:ring-offset-gray-800"
        >
          Tentar novamente
        </button>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div>
          <h2 className="text-2xl font-bold text-gray-900 dark:text-white">
            Perfil do Restaurante
          </h2>
          <p className="text-gray-600 dark:text-gray-400 mt-1">
            Gerencie as informações e configurações do seu restaurante
          </p>
        </div>

        <div className="flex space-x-2">
          {editing ? (
            <>
              <button
                onClick={handleCancel}
                disabled={saving}
                className="flex items-center space-x-2 px-4 py-2 bg-gray-200 text-gray-700 rounded-lg hover:bg-gray-300 disabled:opacity-50 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 dark:focus:ring-offset-gray-800"
                aria-label="Cancelar edição"
              >
                <X className="w-4 h-4" aria-hidden="true" />
                <span>Cancelar</span>
              </button>
              <button
                onClick={handleSave}
                disabled={saving}
                className="flex items-center space-x-2 px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 disabled:opacity-50 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2 dark:focus:ring-offset-gray-800"
                aria-label="Salvar alterações"
              >
                {saving ? (
                  <RefreshCw
                    className="w-4 h-4 animate-spin"
                    aria-hidden="true"
                  />
                ) : (
                  <Save className="w-4 h-4" aria-hidden="true" />
                )}
                <span>Salvar</span>
              </button>
            </>
          ) : (
            <button
              onClick={() => setEditing(true)}
              className="flex items-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 dark:focus:ring-offset-gray-800"
              aria-label="Editar perfil"
            >
              <Edit className="w-4 h-4" aria-hidden="true" />
              <span>Editar</span>
            </button>
          )}
        </div>
      </div>

      {/* Tabs */}
      <div className="flex flex-col space-y-6">
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm overflow-x-auto">
          <div className="flex p-2 gap-1 min-w-max" role="tablist">
            {tabs.map((tab) => (
              <Tab
                key={tab.id}
                id={tab.id}
                label={tab.label}
                icon={tab.icon}
                active={activeTab === tab.id}
                onClick={setActiveTab}
              />
            ))}
          </div>
        </div>

        {/* Tab Content */}
        <AnimatePresence mode="wait">
          <motion.div
            key={activeTab}
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -10 }}
            transition={{ duration: 0.2 }}
          >
            {activeTab === "basic" && renderBasicInfo()}
            {activeTab === "hours" && renderBusinessHours()}
            {activeTab === "settings" && renderSettings()}
            {activeTab === "appearance" && renderAppearance()}
            {activeTab === "integrations" && renderIntegrations()}
          </motion.div>
        </AnimatePresence>
      </div>
    </div>
  );
};

export default RestaurantProfile;
