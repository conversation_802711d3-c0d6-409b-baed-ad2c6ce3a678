import React, { useState, useEffect, useCallback, memo } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { useParams } from "react-router-dom";
import {
  BarChart3,
  TrendingUp,
  TrendingDown,
  Users,
  Music,
  Heart,
  Clock,
  Download,
  RefreshCw,
  Award,
  Activity,
  CheckCircle,
  XCircle,
  Eye,
  Trash2,
} from "lucide-react";
import { toast } from "react-hot-toast";
import { format } from "date-fns";
import { ptBR } from "date-fns/locale";
import apiService from "@/services/api";

// Enums para Advanced Analytics
enum Performance {
  Excellent = "excellent",
  Good = "good",
  Average = "average",
  Poor = "poor",
  Terrible = "terrible",
}

enum Recommendation {
  Keep = "keep",
  Monitor = "monitor",
  Remove = "remove",
  Blacklist = "blacklist",
}

enum HealthRating {
  Excellent = "excellent",
  Good = "good",
  NeedsAttention = "needs_attention",
  Critical = "critical",
}

// Período para Dashboard Geral
enum Period {
  Today = "1d",
  Week = "7d",
  Month = "30d",
  Quarter = "90d",
}

// Interfaces para Dashboard Geral
interface AnalyticsSummary {
  totalPlays: number;
  totalSuggestions: number;
  totalVotes: number;
  activeUsers: number;
  averageRating: number;
  growthRate: number;
  peakHour: string;
  topGenre: string;
}

interface AnalyticsMetrics {
  totalSuggestions: number;
  approvedSuggestions: number;
  rejectedSuggestions: number;
  totalVotes: number;
  upvotes: number;
  downvotes: number;
  uniqueSessions: number;
  averageSessionDuration: number;
  engagementRate: number;
  approvalRate: number;
  topGenres: Array<{ genre: string; count: number; percentage: number }>;
  topArtists: Array<{ artist: string; count: number; percentage: number }>;
  hourlyActivity: Array<{
    hour: number;
    suggestions: number;
    votes: number;
    sessions: number;
  }>;
  dailyActivity: Array<{
    date: string;
    suggestions: number;
    votes: number;
    sessions: number;
  }>;
}

interface PopularSong {
  id: string;
  title: string;
  artist: string;
  votes: number;
  plays: number;
  score: number;
}

// Interfaces para Advanced Analytics
interface TrackAnalytics {
  id: string;
  title: string;
  artist: string;
  youtubeVideoId: string;
  totalVotes: number;
  upvotes: number;
  downvotes: number;
  score: number;
  negativeVoteRatio: number;
  positiveVoteRatio: number;
  playCount: number;
  skipCount: number;
  completionRate: number;
  averagePlayDuration: number;
  lastPlayed?: string;
  suggestedCount: number;
  performance: Performance;
  recommendation: Recommendation;
}

interface PlaylistHealth {
  totalTracks: number;
  excellentTracks: number;
  goodTracks: number;
  averageTracks: number;
  poorTracks: number;
  terribleTracks: number;
  overallScore: number;
  healthRating: HealthRating;
  recommendations: string[];
}

interface UnifiedAnalyticsProps {
  restaurantId?: string;
}

const UnifiedAnalytics: React.FC<UnifiedAnalyticsProps> = memo(
  ({ restaurantId: propRestaurantId }) => {
    const { restaurantId: urlRestaurantId } = useParams<{
      restaurantId: string;
    }>();
    const finalRestaurantId =
      propRestaurantId || urlRestaurantId || "demo-restaurant";

    // Estado para abas
    const [activeTab, setActiveTab] = useState<"dashboard" | "advanced">(
      "dashboard"
    );

    // Estados para Dashboard Geral
    const [summary, setSummary] = useState<AnalyticsSummary | null>(null);
    const [metrics, setMetrics] = useState<AnalyticsMetrics | null>(null);
    const [popularSongs, setPopularSongs] = useState<PopularSong[]>([]);
    const [selectedPeriod, setSelectedPeriod] = useState<Period>(Period.Week);
    const [dashboardTab, setDashboardTab] = useState<
      "overview" | "engagement" | "music" | "users"
    >("overview");

    // Estados para Advanced Analytics
    const [trackAnalytics, setTrackAnalytics] = useState<TrackAnalytics[]>([]);
    const [playlistHealth, setPlaylistHealth] = useState<PlaylistHealth | null>(
      null
    );
    const [filter, setFilter] = useState<Performance | "all">("all");

    // Estados gerais
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState<string | null>(null);

    const periods = [
      { value: Period.Today, label: "Hoje" },
      { value: Period.Week, label: "7 dias" },
      { value: Period.Month, label: "30 dias" },
      { value: Period.Quarter, label: "90 dias" },
    ];

    const mainTabs = [
      { id: "dashboard", label: "Dashboard Geral", icon: BarChart3 },
      { id: "advanced", label: "Analytics Avançado", icon: Activity },
    ] as const;

    const dashboardTabs = [
      { id: "overview", label: "Visão Geral", icon: BarChart3 },
      { id: "engagement", label: "Engajamento", icon: Heart },
      { id: "music", label: "Músicas", icon: Music },
      { id: "users", label: "Usuários", icon: Users },
    ] as const;

    // Função para carregar dados do Dashboard Geral
    const loadDashboardAnalytics = useCallback(async () => {
      try {
        setLoading(true);
        const [summaryRes, metricsRes, songsRes] = await Promise.all([
          apiService.client.get(
            `/analytics/dashboard/${finalRestaurantId}?period=${selectedPeriod}`
          ),
          apiService.client.get(
            `/analytics/metrics/${finalRestaurantId}?period=${selectedPeriod}`
          ),
          apiService.client.get(`/analytics/stats/${finalRestaurantId}`),
        ]);

        setSummary(summaryRes.data.summary || null);
        setMetrics(metricsRes.data.metrics || null);
        setPopularSongs(songsRes.data.stats?.topSongs || []);
      } catch (error) {
        console.error("Erro ao carregar analytics dashboard:", error);
        toast.error("Erro ao carregar dados do dashboard");
        setError("Erro ao carregar dados do dashboard");
      } finally {
        setLoading(false);
      }
    }, [finalRestaurantId, selectedPeriod]);

    // Função para carregar dados do Advanced Analytics
    const loadAdvancedAnalytics = useCallback(async () => {
      try {
        setLoading(true);
        setError(null);

        const [tracksResponse, healthResponse] = await Promise.all([
          fetch(`/api/v1/analytics/tracks/${finalRestaurantId}`),
          fetch(`/api/v1/analytics/playlist-health/${finalRestaurantId}`),
        ]);

        if (!tracksResponse.ok || !healthResponse.ok) {
          throw new Error("Erro ao carregar dados do servidor");
        }

        const tracksData = await tracksResponse.json();
        const healthData = await healthResponse.json();

        if (tracksData.success && tracksData.trackAnalytics) {
          setTrackAnalytics(
            Object.values(tracksData.trackAnalytics) as TrackAnalytics[]
          );
        } else {
          setTrackAnalytics([]);
        }

        if (healthData.success && healthData.playlistHealth) {
          setPlaylistHealth(healthData.playlistHealth);
        } else {
          setPlaylistHealth(null);
        }
      } catch (error) {
        console.error("Erro ao carregar advanced analytics:", error);
        setError("Erro ao carregar dados de analytics avançado");
        toast.error("Erro ao carregar analytics avançado");
      } finally {
        setLoading(false);
      }
    }, [finalRestaurantId]);

    // Carregar dados baseado na aba ativa
    useEffect(() => {
      if (activeTab === "dashboard") {
        loadDashboardAnalytics();
      } else {
        loadAdvancedAnalytics();
      }
    }, [activeTab, loadDashboardAnalytics, loadAdvancedAnalytics]);

    // Funções auxiliares
    const formatDuration = (seconds: number): string => {
      const mins = Math.floor(seconds / 60);
      const secs = seconds % 60;
      return `${mins}:${secs.toString().padStart(2, "0")}`;
    };

    const getPerformanceColor = (performance: Performance): string => {
      switch (performance) {
        case Performance.Excellent:
          return "text-green-600 bg-green-100";
        case Performance.Good:
          return "text-blue-600 bg-blue-100";
        case Performance.Average:
          return "text-yellow-600 bg-yellow-100";
        case Performance.Poor:
          return "text-orange-600 bg-orange-100";
        case Performance.Terrible:
          return "text-red-600 bg-red-100";
        default:
          return "text-gray-600 bg-gray-100";
      }
    };

    const getRecommendationColor = (recommendation: Recommendation): string => {
      switch (recommendation) {
        case Recommendation.Keep:
          return "text-green-600 bg-green-100";
        case Recommendation.Monitor:
          return "text-yellow-600 bg-yellow-100";
        case Recommendation.Remove:
          return "text-orange-600 bg-orange-100";
        case Recommendation.Blacklist:
          return "text-red-600 bg-red-100";
        default:
          return "text-gray-600 bg-gray-100";
      }
    };

    const filteredTracks = trackAnalytics.filter(
      (track) => filter === "all" || track.performance === filter
    );

    if (loading) {
      return (
        <div className="flex items-center justify-center min-h-screen">
          <div className="flex items-center space-x-2">
            <RefreshCw className="w-5 h-5 animate-spin" />
            <span>Carregando analytics...</span>
          </div>
        </div>
      );
    }

    return (
      <div className="max-w-7xl mx-auto p-6 space-y-6">
        {/* Header */}
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
              Analytics Completo
            </h1>
            <p className="text-gray-600 dark:text-gray-300 mt-1">
              Análise detalhada do desempenho do restaurante
            </p>
          </div>

          <button
            onClick={() => {
              if (activeTab === "dashboard") {
                loadDashboardAnalytics();
              } else {
                loadAdvancedAnalytics();
              }
            }}
            className="flex items-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
          >
            <RefreshCw className="w-4 h-4" />
            <span>Atualizar</span>
          </button>
        </div>

        {/* Main Tabs */}
        <div className="border-b border-gray-200 dark:border-gray-700">
          <nav className="-mb-px flex space-x-8">
            {mainTabs.map((tab) => (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={`py-4 px-1 border-b-2 font-medium text-sm flex items-center space-x-2 ${
                  activeTab === tab.id
                    ? "border-blue-500 text-blue-600 dark:text-blue-400"
                    : "border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300"
                }`}
              >
                <tab.icon className="w-4 h-4" />
                <span>{tab.label}</span>
              </button>
            ))}
          </nav>
        </div>

        {/* Content */}
        <AnimatePresence mode="wait">
          {activeTab === "dashboard" && (
            <motion.div
              key="dashboard"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              transition={{ duration: 0.2 }}
              className="space-y-6"
            >
              {/* Dashboard Content */}
              <div className="flex justify-between items-center">
                <div className="flex space-x-4">
                  {dashboardTabs.map((tab) => (
                    <button
                      key={tab.id}
                      onClick={() => setDashboardTab(tab.id)}
                      className={`flex items-center space-x-2 px-4 py-2 rounded-lg transition-colors ${
                        dashboardTab === tab.id
                          ? "bg-blue-100 text-blue-700 dark:bg-blue-900/30 dark:text-blue-400"
                          : "text-gray-600 hover:bg-gray-100 dark:text-gray-400 dark:hover:bg-gray-800"
                      }`}
                    >
                      <tab.icon className="w-4 h-4" />
                      <span className="text-sm">{tab.label}</span>
                    </button>
                  ))}
                </div>

                <select
                  value={selectedPeriod}
                  onChange={(e) => setSelectedPeriod(e.target.value as Period)}
                  className="px-3 py-2 border border-gray-300 rounded-lg text-sm bg-white dark:bg-gray-800 dark:border-gray-600"
                >
                  {periods.map((period) => (
                    <option key={period.value} value={period.value}>
                      {period.label}
                    </option>
                  ))}
                </select>
              </div>

              {/* Dashboard Summary Cards */}
              {summary && (
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                  {[
                    {
                      title: "Total de Plays",
                      value: summary.totalPlays,
                      icon: Music,
                      color: "blue",
                    },
                    {
                      title: "Sugestões",
                      value: summary.totalSuggestions,
                      icon: Heart,
                      color: "green",
                    },
                    {
                      title: "Usuários Ativos",
                      value: summary.activeUsers,
                      icon: Users,
                      color: "purple",
                    },
                    {
                      title: "Nota Média",
                      value: summary.averageRating.toFixed(1),
                      icon: Award,
                      color: "yellow",
                    },
                  ].map((stat, index) => (
                    <motion.div
                      key={stat.title}
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ delay: index * 0.1 }}
                      className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700"
                    >
                      <div className="flex items-center justify-between">
                        <div>
                          <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                            {stat.title}
                          </p>
                          <p className="text-2xl font-bold text-gray-900 dark:text-white">
                            {stat.value}
                          </p>
                        </div>
                        <div
                          className={`p-3 rounded-full bg-${stat.color}-100 dark:bg-${stat.color}-900/30`}
                        >
                          <stat.icon
                            className={`w-6 h-6 text-${stat.color}-600 dark:text-${stat.color}-400`}
                          />
                        </div>
                      </div>
                    </motion.div>
                  ))}
                </div>
              )}

              {/* Dashboard Content Sections */}
              {dashboardTab === "overview" && metrics && (
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                  <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700">
                    <h3 className="text-lg font-semibold mb-4">Top Gêneros</h3>
                    <div className="space-y-3">
                      {metrics.topGenres.slice(0, 5).map((genre, index) => (
                        <div
                          key={genre.genre}
                          className="flex items-center justify-between"
                        >
                          <span className="text-sm text-gray-600 dark:text-gray-400">
                            {genre.genre}
                          </span>
                          <div className="flex items-center space-x-2">
                            <div className="w-20 bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                              <div
                                className="bg-blue-600 h-2 rounded-full"
                                style={{ width: `${genre.percentage}%` }}
                              />
                            </div>
                            <span className="text-sm font-medium">
                              {genre.percentage}%
                            </span>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>

                  <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700">
                    <h3 className="text-lg font-semibold mb-4">Top Artistas</h3>
                    <div className="space-y-3">
                      {metrics.topArtists.slice(0, 5).map((artist, index) => (
                        <div
                          key={artist.artist}
                          className="flex items-center justify-between"
                        >
                          <span className="text-sm text-gray-600 dark:text-gray-400">
                            {artist.artist}
                          </span>
                          <div className="flex items-center space-x-2">
                            <div className="w-20 bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                              <div
                                className="bg-green-600 h-2 rounded-full"
                                style={{ width: `${artist.percentage}%` }}
                              />
                            </div>
                            <span className="text-sm font-medium">
                              {artist.percentage}%
                            </span>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              )}

              {dashboardTab === "music" && popularSongs.length > 0 && (
                <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700">
                  <h3 className="text-lg font-semibold mb-4">
                    Músicas Populares
                  </h3>
                  <div className="space-y-4">
                    {popularSongs.slice(0, 10).map((song, index) => (
                      <div
                        key={song.id}
                        className="flex items-center space-x-4 p-3 bg-gray-50 dark:bg-gray-700 rounded-lg"
                      >
                        <div className="flex-shrink-0 w-8 h-8 bg-blue-100 dark:bg-blue-900/30 rounded-full flex items-center justify-center">
                          <span className="text-sm font-medium text-blue-600 dark:text-blue-400">
                            {index + 1}
                          </span>
                        </div>
                        <div className="flex-1 min-w-0">
                          <p className="text-sm font-medium text-gray-900 dark:text-white truncate">
                            {song.title}
                          </p>
                          <p className="text-sm text-gray-500 dark:text-gray-400 truncate">
                            {song.artist}
                          </p>
                        </div>
                        <div className="flex items-center space-x-4 text-sm text-gray-500 dark:text-gray-400">
                          <span>{song.plays} plays</span>
                          <span>{song.votes} votos</span>
                          <span className="font-medium">{song.score}</span>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </motion.div>
          )}

          {activeTab === "advanced" && (
            <motion.div
              key="advanced"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              transition={{ duration: 0.2 }}
              className="space-y-6"
            >
              {/* Advanced Analytics Content */}

              {/* Playlist Health */}
              {playlistHealth && (
                <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700">
                  <h3 className="text-lg font-semibold mb-4">
                    Saúde da Playlist
                  </h3>
                  <div className="grid grid-cols-2 md:grid-cols-6 gap-4 mb-4">
                    {[
                      {
                        label: "Excelentes",
                        value: playlistHealth.excellentTracks,
                        color: "green",
                      },
                      {
                        label: "Boas",
                        value: playlistHealth.goodTracks,
                        color: "blue",
                      },
                      {
                        label: "Médias",
                        value: playlistHealth.averageTracks,
                        color: "yellow",
                      },
                      {
                        label: "Ruins",
                        value: playlistHealth.poorTracks,
                        color: "orange",
                      },
                      {
                        label: "Péssimas",
                        value: playlistHealth.terribleTracks,
                        color: "red",
                      },
                      {
                        label: "Total",
                        value: playlistHealth.totalTracks,
                        color: "gray",
                      },
                    ].map((item) => (
                      <div key={item.label} className="text-center">
                        <div
                          className={`text-2xl font-bold text-${item.color}-600`}
                        >
                          {item.value}
                        </div>
                        <div className="text-sm text-gray-500">
                          {item.label}
                        </div>
                      </div>
                    ))}
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium">Score Geral:</span>
                    <span className="text-lg font-bold">
                      {playlistHealth.overallScore}/100
                    </span>
                  </div>
                </div>
              )}

              {/* Filter */}
              <div className="flex justify-between items-center">
                <select
                  value={filter}
                  onChange={(e) =>
                    setFilter(e.target.value as Performance | "all")
                  }
                  className="px-3 py-2 border border-gray-300 rounded-lg text-sm bg-white dark:bg-gray-800 dark:border-gray-600"
                >
                  <option value="all">Todas as performances</option>
                  <option value={Performance.Excellent}>Excelente</option>
                  <option value={Performance.Good}>Boa</option>
                  <option value={Performance.Average}>Média</option>
                  <option value={Performance.Poor}>Ruim</option>
                  <option value={Performance.Terrible}>Péssima</option>
                </select>

                <span className="text-sm text-gray-500">
                  {filteredTracks.length} músicas encontradas
                </span>
              </div>

              {/* Tracks Table */}
              <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 overflow-hidden">
                <div className="overflow-x-auto">
                  <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                    <thead className="bg-gray-50 dark:bg-gray-900">
                      <tr>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Música
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Performance
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Votos
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Plays
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Taxa Conclusão
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Recomendação
                        </th>
                      </tr>
                    </thead>
                    <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                      {filteredTracks.map((track) => (
                        <tr
                          key={track.id}
                          className="hover:bg-gray-50 dark:hover:bg-gray-700"
                        >
                          <td className="px-6 py-4 whitespace-nowrap">
                            <div>
                              <div className="text-sm font-medium text-gray-900 dark:text-white">
                                {track.title}
                              </div>
                              <div className="text-sm text-gray-500">
                                {track.artist}
                              </div>
                            </div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <span
                              className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getPerformanceColor(
                                track.performance
                              )}`}
                            >
                              {track.performance}
                            </span>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                            <div className="flex items-center space-x-2">
                              <span className="text-green-600">
                                ↑{track.upvotes}
                              </span>
                              <span className="text-red-600">
                                ↓{track.downvotes}
                              </span>
                            </div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                            {track.playCount}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                            {(track.completionRate * 100).toFixed(1)}%
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <span
                              className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getRecommendationColor(
                                track.recommendation
                              )}`}
                            >
                              {track.recommendation}
                            </span>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>

                {filteredTracks.length === 0 && (
                  <div className="text-center py-8 text-gray-500">
                    Nenhuma música encontrada para este filtro
                  </div>
                )}
              </div>
            </motion.div>
          )}
        </AnimatePresence>

        {error && (
          <div className="bg-red-50 border border-red-200 rounded-lg p-4">
            <div className="flex items-center">
              <XCircle className="w-5 h-5 text-red-600 mr-2" />
              <span className="text-red-700">{error}</span>
            </div>
          </div>
        )}
      </div>
    );
  }
);

UnifiedAnalytics.displayName = "UnifiedAnalytics";

export default UnifiedAnalytics;
