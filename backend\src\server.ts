import "reflect-metadata";
import express from "express";
import cors from "cors";
import helmet from "helmet";
import morgan from "morgan";
import compression from "compression";
import rateLimit from "express-rate-limit";
import { createServer } from "http";
import { Server as SocketIOServer } from "socket.io";
import { config } from "dotenv";
import { logger } from "./utils/logger";
import { AppDataSource, initializeDatabase } from "./config/database";
import { redisClient } from "./config/redis";
import QRCode from "qrcode";
import { v4 as uuidv4 } from "uuid";

// Carregar variáveis de ambiente
config();

// Importar rotas modulares
import genresRoutes from "./routes/genres";
import restaurantsRoutes from "./routes/restaurants";
import suggestionsRoutes from "./routes/suggestions";
import playlistsRoutes from "./routes/playlists";
import analyticsRoutes from "./routes/analytics";
import authRoutes from "./routes/auth";
import clientRoutes from "./routes/client";
import notificationsRoutes from "./routes/notifications";
import paymentsRoutes from "./routes/payments";
import playbackRoutes from "./routes/playback";
import playbackQueueRoutes from "./routes/playbackQueue";
import rewardsRoutes from "./routes/rewards";
import youtubeRoutes from "./routes/youtube";
import testYoutubeRoutes from "./routes/test-youtube";
// import qrcodeRoutes from "./routes/qrcode"; // Removido - usando implementação inline
import businessHoursRoutes from "./routes/businessHours";
import competitiveVotingRoutes from "./routes/competitiveVoting";
import lyricsRoutes from "./routes/lyrics";
import schedulesRoutes from "./routes/schedules";
import playlistSchedulesRoutes from "./routes/playlistSchedules";

// Importar modelos
import { Restaurant } from "./models/Restaurant";
import { User } from "./models/User";
import { Suggestion } from "./models/Suggestion";
import { Vote } from "./models/Vote";
import { Playlist } from "./models/Playlist";
import { ClientSession } from "./models/ClientSession";
import { PlayHistory } from "./models/PlayHistory";
import { AnalyticsDaily } from "./models/AnalyticsDaily";
import { CompetitiveVote } from "./models/CompetitiveVote";
import { Payment } from "./models/Payment";
import { QRCode as QRCodeModel, QRCodeType } from "./models/QRCode";

// Importar serviços
import { YouTubeService } from "./services/YouTubeService";
import NotificationService from "./services/NotificationService";
import { PaymentService } from "./services/PaymentService";
import { PlaybackService } from "./services/PlaybackService";
import RewardService from "./services/RewardService";
import { WebSocketService } from "./services/WebSocketService";

// Configurar Express
const app = express();
const server = createServer(app);

// Configurar CORS
const corsOptions = {
  origin: [
    "http://localhost:3000",
    "http://localhost:8000",
    "http://localhost:5173",
    "https://restaurant-playlist.vercel.app",
    "https://restaurant-playlist-frontend.vercel.app",
  ],
  credentials: true,
  methods: ["GET", "POST", "PUT", "DELETE", "PATCH", "OPTIONS"],
  allowedHeaders: [
    "Content-Type",
    "Authorization",
    "X-Requested-With",
    "Accept",
    "Origin",
    "X-Tenant-ID",
    "X-Session-Token",
    "X-Session-ID",
    "X-Device-Info",
    "Cache-Control",
    "Pragma",
  ],
};

app.use(cors(corsOptions));

// Middleware de segurança
app.use(
  helmet({
    crossOriginEmbedderPolicy: false,
    contentSecurityPolicy: false,
  })
);

// Compression
app.use(compression());

// Logging
if (process.env.NODE_ENV === "development") {
  app.use(morgan("dev"));
}

// Rate limiting
const limiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutos
  max: 1000, // máximo 1000 requests por IP por janela
  message: {
    error: "Muitas requisições deste IP, tente novamente em 15 minutos.",
  },
  standardHeaders: true,
  legacyHeaders: false,
});
app.use(limiter);

// Middleware básico
app.use(express.json({ limit: "10mb" }));
app.use(express.urlencoded({ extended: true, limit: "10mb" }));

// Middleware de tenant
const tenantMiddleware = (
  req: express.Request,
  res: express.Response,
  next: express.NextFunction
) => {
  const tenantId = req.headers["x-tenant-id"] || req.query.restaurantId;
  if (tenantId) {
    (req as any).tenantId = tenantId;
  }
  next();
};
app.use(tenantMiddleware);

// Logging personalizado
app.use((req, res, next) => {
  const start = Date.now();
  res.on("finish", () => {
    const duration = Date.now() - start;
    logger.info(
      `${req.method} ${req.path} - ${res.statusCode} - ${duration}ms`
    );
  });
  next();
});

// ==================== PRODUCTION DATA ONLY ====================
// Sistema configurado para usar apenas dados reais do banco de dados

// Health check
app.get("/health", (req, res) => {
  res.json({
    status: "OK",
    timestamp: new Date().toISOString(),
    version: "1.0.0",
    environment: process.env.NODE_ENV || "development",
    database: AppDataSource.isInitialized ? "connected" : "disconnected",
    redis: redisClient.isReady ? "connected" : "disconnected",
  });
});

// Rota raiz
app.get("/", (req, res) => {
  res.json({
    message: "Restaurant Playlist API está funcionando!",
    timestamp: new Date().toISOString(),
    version: "1.0.0",
    environment: process.env.NODE_ENV || "development",
    endpoints: {
      health: "/health",
      api: "/api/v1",
      docs: "/api/docs",
    },
  });
});

// Socket.IO
const io = new SocketIOServer(server, {
  cors: corsOptions,
  transports: ["websocket", "polling"],
  pingTimeout: 60000,
  pingInterval: 25000,
});

// Inicializar WebSocketService
const wsService = WebSocketService.getInstance(io);

// Disponibilizar Socket.IO para as rotas
app.set("io", io);

console.log("🔧 DEBUG: About to register routes...");

// Registrar rotas modulares
app.use("/api/v1/genres", genresRoutes);
app.use("/api/v1/restaurants", restaurantsRoutes);
app.use("/api/v1/suggestions", suggestionsRoutes);
app.use("/api/v1/playlists", playlistsRoutes);
app.use("/api/v1/analytics", analyticsRoutes);
app.use("/api/v1/auth", authRoutes);
app.use("/api/v1/client", clientRoutes);
app.use("/api/v1/notifications", notificationsRoutes);
app.use("/api/v1/payments", paymentsRoutes);
app.use("/api/v1/playback", playbackRoutes);
app.use("/api/v1/playback-queue", playbackQueueRoutes);
app.use("/api/v1/rewards", rewardsRoutes);
app.use("/api/v1/youtube", youtubeRoutes);
app.use("/api/test-youtube", testYoutubeRoutes);
// app.use("/api/v1/qrcode", qrcodeRoutes); // Removido - usando implementação inline
app.use("/api/v1/business-hours", businessHoursRoutes);
app.use("/api/v1/competitive-voting", competitiveVotingRoutes);
app.use("/api/v1/lyrics", lyricsRoutes);
app.use("/api/v1/schedules", schedulesRoutes);
app.use("/api/v1/playlist-schedules", playlistSchedulesRoutes);

console.log("✅ Routes registered successfully");

// Endpoints adicionais para compatibilidade
// Endpoint para obter dados de um restaurante específico
app.get("/api/v1/restaurants/:id", async (req, res) => {
  try {
    const { id } = req.params;
    const restaurantRepository = AppDataSource.getRepository(Restaurant);

    const restaurant = await restaurantRepository.findOne({
      where: { id },
      relations: ["playlists", "suggestions", "users"],
    });

    if (!restaurant) {
      return res.status(404).json({ error: "Restaurante não encontrado" });
    }

    res.json({ restaurant: restaurant.toPublicJSON() });
  } catch (error) {
    logger.error("Erro ao buscar restaurante:", error);
    res.status(500).json({ error: "Erro interno do servidor" });
  }
});

// Endpoint para listar restaurantes
app.get("/api/v1/restaurants", async (req, res) => {
  try {
    const restaurantRepository = AppDataSource.getRepository(Restaurant);

    const restaurants = await restaurantRepository.find({
      where: { isActive: true },
      order: { name: "ASC" },
    });

    res.json({
      restaurants: restaurants.map((r) => r.toPublicJSON()),
    });
  } catch (error) {
    logger.error("Erro ao listar restaurantes:", error);
    res.status(500).json({ error: "Erro interno do servidor" });
  }
});

// Endpoint removido - usando rota modular em /routes/qrcode.ts

// Endpoint removido - usando rota modular em /routes/qrcode.ts

// Endpoint removido - usando rota modular em /routes/qrcode.ts

// Endpoint removido - usando rota modular em /routes/qrcode.ts

// Endpoint para gêneros removido temporariamente para evitar problemas de metadata

// Endpoint para criar sugestão musical (gratuita ou paga)
app.post("/api/v1/suggestions", async (req, res) => {
  try {
    const {
      restaurantId,
      youtubeId,
      title,
      artist,
      duration,
      thumbnail,
      clientName,
      tableNumber,
      sessionId,
      isPriority = false, // false = gratuita, true = paga (R$2,00)
      paymentId = null, // ID do pagamento PIX se for prioritária
    } = req.body;

    if (!restaurantId || !youtubeId || !title) {
      return res.status(400).json({
        error: "RestaurantId, youtubeId e title são obrigatórios",
      });
    }

    const restaurantRepository = AppDataSource.getRepository(Restaurant);
    const suggestionRepository = AppDataSource.getRepository(Suggestion);

    // Verificar se o restaurante existe
    const restaurant = await restaurantRepository.findOne({
      where: { id: restaurantId },
    });

    if (!restaurant) {
      return res.status(404).json({ error: "Restaurante não encontrado" });
    }

    // Se for sugestão prioritária, verificar se o pagamento foi processado
    if (isPriority && !paymentId) {
      return res.status(400).json({
        error: "PaymentId é obrigatório para sugestões prioritárias",
      });
    }

    // Criar a sugestão
    const suggestion = suggestionRepository.create({
      restaurantId,
      youtubeId,
      title,
      artist: artist || "Artista Desconhecido",
      duration: duration || 0,
      thumbnail: thumbnail || "",
      clientName: clientName || "Anônimo",
      tableNumber: tableNumber || null,
      sessionId: sessionId || null,
      status: isPriority ? "approved" : "pending", // Sugestões pagas são aprovadas automaticamente
      isPriority,
      paymentId,
      paymentAmount: isPriority ? 2.0 : 0.0,
      paymentStatus: isPriority ? "paid" : null,
      votes: 0,
      createdAt: new Date(),
    });

    const savedSuggestion = await suggestionRepository.save(suggestion);

    // Emitir evento via Socket.IO para atualizar em tempo real
    io.to(`restaurant-${restaurantId}`).emit("newSuggestion", {
      suggestion: savedSuggestion,
      isPriority,
    });

    // Se for prioritária, também emitir para a fila de reprodução
    if (isPriority) {
      io.to(`restaurant-${restaurantId}`).emit("prioritySuggestion", {
        suggestion: savedSuggestion,
      });
    }

    res.json({
      success: true,
      suggestion: savedSuggestion,
      message: isPriority
        ? "Sugestão prioritária criada com sucesso!"
        : "Sugestão criada com sucesso!",
    });
  } catch (error) {
    logger.error("Erro ao criar sugestão:", error);
    res.status(500).json({ error: "Erro interno do servidor" });
  }
});

// Endpoint para gerar pagamento PIX para sugestão prioritária
app.post("/api/v1/payments/pix/suggestion", async (req, res) => {
  try {
    const {
      restaurantId,
      youtubeId,
      title,
      artist,
      clientName,
      tableNumber,
      sessionId,
    } = req.body;

    if (!restaurantId || !youtubeId || !title) {
      return res.status(400).json({
        error: "RestaurantId, youtubeId e title são obrigatórios",
      });
    }

    const restaurantRepository = AppDataSource.getRepository(Restaurant);
    const restaurant = await restaurantRepository.findOne({
      where: { id: restaurantId },
    });

    if (!restaurant) {
      return res.status(404).json({ error: "Restaurante não encontrado" });
    }

    // Gerar ID único para o pagamento
    const paymentId = uuidv4();
    const amount = 2.0; // R$ 2,00 fixo para sugestões prioritárias

    // Dados do PIX (em produção, integrar com gateway de pagamento real)
    const pixData = {
      id: paymentId,
      amount,
      description: `Sugestão prioritária: ${title}`,
      restaurantId,
      youtubeId,
      title,
      artist,
      clientName,
      tableNumber,
      sessionId,
      status: "pending",
      createdAt: new Date().toISOString(),
      expiresAt: new Date(Date.now() + 15 * 60 * 1000).toISOString(), // 15 minutos
      // Em produção, aqui seria gerado o código PIX real
      pixCode: `00020126580014BR.GOV.BCB.PIX0136${paymentId}520400005303986540${amount.toFixed(
        2
      )}5802BR5913${restaurant.name}6009SAO PAULO62070503***6304`,
      qrCodeData: `data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==`, // Placeholder
    };

    // Em produção, salvar no banco de dados
    // const paymentRepository = AppDataSource.getRepository(Payment);
    // await paymentRepository.save(pixData);

    res.json({
      success: true,
      payment: pixData,
      message: "Código PIX gerado com sucesso!",
    });
  } catch (error) {
    logger.error("Erro ao gerar pagamento PIX:", error);
    res.status(500).json({ error: "Erro interno do servidor" });
  }
});

// Endpoint para verificar status do pagamento PIX
app.get("/api/v1/payments/pix/:paymentId/status", async (req, res) => {
  try {
    const { paymentId } = req.params;

    if (!paymentId) {
      return res.status(400).json({ error: "PaymentId é obrigatório" });
    }

    // Em produção, consultar o gateway de pagamento real
    // Por enquanto, simular verificação
    const paymentStatus = {
      id: paymentId,
      status: "pending", // pending, paid, expired, cancelled
      paidAt: null,
      amount: 2.0,
    };

    // Simular pagamento aprovado após 30 segundos (apenas para demo)
    const createdTime = new Date(paymentId.substring(0, 8)).getTime();
    const now = Date.now();
    if (now - createdTime > 30000) {
      paymentStatus.status = "paid";
      paymentStatus.paidAt = new Date().toISOString();
    }

    res.json({
      success: true,
      payment: paymentStatus,
    });
  } catch (error) {
    logger.error("Erro ao verificar status do pagamento:", error);
    res.status(500).json({ error: "Erro interno do servidor" });
  }
});

// Endpoint para confirmar pagamento e criar sugestão prioritária
app.post("/api/v1/payments/pix/:paymentId/confirm", async (req, res) => {
  try {
    const { paymentId } = req.params;
    const {
      restaurantId,
      youtubeId,
      title,
      artist,
      clientName,
      tableNumber,
      sessionId,
    } = req.body;

    if (!paymentId || !restaurantId || !youtubeId || !title) {
      return res.status(400).json({
        error: "PaymentId, restaurantId, youtubeId e title são obrigatórios",
      });
    }

    // Verificar se o pagamento foi aprovado
    // Em produção, consultar o gateway de pagamento
    const paymentStatus = "paid"; // Simular pagamento aprovado

    if (paymentStatus !== "paid") {
      return res.status(400).json({
        error: "Pagamento não foi aprovado ainda",
      });
    }

    // Criar sugestão prioritária
    const suggestionRepository = AppDataSource.getRepository(Suggestion);
    const suggestion = suggestionRepository.create({
      restaurantId,
      youtubeId,
      title,
      artist: artist || "Artista Desconhecido",
      clientName: clientName || "Anônimo",
      tableNumber: tableNumber || null,
      sessionId: sessionId || null,
      status: "approved",
      isPriority: true,
      paymentId,
      paymentAmount: 2.0,
      paymentStatus: "paid",
      votes: 0,
      createdAt: new Date(),
    });

    const savedSuggestion = await suggestionRepository.save(suggestion);

    // Emitir eventos via Socket.IO
    io.to(`restaurant-${restaurantId}`).emit("prioritySuggestion", {
      suggestion: savedSuggestion,
    });

    res.json({
      success: true,
      suggestion: savedSuggestion,
      message: "Sugestão prioritária criada com sucesso!",
    });
  } catch (error) {
    logger.error("Erro ao confirmar pagamento:", error);
    res.status(500).json({ error: "Erro interno do servidor" });
  }
});

// Endpoint para listar sugestões de um restaurante
app.get("/api/v1/suggestions/:restaurantId", async (req, res) => {
  try {
    const { restaurantId } = req.params;
    const { status, limit = 50, offset = 0 } = req.query;

    if (!restaurantId) {
      return res.status(400).json({ error: "RestaurantId é obrigatório" });
    }

    const suggestionRepository = AppDataSource.getRepository(Suggestion);

    const whereConditions: any = { restaurantId };
    if (status) {
      whereConditions.status = status;
    }

    const suggestions = await suggestionRepository.find({
      where: whereConditions,
      order: {
        isPriority: "DESC", // Prioritárias primeiro
        createdAt: "DESC",
      },
      take: Number(limit),
      skip: Number(offset),
    });

    res.json({
      success: true,
      suggestions,
      total: suggestions.length,
    });
  } catch (error) {
    logger.error("Erro ao listar sugestões:", error);
    res.status(500).json({ error: "Erro interno do servidor" });
  }
});

// Endpoint para votar em uma sugestão
app.post("/api/v1/suggestions/:suggestionId/vote", async (req, res) => {
  try {
    const { suggestionId } = req.params;
    const { sessionId, tableNumber } = req.body;

    if (!suggestionId) {
      return res.status(400).json({ error: "SuggestionId é obrigatório" });
    }

    const suggestionRepository = AppDataSource.getRepository(Suggestion);
    const voteRepository = AppDataSource.getRepository(Vote);

    // Verificar se a sugestão existe
    const suggestion = await suggestionRepository.findOne({
      where: { id: suggestionId },
    });

    if (!suggestion) {
      return res.status(404).json({ error: "Sugestão não encontrada" });
    }

    // Verificar se já votou (por sessionId ou tableNumber)
    const existingVote = await voteRepository.findOne({
      where: [
        { suggestionId, sessionId },
        { suggestionId, tableNumber },
      ],
    });

    if (existingVote) {
      return res.status(400).json({ error: "Você já votou nesta sugestão" });
    }

    // Criar o voto
    const vote = voteRepository.create({
      suggestionId,
      sessionId,
      tableNumber,
      createdAt: new Date(),
    });

    await voteRepository.save(vote);

    // Atualizar contador de votos na sugestão
    suggestion.votes = (suggestion.votes || 0) + 1;
    await suggestionRepository.save(suggestion);

    // Emitir evento via Socket.IO
    io.to(`restaurant-${suggestion.restaurantId}`).emit("voteUpdate", {
      suggestionId,
      votes: suggestion.votes,
    });

    res.json({
      success: true,
      votes: suggestion.votes,
      message: "Voto registrado com sucesso!",
    });
  } catch (error) {
    logger.error("Erro ao votar:", error);
    res.status(500).json({ error: "Erro interno do servidor" });
  }
});

// Endpoint para aprovar/rejeitar sugestão (admin)
app.patch("/api/v1/suggestions/:suggestionId/status", async (req, res) => {
  try {
    const { suggestionId } = req.params;
    const { status, reason } = req.body;

    if (!suggestionId || !status) {
      return res.status(400).json({
        error: "SuggestionId e status são obrigatórios",
      });
    }

    if (!["approved", "rejected", "pending"].includes(status)) {
      return res.status(400).json({
        error: "Status deve ser: approved, rejected ou pending",
      });
    }

    const suggestionRepository = AppDataSource.getRepository(Suggestion);
    const suggestion = await suggestionRepository.findOne({
      where: { id: suggestionId },
    });

    if (!suggestion) {
      return res.status(404).json({ error: "Sugestão não encontrada" });
    }

    // Atualizar status
    suggestion.status = status as any;
    if (reason) {
      suggestion.rejectionReason = reason;
    }
    suggestion.updatedAt = new Date();

    await suggestionRepository.save(suggestion);

    // Emitir evento via Socket.IO
    io.to(`restaurant-${suggestion.restaurantId}`).emit(
      "suggestionStatusUpdate",
      {
        suggestionId,
        status,
        reason,
      }
    );

    res.json({
      success: true,
      suggestion,
      message: `Sugestão ${
        status === "approved" ? "aprovada" : "rejeitada"
      } com sucesso!`,
    });
  } catch (error) {
    logger.error("Erro ao atualizar status da sugestão:", error);
    res.status(500).json({ error: "Erro interno do servidor" });
  }
});

// Endpoint para buscar músicas no YouTube
app.get("/api/v1/youtube/search", async (req, res) => {
  try {
    const { q, maxResults = 10 } = req.query;

    if (!q) {
      return res.status(400).json({ error: "Query (q) é obrigatória" });
    }

    // Em produção, usar a API real do YouTube
    // Por enquanto, retornar dados simulados
    const mockResults = [
      {
        id: { videoId: "dQw4w9WgXcQ" },
        snippet: {
          title: "Rick Astley - Never Gonna Give You Up",
          channelTitle: "Rick Astley",
          thumbnails: {
            default: { url: "https://i.ytimg.com/vi/dQw4w9WgXcQ/default.jpg" },
            medium: { url: "https://i.ytimg.com/vi/dQw4w9WgXcQ/mqdefault.jpg" },
            high: { url: "https://i.ytimg.com/vi/dQw4w9WgXcQ/hqdefault.jpg" },
          },
          publishedAt: "2009-10-25T06:57:33Z",
        },
        contentDetails: {
          duration: "PT3M33S",
        },
      },
    ];

    res.json({
      success: true,
      items: mockResults,
      query: q,
      maxResults: Number(maxResults),
    });
  } catch (error) {
    logger.error("Erro ao buscar no YouTube:", error);
    res.status(500).json({ error: "Erro interno do servidor" });
  }
});

// Endpoint removido - usando rota modular em /routes/qrcode.ts

// Endpoint duplicado removido - usar routes/client.ts

// Endpoint para verificar sessão do cliente
app.get("/api/v1/client/session/:sessionId", async (req, res) => {
  try {
    const { sessionId } = req.params;

    if (!sessionId) {
      return res.status(400).json({ error: "SessionId é obrigatório" });
    }

    // Buscar sessão no Redis
    const sessionData = await redisClient.get(`client_session:${sessionId}`);

    if (!sessionData) {
      return res
        .status(404)
        .json({ error: "Sessão não encontrada ou expirada" });
    }

    const session = JSON.parse(sessionData);

    // Buscar dados do restaurante
    const restaurantRepository = AppDataSource.getRepository(Restaurant);
    const restaurant = await restaurantRepository.findOne({
      where: { id: session.restaurantId },
    });

    if (!restaurant) {
      return res.status(404).json({ error: "Restaurante não encontrado" });
    }

    res.json({
      success: true,
      session,
      restaurant: {
        id: restaurant.id,
        name: restaurant.name,
        description: restaurant.description,
      },
    });
  } catch (error) {
    logger.error("Erro ao verificar sessão do cliente:", error);
    res.status(500).json({ error: "Erro interno do servidor" });
  }
});

// Socket.IO handlers
io.on("connection", (socket) => {
  console.log(`Cliente conectado: ${socket.id}`);

  // Join restaurant room
  socket.on("join-restaurant", (restaurantId: string) => {
    socket.join(`restaurant-${restaurantId}`);
    console.log(`Socket ${socket.id} joined restaurant ${restaurantId}`);
  });

  // Join table room
  socket.on(
    "join-table",
    (data: { restaurantId: string; tableNumber: string }) => {
      const roomName = `restaurant-${data.restaurantId}-table-${data.tableNumber}`;
      socket.join(roomName);
      console.log(
        `Socket ${socket.id} joined table ${data.tableNumber} in restaurant ${data.restaurantId}`
      );
    }
  );

  // Handle suggestion events
  socket.on("new-suggestion", (data) => {
    socket.to(`restaurant-${data.restaurantId}`).emit("suggestion-added", data);
  });

  // Handle vote events
  socket.on("new-vote", (data) => {
    socket.to(`restaurant-${data.restaurantId}`).emit("vote-added", data);
  });

  // Handle playback events
  socket.on("playback-update", (data) => {
    socket
      .to(`restaurant-${data.restaurantId}`)
      .emit("playback-state-changed", data);
  });

  socket.on("disconnect", () => {
    console.log(`Cliente desconectado: ${socket.id}`);
  });
});

// Middleware de tratamento de erros
app.use(
  (
    err: any,
    req: express.Request,
    res: express.Response,
    next: express.NextFunction
  ) => {
    logger.error("Erro não tratado:", err);

    if (res.headersSent) {
      return next(err);
    }

    const statusCode = err.statusCode || err.status || 500;
    const message = err.message || "Erro interno do servidor";

    res.status(statusCode).json({
      error: message,
      ...(process.env.NODE_ENV === "development" && { stack: err.stack }),
    });
  }
);

// ==================== ENDPOINTS QR CODE ====================

// Listar QR Codes de um restaurante
app.get("/api/v1/qrcode/:restaurantId", async (req, res) => {
  try {
    const { restaurantId } = req.params;
    logger.info("📱 Carregando QR Codes para restaurante:", restaurantId);

    const qrCodeRepository = AppDataSource.getRepository(QRCodeModel);

    // Buscar QR codes do banco de dados
    const qrCodes = await qrCodeRepository.find({
      where: {
        restaurant: { id: restaurantId },
        isActive: true,
      },
      relations: ["restaurant"],
      order: { createdAt: "DESC" },
    });

    logger.info("📊 Total de QR Codes encontrados:", qrCodes.length);

    res.json({
      success: true,
      qrCodes: qrCodes.map((qr) => qr.toPublicJSON()),
      total: qrCodes.length,
      restaurantId,
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    logger.error("Erro ao buscar QR Codes:", error);
    res.status(500).json({
      success: false,
      error: "Erro interno do servidor",
    });
  }
});

// Gerar QR Code para mesa específica
app.post("/api/v1/qrcode/table", async (req, res) => {
  try {
    console.log("🚨 ROTA EXECUTADA: /api/v1/qrcode/table");
    console.log("🚨 BODY:", req.body);

    const { restaurantId, tableNumber, tableName } = req.body;

    if (!restaurantId || !tableNumber) {
      return res.status(400).json({
        success: false,
        error: "restaurantId e tableNumber são obrigatórios",
      });
    }

    logger.info("🏷️ Gerando QR Code para mesa:", {
      restaurantId,
      tableNumber,
      tableName,
    });

    const qrCodeRepository = AppDataSource.getRepository(QRCodeModel);
    const restaurantRepository = AppDataSource.getRepository(Restaurant);

    // Verificar se o restaurante existe
    const restaurant = await restaurantRepository.findOne({
      where: { id: restaurantId },
    });

    if (!restaurant) {
      return res.status(404).json({
        success: false,
        error: "Restaurante não encontrado",
      });
    }

    // Verificar se já existe QR Code para esta mesa
    const existingQR = await qrCodeRepository.findOne({
      where: {
        restaurant: { id: restaurantId },
        tableNumber: String(tableNumber),
        type: QRCodeType.TABLE,
        isActive: true,
      },
    });

    if (existingQR) {
      return res.status(400).json({
        success: false,
        error: `QR Code para mesa ${tableNumber} já existe`,
      });
    }

    // Gerar URL do QR Code
    const qrUrl = `http://localhost:8000/client/${restaurantId}?tableNumber=${tableNumber}`;

    // Gerar QR Code visual usando a biblioteca qrcode
    const qrCodeDataURL = await QRCode.toDataURL(qrUrl, {
      errorCorrectionLevel: "M",
      type: "image/png",
      quality: 0.92,
      margin: 1,
      color: {
        dark: "#000000",
        light: "#FFFFFF",
      },
      width: 256,
    });

    // Criar QR Code no banco de dados
    const qrCode = qrCodeRepository.create({
      restaurant: restaurant,
      type: QRCodeType.TABLE,
      name: tableName || `Mesa ${tableNumber}`,
      tableNumber: String(tableNumber),
      url: qrUrl,
      qrCodeData: qrCodeDataURL,
      isActive: true,
    });

    const savedQRCode = await qrCodeRepository.save(qrCode);

    logger.info("✅ QR Code criado no banco:", savedQRCode.id);

    res.status(201).json({
      success: true,
      message: "QR Code gerado com sucesso",
      qrCode: savedQRCode.toPublicJSON(),
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    logger.error("Erro ao criar QR Code:", error);
    res.status(500).json({
      success: false,
      error: "Erro interno do servidor",
    });
  }
});

// Gerar QR Codes em lote para múltiplas mesas
app.post("/api/v1/qrcode/bulk-tables", async (req, res) => {
  try {
    const { restaurantId, tableCount, tablePrefix = "Mesa" } = req.body;

    if (!restaurantId || !tableCount || tableCount < 1 || tableCount > 50) {
      return res.status(400).json({
        success: false,
        error:
          "restaurantId é obrigatório e tableCount deve estar entre 1 e 50",
      });
    }

    console.log("🏷️ Gerando QR Codes em lote:", {
      restaurantId,
      tableCount,
      tablePrefix,
    });

    const qrCodeRepository = AppDataSource.getRepository(QRCodeModel);
    const restaurantRepository = AppDataSource.getRepository(Restaurant);

    // Verificar se o restaurante existe
    const restaurant = await restaurantRepository.findOne({
      where: { id: restaurantId },
    });

    if (!restaurant) {
      return res.status(404).json({
        success: false,
        error: "Restaurante não encontrado",
      });
    }

    const newQRCodes = [];
    let generated = 0;

    for (let i = 1; i <= tableCount; i++) {
      // Verificar se já existe
      const existingQR = await qrCodeRepository.findOne({
        where: {
          restaurant: { id: restaurantId },
          tableNumber: String(i),
          type: QRCodeType.TABLE,
          isActive: true,
        },
      });

      if (!existingQR) {
        const qrUrl = `http://localhost:8000/client/${restaurantId}?tableNumber=${i}`;
        
        // Gerar QR Code visual usando a biblioteca qrcode
        const qrCodeDataURL = await QRCode.toDataURL(qrUrl, {
          errorCorrectionLevel: 'M',
          type: 'image/png',
          quality: 0.92,
          margin: 1,
          color: {
            dark: '#000000',
            light: '#FFFFFF',
          },
          width: 256,
        });

        const qrCode = qrCodeRepository.create({
          restaurant: restaurant,
          type: QRCodeType.TABLE,
          name: `${tablePrefix} ${i}`,
          tableNumber: String(i),
          url: qrUrl,
          qrCodeData: qrCodeDataURL,
          isActive: true,
        });

        const savedQRCode = await qrCodeRepository.save(qrCode);
        newQRCodes.push(savedQRCode.toPublicJSON());
        generated++;
      }
    }

    console.log(`✅ ${generated} QR Codes gerados em lote`);

    res.status(201).json({
      success: true,
      message: `${generated} QR Codes gerados com sucesso`,
      qrCodes: newQRCodes,
      totalGenerated: generated,
      skipped: tableCount - generated,
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    logger.error("Erro ao criar QR Codes em lote:", error);
    res.status(500).json({
      success: false,
      error: "Erro interno do servidor",
    });
  }
});

// Gerar QR Code geral do restaurante
app.post("/api/v1/qrcode/restaurant", async (req, res) => {
  try {
    const { restaurantId, restaurantName } = req.body;

    if (!restaurantId) {
      return res.status(400).json({
        success: false,
        error: "restaurantId é obrigatório",
      });
    }

    console.log("🏪 Gerando QR Code geral do restaurante:", {
      restaurantId,
      restaurantName,
    });

    const qrCodeRepository = AppDataSource.getRepository(QRCodeModel);
    const restaurantRepository = AppDataSource.getRepository(Restaurant);

    // Verificar se o restaurante existe
    const restaurant = await restaurantRepository.findOne({
      where: { id: restaurantId },
    });

    if (!restaurant) {
      return res.status(404).json({
        success: false,
        error: "Restaurante não encontrado",
      });
    }

    // Verificar se já existe QR Code geral para este restaurante
    const existingQR = await qrCodeRepository.findOne({
      where: {
        restaurant: { id: restaurantId },
        type: QRCodeType.RESTAURANT,
        isActive: true,
      },
    });

    if (existingQR) {
      return res.status(400).json({
        success: false,
        error: "QR Code geral do restaurante já existe",
      });
    }

    const qrUrl = `http://localhost:8000/client/${restaurantId}`;
    
    // Gerar QR Code visual usando a biblioteca qrcode
    const qrCodeDataURL = await QRCode.toDataURL(qrUrl, {
      errorCorrectionLevel: 'M',
      type: 'image/png',
      quality: 0.92,
      margin: 1,
      color: {
        dark: '#000000',
        light: '#FFFFFF',
      },
      width: 256,
    });

    const qrCode = qrCodeRepository.create({
      restaurant: restaurant,
      type: QRCodeType.RESTAURANT,
      name: restaurantName || "Acesso Geral",
      url: qrUrl,
      qrCodeData: qrCodeDataURL,
      isActive: true,
    });

    const savedQRCode = await qrCodeRepository.save(qrCode);

    console.log("✅ QR Code geral criado:", savedQRCode.id);

    res.status(201).json({
      success: true,
      message: "QR Code geral gerado com sucesso",
      qrCode: savedQRCode.toPublicJSON(),
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    logger.error("Erro ao criar QR Code geral:", error);
    res.status(500).json({
      success: false,
      error: "Erro interno do servidor",
    });
  }
});

// Deletar QR Code
app.delete("/api/v1/qrcode/:qrCodeId", async (req, res) => {
  try {
    const { qrCodeId } = req.params;

    if (!qrCodeId) {
      return res.status(400).json({
        success: false,
        error: "qrCodeId é obrigatório",
      });
    }

    console.log("🗑️ Deletando QR Code:", qrCodeId);

    const qrCodeRepository = AppDataSource.getRepository(QRCodeModel);

    const qrCode = await qrCodeRepository.findOne({
      where: { id: qrCodeId },
      relations: ["restaurant"],
    });

    if (!qrCode) {
      return res.status(404).json({
        success: false,
        error: "QR Code não encontrado",
      });
    }

    // Soft delete - marcar como inativo ao invés de deletar
    qrCode.isActive = false;
    await qrCodeRepository.save(qrCode);

    console.log("✅ QR Code desativado:", qrCode.name);

    res.json({
      success: true,
      message: "QR Code deletado com sucesso",
      deletedQRCode: qrCode.toPublicJSON(),
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    logger.error("Erro ao deletar QR Code:", error);
    res.status(500).json({
      success: false,
      error: "Erro interno do servidor",
    });
  }
});

// Atualizar imagem do QR Code
app.post("/api/v1/qrcode/update-image", async (req, res) => {
  try {
    const { qrCodeId, qrCodeDataURL } = req.body;

    if (!qrCodeId || !qrCodeDataURL) {
      return res.status(400).json({
        success: false,
        error: "qrCodeId e qrCodeDataURL são obrigatórios",
      });
    }

    console.log("🔄 Atualizando imagem do QR Code:", qrCodeId);

    const qrCodeRepository = AppDataSource.getRepository(QRCodeModel);

    const qrCode = await qrCodeRepository.findOne({
      where: { id: qrCodeId },
      relations: ["restaurant"],
    });

    if (!qrCode) {
      return res.status(404).json({
        success: false,
        error: "QR Code não encontrado",
      });
    }

    // Atualizar a imagem do QR Code
    qrCode.qrCodeData = qrCodeDataURL;
    await qrCodeRepository.save(qrCode);

    console.log("✅ Imagem do QR Code atualizada:", qrCode.name);

    res.json({
      success: true,
      message: "Imagem do QR Code atualizada com sucesso",
      qrCode: qrCode.toPublicJSON(),
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    logger.error("Erro ao atualizar imagem do QR Code:", error);
    res.status(500).json({
      success: false,
      error: "Erro interno do servidor",
    });
  }
});

// 404 handler
app.use("*", (req, res) => {
  res.status(404).json({
    error: "Endpoint não encontrado",
    path: req.originalUrl,
    method: req.method,
  });
});

// Função para inicializar o servidor
async function startServer() {
  try {
    console.log("🔄 Inicializando servidor...");

    // Inicializar banco de dados primeiro
    console.log("🔄 Conectando ao banco de dados...");
    await initializeDatabase();
    console.log("✅ Database initialized");

    // Verificar se as entidades estão carregadas
    const entities = AppDataSource.entityMetadatas;
    console.log(
      `✅ ${entities.length} entidades carregadas:`,
      entities.map((e) => e.name)
    );

    // Inicializar Redis
    console.log("🔄 Conectando ao Redis...");
    if (!redisClient.isReady()) {
      await redisClient.connect();
      console.log("✅ Redis connected");
    } else {
      console.log("✅ Redis already connected");
    }

    // Inicializar serviços
    console.log("🔄 Inicializando serviços...");
    const youtubeService = new YouTubeService();
    const notificationService = new NotificationService();
    const playbackService = PlaybackService.getInstance();
    const rewardService = new RewardService();

    console.log("✅ Services initialized");

    // Criar dados de exemplo se não existirem
    await createSampleData();

    // Iniciar servidor
    const PORT = process.env.PORT || 5000;
    server.listen(PORT, () => {
      console.log(`🚀 Servidor rodando na porta ${PORT}`);
      console.log(`📊 Health check: http://localhost:${PORT}/health`);
      console.log(`🌐 API: http://localhost:${PORT}/api/v1`);
      console.log(`🎵 Sistema de Playlist Interativa - Pronto!`);
    });
  } catch (error) {
    console.error("❌ Erro ao inicializar servidor:", error);
    console.error("Stack trace:", error.stack);
    process.exit(1);
  }
}

// Função para criar dados de exemplo
async function createSampleData() {
  try {
    const restaurantRepository = AppDataSource.getRepository(Restaurant);

    // Verificar se já existe o restaurante demo
    const existingRestaurant = await restaurantRepository.findOne({
      where: { id: "demo-restaurant" },
    });

    if (!existingRestaurant) {
      console.log("🔄 Criando restaurante demo...");

      const demoRestaurant = restaurantRepository.create({
        id: "demo-restaurant",
        name: "Restaurante Demo",
        description: "Restaurante de demonstração do sistema",
        email: "<EMAIL>",
        phone: "(11) 99999-9999",
        isActive: true,
        status: "active" as any,
        settings: {
          moderation: {
            autoApprove: false,
            requireApproval: true,
            maxSuggestionsPerUser: 5,
            maxSuggestionsPerHour: 20,
            allowExplicitContent: false,
            allowLiveVideos: true,
            minVideoDuration: 30,
            maxVideoDuration: 600,
          },
          playlist: {
            maxQueueSize: 50,
            allowDuplicates: false,
            shuffleMode: false,
            repeatMode: "none",
            crossfadeDuration: 3,
            defaultVolume: 70,
          },
          interface: {
            allowSuggestions: true,
            allowVoting: true,
            showQueue: true,
            showHistory: true,
            allowAnonymous: true,
            requireTableNumber: false,
          },
        },
        businessHours: {
          monday: { open: "18:00", close: "02:00", isOpen: true },
          tuesday: { open: "18:00", close: "02:00", isOpen: true },
          wednesday: { open: "18:00", close: "02:00", isOpen: true },
          thursday: { open: "18:00", close: "02:00", isOpen: true },
          friday: { open: "18:00", close: "03:00", isOpen: true },
          saturday: { open: "18:00", close: "03:00", isOpen: true },
          sunday: { open: "18:00", close: "01:00", isOpen: true },
        },
      });

      await restaurantRepository.save(demoRestaurant);
      console.log("✅ Restaurante demo criado");
    } else {
      console.log("✅ Restaurante demo já existe");
    }
  } catch (error) {
    console.error("❌ Erro ao criar dados de exemplo:", error);
    // Não falhar o servidor por causa disso
  }
}

// Graceful shutdown
process.on("SIGTERM", async () => {
  console.log("🔄 Recebido SIGTERM, encerrando servidor...");
  server.close(async () => {
    await AppDataSource.destroy();
    await redisClient.quit();
    console.log("✅ Servidor encerrado graciosamente");
    process.exit(0);
  });
});

process.on("SIGINT", async () => {
  console.log("🔄 Recebido SIGINT, encerrando servidor...");
  server.close(async () => {
    await AppDataSource.destroy();
    await redisClient.quit();
    console.log("✅ Servidor encerrado graciosamente");
    process.exit(0);
  });
});

// Inicializar servidor
startServer();

export { app, server, io };
