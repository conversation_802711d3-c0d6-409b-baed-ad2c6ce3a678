import { Router, Request, Response } from "express";
import { param, body, validationResult } from "express-validator";
import { In } from "typeorm";
import { AppDataSource } from "../config/database";
import { Playlist, PlaylistStatus, PlaylistType } from "../models/Playlist";
import { Restaurant } from "../models/Restaurant";
import asyncHandler from "../middleware/asyncHandler";
import {
  createValidationError,
  NotFoundError,
  ValidationErrorClass,
} from "../middleware/errorHandler";
import { authMiddleware, optionalAuth } from "../middleware/auth";
import { youtubeService } from "../config/youtube";

const router = Router();

/**
 * @swagger
 * /api/v1/playlists/{restaurantId}:
 *   get:
 *     summary: Obter playlists de um restaurante
 *     tags: [Playlists]
 *     parameters:
 *       - in: path
 *         name: restaurantId
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *     responses:
 *       200:
 *         description: Lista de playlists
 */
router.get(
  "/:restaurantId",
  [
    param("restaurantId")
      .notEmpty()
      .withMessage("ID do restaurante é obrigatório"),
  ],
  optionalAuth,
  asyncHandler(async (req: Request, res: Response) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      throw createValidationError(errors.array());
    }

    const { restaurantId } = req.params;
    const playlistRepository = AppDataSource.getRepository(Playlist);

    const playlists = await playlistRepository.find({
      where: {
        restaurant: { id: restaurantId },
        status: In([PlaylistStatus.ACTIVE, PlaylistStatus.INACTIVE]),
      },
      relations: ["suggestions"],
      order: { createdAt: "DESC" },
    });

    res.json({
      playlists: playlists.map((p) => ({
        id: p.id,
        name: p.name,
        description: p.description,
        youtubePlaylistId: p.youtubePlaylistId,
        youtubeUrl: p.youtubePlaylistId
          ? `https://www.youtube.com/playlist?list=${p.youtubePlaylistId}`
          : null,
        thumbnail: p.coverImage,
        videoCount: p.trackCount,
        isActive: p.status === PlaylistStatus.ACTIVE,
        isDefault: p.isDefault,
        tags: [...(p.genreTags || []), ...(p.moodTags || [])],
        suggestionsCount: p.suggestions?.length || 0,
        createdAt: p.createdAt,
        lastSync: p.updatedAt,
        schedule: p.schedule,
      })),
    });
  })
);

/**
 * @swagger
 * /api/v1/playlists/{id}/sync:
 *   post:
 *     summary: Sincronizar playlist com YouTube
 *     tags: [Playlists]
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Playlist sincronizada com sucesso
 *       404:
 *         description: Playlist não encontrada
 */
router.post(
  "/:id/sync",
  [param("id").notEmpty().withMessage("ID da playlist é obrigatório")],
  optionalAuth,
  asyncHandler(async (req: Request, res: Response) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      throw createValidationError(errors.array());
    }

    const { id } = req.params;
    const playlistRepository = AppDataSource.getRepository(Playlist);

    const playlist = await playlistRepository.findOne({
      where: { id },
      relations: ["restaurant"],
    });

    if (!playlist) {
      throw new NotFoundError("Playlist não encontrada");
    }

    if (!playlist.youtubePlaylistId) {
      throw new ValidationErrorClass(
        "Playlist não possui ID do YouTube para sincronização",
        []
      );
    }

    try {
      console.log(
        `🔄 Iniciando sincronização da playlist ${playlist.name} (${playlist.youtubePlaylistId})`
      );

      const youtubePlaylist = await youtubeService.getPlaylist(
        playlist.youtubePlaylistId
      );

      if (youtubePlaylist) {
        console.log(
          `✅ Dados obtidos do YouTube: ${youtubePlaylist.videos.length} vídeos`
        );

        playlist.trackCount = youtubePlaylist.videoCount;
        playlist.totalDuration = youtubePlaylist.totalDuration || 0;
        playlist.tracks = youtubePlaylist.videos.map((video, index) => ({
          youtubeVideoId: video.id,
          title: video.title,
          artist: video.channelTitle,
          duration: video.duration,
          formattedDuration: formatDurationHelper(video.duration),
          thumbnailUrl: video.thumbnailUrl,
          addedAt: new Date().toISOString(),
          position: index,
        }));

        await playlistRepository.save(playlist);

        console.log(
          `✅ Playlist sincronizada: ${playlist.tracks.length} tracks salvas`
        );

        res.json({
          success: true,
          message: "Playlist sincronizada com sucesso",
          playlist: {
            id: playlist.id,
            name: playlist.name,
            trackCount: playlist.trackCount,
            totalDuration: playlist.totalDuration,
            tracksCount: playlist.tracks.length,
            lastSync: playlist.updatedAt,
            tracks: playlist.tracks.slice(0, 5), // Primeiras 5 tracks para preview
          },
        });
      } else {
        console.error("❌ Não foi possível obter dados da playlist do YouTube");
        throw new Error("Não foi possível obter dados da playlist do YouTube");
      }
    } catch (error) {
      console.error("❌ Erro ao sincronizar playlist:", error);
      throw new Error(`Erro ao sincronizar com o YouTube: ${error.message}`);
    }
  })
);

/**
 * @swagger
 * /api/v1/playlists:
 *   post:
 *     summary: Criar nova playlist
 *     tags: [Playlists]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - restaurantId
 *               - name
 *               - youtubeUrl
 *             properties:
 *               restaurantId:
 *                 type: string
 *               name:
 *                 type: string
 *               description:
 *                 type: string
 *               youtubeUrl:
 *                 type: string
 *               tags:
 *                 type: array
 *                 items:
 *                   type: string
 *               isActive:
 *                 type: boolean
 *     responses:
 *       201:
 *         description: Playlist criada com sucesso
 *       400:
 *         description: Dados inválidos
 */
router.post(
  "/",
  [
    body("restaurantId")
      .notEmpty()
      .withMessage("ID do restaurante é obrigatório"),
    body("name").notEmpty().withMessage("Nome da playlist é obrigatório"),
    body("youtubeUrl")
      .notEmpty()
      .withMessage("URL do YouTube é obrigatória")
      .matches(/^https:\/\/(www\.)?(youtube\.com|youtu\.be)/)
      .withMessage("URL deve ser do YouTube"),
    body("description").optional().isString(),
    body("tags").optional().isArray(),
    body("isActive").optional().isBoolean(),
  ],
  optionalAuth,
  asyncHandler(async (req: Request, res: Response) => {
    console.log(`🚀 Iniciando criação de playlist...`);
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      throw createValidationError(errors.array());
    }

    const { restaurantId, name, description, youtubeUrl, tags, isActive } =
      req.body;

    // Verificar se o restaurante existe
    const restaurantRepository = AppDataSource.getRepository(Restaurant);
    const restaurant = await restaurantRepository.findOne({
      where: { id: restaurantId, isActive: true },
    });

    if (!restaurant) {
      throw new NotFoundError("Restaurante não encontrado");
    }

    // Extrair ID da playlist do YouTube da URL
    let youtubePlaylistId = "";
    try {
      const url = new URL(youtubeUrl);
      if (url.hostname.includes("youtube.com")) {
        youtubePlaylistId = url.searchParams.get("list") || "";
      }

      if (!youtubePlaylistId) {
        throw new ValidationErrorClass(
          "URL do YouTube inválida - não foi possível extrair ID da playlist",
          []
        );
      }
    } catch (error) {
      throw new ValidationErrorClass("URL do YouTube inválida", []);
    }

    const playlistRepository = AppDataSource.getRepository(Playlist);

    // Verificar se já existe uma playlist com o mesmo YouTube ID para este restaurante
    const existingPlaylist = await playlistRepository.findOne({
      where: {
        restaurant: { id: restaurantId },
        youtubePlaylistId,
        status: PlaylistStatus.ACTIVE,
      },
    });

    if (existingPlaylist) {
      throw new ValidationErrorClass(
        "Esta playlist do YouTube já foi adicionada",
        []
      );
    }

    // Criar nova playlist
    const playlist = playlistRepository.create({
      name,
      description: description || "",
      type: PlaylistType.YOUTUBE,
      status:
        isActive !== false ? PlaylistStatus.ACTIVE : PlaylistStatus.INACTIVE,
      youtubePlaylistId,
      genreTags: tags || [],
      isDefault: false,
      isPublic: true,
      trackCount: 0,
      totalDuration: 0,
      playCount: 0,
      averageRating: 0,
      tracks: [],
      settings: {
        shuffle: false,
        repeat: "none",
        autoPlay: true,
        crossfade: 3,
        volume: 80,
        allowVoting: true,
        allowSuggestions: true,
        maxSuggestionsPerUser: 5,
      },
      restaurant,
    });

    await playlistRepository.save(playlist);
    console.log(`✅ Playlist salva no banco de dados: ${playlist.id}`);

    // Sincronizar com o YouTube imediatamente
    try {
      console.log(`🔄 Sincronizando playlist ${playlist.name} com YouTube...`);
      console.log(`🔗 YouTube Playlist ID: ${youtubePlaylistId}`);

      const youtubePlaylist = await youtubeService.getPlaylist(
        youtubePlaylistId
      );

      console.log(
        `📊 Resultado da sincronização:`,
        youtubePlaylist ? "Sucesso" : "Falhou"
      );

      if (youtubePlaylist) {
        console.log(
          `✅ Obtidos ${youtubePlaylist.videos.length} vídeos do YouTube`
        );
        playlist.trackCount = youtubePlaylist.videoCount;
        playlist.totalDuration = youtubePlaylist.totalDuration || 0;
        playlist.tracks = youtubePlaylist.videos.map((video, index) => ({
          youtubeVideoId: video.id,
          title: video.title,
          artist: video.channelTitle,
          duration: video.duration,
          formattedDuration: formatDurationHelper(video.duration),
          thumbnailUrl: video.thumbnailUrl,
          addedAt: new Date().toISOString(),
          position: index,
        }));
        await playlistRepository.save(playlist);
        console.log(
          `✅ Playlist sincronizada com ${playlist.tracks.length} tracks`
        );
      } else {
        console.log(`❌ Não foi possível obter dados da playlist do YouTube`);
      }
    } catch (error) {
      console.error("⚠️ Erro ao sincronizar playlist com YouTube:", error);
      console.error("⚠️ Stack trace:", error.stack);
      // Não falhar a criação da playlist se a sincronização falhar
    }

    res.status(201).json({
      success: true,
      message: "Playlist criada com sucesso",
      playlist: {
        id: playlist.id,
        name: playlist.name,
        description: playlist.description,
        youtubePlaylistId: playlist.youtubePlaylistId,
        isDefault: playlist.isDefault,
        trackCount: playlist.trackCount,
        status: playlist.status,
        createdAt: playlist.createdAt,
      },
    });
  })
);

/**
 * @swagger
 * /api/v1/playlists/{id}:
 *   put:
 *     summary: Atualizar playlist
 *     tags: [Playlists]
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               name:
 *                 type: string
 *               description:
 *                 type: string
 *               youtubeUrl:
 *                 type: string
 *               tags:
 *                 type: array
 *                 items:
 *                   type: string
 *               isActive:
 *                 type: boolean
 *     responses:
 *       200:
 *         description: Playlist atualizada com sucesso
 *       404:
 *         description: Playlist não encontrada
 */
router.put(
  "/:id",
  [
    param("id").notEmpty().withMessage("ID da playlist é obrigatório"),
    body("name").optional().notEmpty().withMessage("Nome não pode ser vazio"),
    body("youtubeUrl")
      .optional()
      .matches(/^https:\/\/(www\.)?(youtube\.com|youtu\.be)/)
      .withMessage("URL deve ser do YouTube"),
    body("description").optional().isString(),
    body("tags").optional().isArray(),
    body("isActive").optional().isBoolean(),
  ],
  optionalAuth,
  asyncHandler(async (req: Request, res: Response) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      throw createValidationError(errors.array());
    }

    const { id } = req.params;
    const { name, description, youtubeUrl, tags, isActive } = req.body;

    const playlistRepository = AppDataSource.getRepository(Playlist);

    const playlist = await playlistRepository.findOne({
      where: { id },
      relations: ["restaurant"],
    });

    if (!playlist) {
      throw new NotFoundError("Playlist não encontrada");
    }

    // Atualizar campos
    if (name !== undefined) playlist.name = name;
    if (description !== undefined) playlist.description = description;
    if (tags !== undefined) playlist.genreTags = tags;
    if (isActive !== undefined) {
      playlist.status = isActive
        ? PlaylistStatus.ACTIVE
        : PlaylistStatus.INACTIVE;
    }

    // Se a URL do YouTube foi alterada, extrair novo ID
    if (youtubeUrl && youtubeUrl !== playlist.youtubePlaylistId) {
      let youtubePlaylistId = "";
      try {
        const url = new URL(youtubeUrl);
        if (url.hostname.includes("youtube.com")) {
          youtubePlaylistId = url.searchParams.get("list") || "";
        }

        if (!youtubePlaylistId) {
          throw new ValidationErrorClass(
            "URL do YouTube inválida - não foi possível extrair ID da playlist",
            []
          );
        }

        playlist.youtubePlaylistId = youtubePlaylistId;

        // Resetar dados da playlist para nova sincronização
        playlist.trackCount = 0;
        playlist.totalDuration = 0;
        playlist.tracks = [];
      } catch (error) {
        if (error instanceof ValidationErrorClass) throw error;
        throw new ValidationErrorClass("URL do YouTube inválida", []);
      }
    }

    await playlistRepository.save(playlist);

    res.json({
      success: true,
      message: "Playlist atualizada com sucesso",
      playlist: {
        id: playlist.id,
        name: playlist.name,
        description: playlist.description,
        youtubePlaylistId: playlist.youtubePlaylistId,
        isDefault: playlist.isDefault,
        trackCount: playlist.trackCount,
        status: playlist.status,
        updatedAt: playlist.updatedAt,
      },
    });
  })
);

/**
 * @swagger
 * /api/v1/playlists/{id}:
 *   delete:
 *     summary: Deletar playlist
 *     tags: [Playlists]
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Playlist deletada com sucesso
 *       404:
 *         description: Playlist não encontrada
 *       400:
 *         description: Não é possível deletar playlist padrão
 */
router.delete(
  "/:id",
  [param("id").notEmpty().withMessage("ID da playlist é obrigatório")],
  optionalAuth,
  asyncHandler(async (req: Request, res: Response) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      throw createValidationError(errors.array());
    }

    const { id } = req.params;
    const playlistRepository = AppDataSource.getRepository(Playlist);

    const playlist = await playlistRepository.findOne({
      where: { id },
    });

    if (!playlist) {
      throw new NotFoundError("Playlist não encontrada");
    }

    if (playlist.isDefault) {
      throw new ValidationErrorClass(
        "Não é possível deletar a playlist padrão",
        []
      );
    }

    // Soft delete - marcar como inativa ao invés de deletar
    playlist.status = PlaylistStatus.DELETED;
    await playlistRepository.save(playlist);

    res.json({
      success: true,
      message: "Playlist deletada com sucesso",
    });
  })
);

// Função auxiliar para formatar duração
function formatDurationHelper(seconds: number): string {
  const minutes = Math.floor(seconds / 60);
  const remainingSeconds = seconds % 60;
  return `${minutes}:${remainingSeconds.toString().padStart(2, "0")}`;
}

export default router;
