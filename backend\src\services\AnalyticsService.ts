import { AppDataSource } from "../config/database";
import { AnalyticsDaily } from "../models/AnalyticsDaily";
import { Restaurant } from "../models/Restaurant";
import { Suggestion } from "../models/Suggestion";
import { Vote } from "../models/Vote";
import { ClientSession } from "../models/ClientSession";
import { redisClient } from "../config/redis";
import { Between, MoreThan } from "typeorm";

export interface AnalyticsMetrics {
  totalSuggestions: number;
  approvedSuggestions: number;
  rejectedSuggestions: number;
  totalVotes: number;
  upvotes: number;
  downvotes: number;
  uniqueSessions: number;
  totalSessions: number;
  averageSessionDuration: number;
  totalPlayTime: number;
  songsPlayed: number;
  songsSkipped: number;
  peakHour: number | null;
  topGenres: Array<{ genre: string; count: number; percentage: number }>;
  topArtists: Array<{ artist: string; count: number; percentage: number }>;
  hourlyActivity: Array<{
    hour: number;
    suggestions: number;
    votes: number;
    sessions: number;
  }>;
  dailyActivity: Array<{
    date: string;
    suggestions: number;
    votes: number;
    sessions: number;
  }>;
  deviceStats: { mobile: number; tablet: number; desktop: number };
  engagementRate: number;
  approvalRate: number;
  skipRate: number;
}

export interface DashboardSummary {
  totalPlays: number;
  totalSuggestions: number;
  totalVotes: number;
  activeUsers: number;
  averageRating: number;
  growthRate: number;
  peakHour: string;
  topGenre: string;
}

export interface PopularSong {
  id: string;
  title: string;
  artist: string;
  votes: number;
  plays: number;
  score: number;
  lastPlayed?: Date;
  thumbnail?: string;
}

export interface EngagementData {
  daily: Array<{ date: string; engagement: number }>;
  hourly: Array<{ hour: number; engagement: number }>;
  byGenre: Array<{ genre: string; engagement: number }>;
  averageSessionTime: number;
  returnRate: number;
  interactionRate: number;
}

class AnalyticsService {
  private analyticsRepository = AppDataSource.getRepository(AnalyticsDaily);
  private restaurantRepository = AppDataSource.getRepository(Restaurant);
  private suggestionRepository = AppDataSource.getRepository(Suggestion);
  private voteRepository = AppDataSource.getRepository(Vote);
  private sessionRepository = AppDataSource.getRepository(ClientSession);

  // Gerar métricas em tempo real
  async generateRealTimeMetrics(
    restaurantId: string,
    period: string = "7d"
  ): Promise<AnalyticsMetrics> {
    const endDate = new Date();
    const startDate = new Date();

    // Definir período
    switch (period) {
      case "1d":
        startDate.setDate(endDate.getDate() - 1);
        break;
      case "7d":
        startDate.setDate(endDate.getDate() - 7);
        break;
      case "30d":
        startDate.setDate(endDate.getDate() - 30);
        break;
      case "90d":
        startDate.setDate(endDate.getDate() - 90);
        break;
      default:
        startDate.setDate(endDate.getDate() - 7);
    }

    // Buscar dados das tabelas
    const [suggestions, votes, sessions] = await Promise.all([
      this.suggestionRepository.find({
        where: {
          restaurant: { id: restaurantId },
          createdAt: Between(startDate, endDate),
        },
        relations: ["votes"],
      }),
      this.voteRepository.find({
        where: {
          suggestion: {
            restaurant: { id: restaurantId },
          },
          createdAt: Between(startDate, endDate),
        },
        relations: ["suggestion"],
      }),
      this.sessionRepository.find({
        where: {
          restaurant: { id: restaurantId },
          createdAt: Between(startDate, endDate),
        },
      }),
    ]);

    // Calcular métricas básicas
    const totalSuggestions = suggestions.length;
    const approvedSuggestions = suggestions.filter(
      (s) => s.status === "approved"
    ).length;
    const rejectedSuggestions = suggestions.filter(
      (s) => s.status === "rejected"
    ).length;
    const totalVotes = votes.length;
    const upvotes = votes.filter((v) => v.voteType === "up").length;
    const downvotes = votes.filter((v) => v.voteType === "down").length;
    const uniqueSessions = sessions.length;

    // Calcular atividade por hora
    const hourlyActivity = Array.from({ length: 24 }, (_, hour) => {
      const hourSuggestions = suggestions.filter(
        (s) => new Date(s.createdAt).getHours() === hour
      ).length;
      const hourVotes = votes.filter(
        (v) => new Date(v.createdAt).getHours() === hour
      ).length;
      const hourSessions = sessions.filter(
        (s) => new Date(s.createdAt).getHours() === hour
      ).length;

      return {
        hour,
        suggestions: hourSuggestions,
        votes: hourVotes,
        sessions: hourSessions,
      };
    });

    // Calcular atividade diária
    const dailyActivity = [];
    for (
      let d = new Date(startDate);
      d <= endDate;
      d.setDate(d.getDate() + 1)
    ) {
      const dateStr = d.toISOString().split("T")[0];
      const daySuggestions = suggestions.filter(
        (s) => s.createdAt.toISOString().split("T")[0] === dateStr
      ).length;
      const dayVotes = votes.filter(
        (v) => v.createdAt.toISOString().split("T")[0] === dateStr
      ).length;
      const daySessions = sessions.filter(
        (s) => s.createdAt.toISOString().split("T")[0] === dateStr
      ).length;

      dailyActivity.push({
        date: dateStr,
        suggestions: daySuggestions,
        votes: dayVotes,
        sessions: daySessions,
      });
    }

    // Calcular gêneros populares
    const genreCount: Record<string, number> = {};
    suggestions.forEach((suggestion) => {
      if (suggestion.genre) {
        genreCount[suggestion.genre] = (genreCount[suggestion.genre] || 0) + 1;
      }
    });

    const topGenres = Object.entries(genreCount)
      .map(([genre, count]) => ({
        genre,
        count,
        percentage: Math.round((count / totalSuggestions) * 100),
      }))
      .sort((a, b) => b.count - a.count)
      .slice(0, 10);

    // Calcular artistas populares
    const artistCount: Record<string, number> = {};
    suggestions.forEach((suggestion) => {
      if (suggestion.artist) {
        artistCount[suggestion.artist] =
          (artistCount[suggestion.artist] || 0) + 1;
      }
    });

    const topArtists = Object.entries(artistCount)
      .map(([artist, count]) => ({
        artist,
        count,
        percentage: Math.round((count / totalSuggestions) * 100),
      }))
      .sort((a, b) => b.count - a.count)
      .slice(0, 10);

    // Calcular horário de pico
    const peakHour = hourlyActivity.reduce((peak, current) =>
      current.suggestions + current.votes > peak.suggestions + peak.votes
        ? current
        : peak
    ).hour;

    // Calcular duração média de sessão
    const totalSessionDuration = sessions.reduce((total, session) => {
      if (session.lastActivity && session.createdAt) {
        return (
          total + (session.lastActivity.getTime() - session.createdAt.getTime())
        );
      }
      return total;
    }, 0);
    const averageSessionDuration =
      sessions.length > 0
        ? Math.round(totalSessionDuration / sessions.length / 1000 / 60) // em minutos
        : 0;

    // Estatísticas de dispositivos (mock - seria implementado com user-agent)
    const deviceStats = {
      mobile: Math.round(uniqueSessions * 0.6),
      tablet: Math.round(uniqueSessions * 0.2),
      desktop: Math.round(uniqueSessions * 0.2),
    };

    // Calcular taxas
    const engagementRate =
      uniqueSessions > 0
        ? Math.round(((totalSuggestions + totalVotes) / uniqueSessions) * 100) /
          100
        : 0;
    const approvalRate =
      totalSuggestions > 0
        ? Math.round((approvedSuggestions / totalSuggestions) * 100)
        : 0;
    const skipRate = 0; // Seria calculado com dados de reprodução

    return {
      totalSuggestions,
      approvedSuggestions,
      rejectedSuggestions,
      totalVotes,
      upvotes,
      downvotes,
      uniqueSessions,
      totalSessions: uniqueSessions,
      averageSessionDuration,
      totalPlayTime: 0, // Seria calculado com dados de reprodução
      songsPlayed: approvedSuggestions,
      songsSkipped: 0,
      peakHour,
      topGenres,
      topArtists,
      hourlyActivity,
      dailyActivity,
      deviceStats,
      engagementRate,
      approvalRate,
      skipRate,
    };
  }

  // Gerar resumo do dashboard
  async generateDashboardSummary(
    restaurantId: string,
    period: string = "7d"
  ): Promise<DashboardSummary> {
    const metrics = await this.generateRealTimeMetrics(restaurantId, period);

    // Calcular crescimento (comparar com período anterior)
    const previousMetrics = await this.generateRealTimeMetrics(
      restaurantId,
      period
    );
    const growthRate = 0; // Seria calculado comparando períodos

    return {
      totalPlays: metrics.songsPlayed,
      totalSuggestions: metrics.totalSuggestions,
      totalVotes: metrics.totalVotes,
      activeUsers: metrics.uniqueSessions,
      averageRating:
        metrics.upvotes > 0
          ? (metrics.upvotes / (metrics.upvotes + metrics.downvotes)) * 5
          : 0,
      growthRate,
      peakHour: metrics.peakHour !== null ? `${metrics.peakHour}:00` : "N/A",
      topGenre: metrics.topGenres[0]?.genre || "N/A",
    };
  }

  // Obter músicas populares
  async getPopularSongs(
    restaurantId: string,
    limit: number = 10
  ): Promise<PopularSong[]> {
    const suggestions = await this.suggestionRepository.find({
      where: { restaurant: { id: restaurantId } },
      relations: ["votes"],
      order: { createdAt: "DESC" },
      take: 100, // Buscar mais para calcular score
    });

    const songsWithScore = suggestions.map((suggestion) => {
      const upvotes =
        suggestion.votes?.filter((v) => v.voteType === "up").length || 0;
      const downvotes =
        suggestion.votes?.filter((v) => v.voteType === "down").length || 0;
      const totalVotes = upvotes + downvotes;
      const score = upvotes - downvotes;

      return {
        id: suggestion.id,
        title: suggestion.title,
        artist: suggestion.artist,
        votes: totalVotes,
        plays: 0, // Seria obtido de dados de reprodução
        score,
        thumbnail: suggestion.thumbnail,
      };
    });

    return songsWithScore.sort((a, b) => b.score - a.score).slice(0, limit);
  }

  // Obter dados de engajamento
  async getEngagementData(
    restaurantId: string,
    period: string = "7d"
  ): Promise<EngagementData> {
    const metrics = await this.generateRealTimeMetrics(restaurantId, period);

    // Engajamento diário
    const daily = metrics.dailyActivity.map((day) => ({
      date: day.date,
      engagement: day.suggestions + day.votes,
    }));

    // Engajamento por hora
    const hourly = metrics.hourlyActivity.map((hour) => ({
      hour: hour.hour,
      engagement: hour.suggestions + hour.votes,
    }));

    // Engajamento por gênero
    const byGenre = metrics.topGenres.map((genre) => ({
      genre: genre.genre,
      engagement: genre.count,
    }));

    return {
      daily,
      hourly,
      byGenre,
      averageSessionTime: metrics.averageSessionDuration,
      returnRate: 0, // Seria calculado com dados de sessões recorrentes
      interactionRate: metrics.engagementRate,
    };
  }

  // Salvar dados diários (para ser executado via cron)
  async saveDailyAnalytics(
    restaurantId: string,
    date: Date = new Date()
  ): Promise<void> {
    const startOfDay = new Date(date);
    startOfDay.setHours(0, 0, 0, 0);
    const endOfDay = new Date(date);
    endOfDay.setHours(23, 59, 59, 999);

    // Verificar se já existe registro para o dia
    const existing = await this.analyticsRepository.findOne({
      where: {
        restaurant: { id: restaurantId },
        date: startOfDay,
      },
    });

    if (existing) {
      console.log(
        `Analytics já existem para ${restaurantId} em ${
          date.toISOString().split("T")[0]
        }`
      );
      return;
    }

    // Gerar métricas do dia
    const metrics = await this.generateRealTimeMetrics(restaurantId, "1d");

    // Criar registro
    const analytics = this.analyticsRepository.create({
      restaurant: { id: restaurantId },
      date: startOfDay,
      totalSuggestions: metrics.totalSuggestions,
      approvedSuggestions: metrics.approvedSuggestions,
      rejectedSuggestions: metrics.rejectedSuggestions,
      totalVotes: metrics.totalVotes,
      upvotes: metrics.upvotes,
      downvotes: metrics.downvotes,
      uniqueSessions: metrics.uniqueSessions,
      totalSessions: metrics.totalSessions,
      averageSessionDuration: metrics.averageSessionDuration,
      totalPlayTime: metrics.totalPlayTime,
      songsPlayed: metrics.songsPlayed,
      songsSkipped: metrics.songsSkipped,
      topGenres: metrics.topGenres,
      topArtists: metrics.topArtists,
      hourlyActivity: metrics.hourlyActivity,
      deviceStats: metrics.deviceStats,
    });

    await this.analyticsRepository.save(analytics);
    console.log(
      `Analytics salvos para ${restaurantId} em ${
        date.toISOString().split("T")[0]
      }`
    );
  }

  // Obter dados históricos
  async getHistoricalData(
    restaurantId: string,
    startDate: Date,
    endDate: Date
  ): Promise<AnalyticsDaily[]> {
    return this.analyticsRepository.find({
      where: {
        restaurant: { id: restaurantId },
        date: Between(startDate, endDate),
      },
      order: { date: "ASC" },
    });
  }

  // Exportar dados para relatório
  async exportData(
    restaurantId: string,
    format: "json" | "csv" = "json",
    period: string = "30d"
  ): Promise<any> {
    const metrics = await this.generateRealTimeMetrics(restaurantId, period);
    const popularSongs = await this.getPopularSongs(restaurantId, 20);
    const engagementData = await this.getEngagementData(restaurantId, period);

    const exportData = {
      restaurant: restaurantId,
      period,
      generatedAt: new Date().toISOString(),
      summary: {
        totalSuggestions: metrics.totalSuggestions,
        totalVotes: metrics.totalVotes,
        uniqueSessions: metrics.uniqueSessions,
        engagementRate: metrics.engagementRate,
        approvalRate: metrics.approvalRate,
      },
      metrics,
      popularSongs,
      engagementData,
    };

    if (format === "csv") {
      // Converter para CSV (implementação simplificada)
      return this.convertToCSV(exportData);
    }

    return exportData;
  }

  private convertToCSV(data: any): string {
    // Implementação básica de conversão para CSV
    const headers = Object.keys(data.summary);
    const values = Object.values(data.summary);

    return [headers.join(","), values.join(",")].join("\n");
  }

  // Limpar dados antigos (para ser executado via cron)
  async cleanupOldData(daysToKeep: number = 365): Promise<void> {
    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - daysToKeep);

    await this.analyticsRepository.delete({
      date: MoreThan(cutoffDate),
    });

    console.log(
      `Dados de analytics anteriores a ${
        cutoffDate.toISOString().split("T")[0]
      } foram removidos`
    );
  }

  // Análise detalhada das tracks para AdvancedPlaylistAnalytics
  async getTracksAnalytics(restaurantId: string): Promise<any[]> {
    try {
      const suggestions = await this.suggestionRepository
        .createQueryBuilder("suggestion")
        .leftJoinAndSelect("suggestion.votes", "vote")
        .where("suggestion.restaurantId = :restaurantId", { restaurantId })
        .getMany();

      const trackAnalytics = suggestions.map((suggestion) => {
        const votes = suggestion.votes || [];
        const upvotes = votes.filter((v) => v.voteType === "up").length;
        const downvotes = votes.filter((v) => v.voteType === "down").length;
        const totalVotes = upvotes + downvotes;

        const score = upvotes - downvotes;
        const negativeVoteRatio = totalVotes > 0 ? downvotes / totalVotes : 0;
        const positiveVoteRatio = totalVotes > 0 ? upvotes / totalVotes : 0;

        // Simular alguns dados adicionais
        const playCount = Math.floor(Math.random() * 50) + totalVotes;
        const skipCount = Math.floor(playCount * 0.2); // 20% skip rate
        const completionRate = 1 - skipCount / Math.max(playCount, 1);

        // Determinar performance baseada na taxa de rejeição
        let performance = "average";
        if (negativeVoteRatio <= 0.1) performance = "excellent";
        else if (negativeVoteRatio <= 0.25) performance = "good";
        else if (negativeVoteRatio <= 0.5) performance = "average";
        else if (negativeVoteRatio <= 0.75) performance = "poor";
        else performance = "terrible";

        // Determinar recomendação
        let recommendation = "monitor";
        if (performance === "excellent") recommendation = "keep";
        else if (performance === "good") recommendation = "keep";
        else if (performance === "average") recommendation = "monitor";
        else if (performance === "poor") recommendation = "remove";
        else recommendation = "blacklist";

        return {
          id: suggestion.id,
          title: suggestion.title,
          artist: suggestion.artist,
          youtubeVideoId: suggestion.youtubeVideoId,
          totalVotes,
          upvotes,
          downvotes,
          score,
          negativeVoteRatio,
          positiveVoteRatio,
          playCount,
          skipCount,
          completionRate,
          averagePlayDuration: Math.floor(Math.random() * 180) + 60, // 1-4 min
          lastPlayed: new Date(
            Date.now() - Math.random() * 7 * 24 * 60 * 60 * 1000
          ),
          suggestedCount: 1,
          performance,
          recommendation,
        };
      });

      return trackAnalytics;
    } catch (error) {
      console.error("Erro ao buscar analytics de tracks:", error);
      return [];
    }
  }

  // Saúde geral da playlist
  async getPlaylistHealth(restaurantId: string): Promise<any> {
    try {
      const trackAnalytics = await this.getTracksAnalytics(restaurantId);

      const totalTracks = trackAnalytics.length;
      const excellentTracks = trackAnalytics.filter(
        (t) => t.performance === "excellent"
      ).length;
      const goodTracks = trackAnalytics.filter(
        (t) => t.performance === "good"
      ).length;
      const averageTracks = trackAnalytics.filter(
        (t) => t.performance === "average"
      ).length;
      const poorTracks = trackAnalytics.filter(
        (t) => t.performance === "poor"
      ).length;
      const terribleTracks = trackAnalytics.filter(
        (t) => t.performance === "terrible"
      ).length;

      // Calcular score geral (peso: excellent=5, good=4, average=3, poor=2, terrible=1)
      const overallScore =
        totalTracks > 0
          ? Math.round(
              (excellentTracks * 5 +
                goodTracks * 4 +
                averageTracks * 3 +
                poorTracks * 2 +
                terribleTracks * 1) /
                totalTracks
            )
          : 0;

      // Determinar rating da saúde
      let healthRating = "needs_attention";
      if (overallScore >= 4.5) healthRating = "excellent";
      else if (overallScore >= 3.5) healthRating = "good";
      else if (overallScore >= 2.5) healthRating = "needs_attention";
      else healthRating = "critical";

      // Gerar recomendações
      const recommendations = [];
      const terriblePercentage =
        totalTracks > 0 ? (terribleTracks / totalTracks) * 100 : 0;
      const poorPercentage =
        totalTracks > 0 ? (poorTracks / totalTracks) * 100 : 0;

      if (terriblePercentage > 20) {
        recommendations.push(
          `${terribleTracks} músicas com performance terrível precisam ser removidas`
        );
      }
      if (poorPercentage > 30) {
        recommendations.push(
          `${poorTracks} músicas com performance ruim precisam de atenção`
        );
      }
      if (excellentTracks < totalTracks * 0.3) {
        recommendations.push(
          "Adicione mais músicas populares para melhorar o engajamento"
        );
      }
      if (totalTracks < 20) {
        recommendations.push(
          "Playlist pequena - considere adicionar mais variedade musical"
        );
      }

      return {
        totalTracks,
        excellentTracks,
        goodTracks,
        averageTracks,
        poorTracks,
        terribleTracks,
        overallScore,
        healthRating,
        recommendations,
      };
    } catch (error) {
      console.error("Erro ao calcular saúde da playlist:", error);
      return null;
    }
  }
}

export const analyticsService = new AnalyticsService();
export default AnalyticsService;
