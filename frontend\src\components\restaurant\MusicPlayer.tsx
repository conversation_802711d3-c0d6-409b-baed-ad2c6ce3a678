import React, { useState, useEffect, useRef, useCallback } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { useParams } from "react-router-dom";
import { apiService } from "@/services/api";
import {
  Play,
  Pause,
  Square,
  Volume2,
  VolumeX,
  Music,
  SkipBack,
  SkipForward,
  Shuffle,
  Repeat,
  Heart,
  Share2,
  Download,
  Settings,
  Maximize2,
  Minimize2,
  Search,
  RefreshCw,
  List,
  X,
  Youtube,
  ExternalLink,
  AlertCircle,
} from "lucide-react";
import { toast } from "react-hot-toast";
import YouTube from 'react-youtube';

// Interfaces locais para o player
interface PlaylistTrack {
  youtubeVideoId: string;
  title: string;
  artist: string;
  duration: number;
  thumbnailUrl: string;
  addedAt: string;
  position: number;
}

interface PlaylistData {
  id: string;
  name: string;
  description: string;
  thumbnail: string;
  tracks: PlaylistTrack[];
  totalDuration: number;
  videoCount: number;
  isActive: boolean;
}

// Componente principal
const MusicPlayer: React.FC = () => {
  const { restaurantId } = useParams<{ restaurantId: string }>();
  
  // Estado do player
  const [isPlaying, setIsPlaying] = useState(false);
  const [volume, setVolume] = useState(0.7);
  const [isMuted, setIsMuted] = useState(false);
  const [currentTime, setCurrentTime] = useState(0);
  const [isVisible, setIsVisible] = useState(true);
  const [isShuffled, setIsShuffled] = useState(false);
  const [repeatMode, setRepeatMode] = useState<"none" | "one" | "all">("none");
  const [isLiked, setIsLiked] = useState(false);
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [showPlaylist, setShowPlaylist] = useState(false);
  
  // YouTube e Playlist
  const [playlists, setPlaylists] = useState<PlaylistData[]>([]);
  const [currentPlaylist, setCurrentPlaylist] = useState<PlaylistData | null>(null);
  const [currentVideoIndex, setCurrentVideoIndex] = useState(0);
  const [searchQuery, setSearchQuery] = useState("");
  const [isSearching, setIsSearching] = useState(false);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [player, setPlayer] = useState<any>(null);
  
  // Referência para o player do YouTube
  const youtubePlayerRef = useRef<any>(null);
  
  // Obtém o vídeo atual
  const currentVideo = currentPlaylist?.tracks[currentVideoIndex] || {
    youtubeVideoId: "",
    title: "Nenhuma música selecionada",
    artist: "Selecione uma playlist",
    thumbnailUrl: "",
    duration: 0,
    addedAt: "",
    position: 0,
  };
  
  // Opções para o player do YouTube
  const youtubeOpts = {
    height: '0',
    width: '0',
    playerVars: {
      autoplay: isPlaying ? 1 : 0,
      controls: 0,
      disablekb: 1,
      enablejsapi: 1,
      iv_load_policy: 3,
      modestbranding: 1,
      rel: 0,
      showinfo: 0,
    },
  };

  // Função para buscar playlists do YouTube
  const searchYouTubePlaylists = useCallback(async (query: string) => {
    if (!query.trim()) return;
    
    setLoading(true);
    setError(null);
    
    try {
      // Em um ambiente real, esta requisição seria feita pelo servidor para proteger a chave da API
      const response = await fetch(
        `https://www.googleapis.com/youtube/v3/search?part=snippet&q=${encodeURIComponent(
          query
        )}&type=playlist&key=${YOUTUBE_API_KEY}&maxResults=5`
      );
      
      if (!response.ok) {
        throw new Error(`Erro ao buscar playlists: ${response.status}`);
      }
      
      const data = await response.json();
      
      // Mapear os resultados para nosso formato de playlist
      const foundPlaylists: Playlist[] = await Promise.all(
        data.items.map(async (item: any) => {
          const playlistId = item.id.playlistId;
          
          // Buscar os vídeos da playlist
          const videosResponse = await fetch(
            `https://www.googleapis.com/youtube/v3/playlistItems?part=snippet,contentDetails&maxResults=20&playlistId=${playlistId}&key=${YOUTUBE_API_KEY}`
          );
          
          if (!videosResponse.ok) {
            throw new Error(`Erro ao buscar vídeos da playlist: ${videosResponse.status}`);
          }
          
          const videosData = await videosResponse.json();
          
          // Mapear os vídeos
          const videos: YouTubeVideo[] = await Promise.all(
            videosData.items.map(async (videoItem: any) => {
              const videoId = videoItem.contentDetails.videoId;
              const title = videoItem.snippet.title;
              let artist = videoItem.snippet.videoOwnerChannelTitle || "Artista desconhecido";
              
              // Extrair artista do título (formato comum: "Artista - Título")
              if (title.includes(" - ")) {
                artist = title.split(" - ")[0].trim();
              }
              
              // Buscar duração do vídeo
              const videoDetailsResponse = await fetch(
                `https://www.googleapis.com/youtube/v3/videos?part=contentDetails&id=${videoId}&key=${YOUTUBE_API_KEY}`
              );
              
              if (!videoDetailsResponse.ok) {
                throw new Error(`Erro ao buscar detalhes do vídeo: ${videoDetailsResponse.status}`);
              }
              
              const videoDetails = await videoDetailsResponse.json();
              const durationISO = videoDetails.items[0]?.contentDetails?.duration || "PT0M0S";
              
              // Converter duração ISO 8601 para segundos
              const duration = convertISO8601ToSeconds(durationISO);
              
              return {
                id: videoId,
                title: title.includes(" - ") ? title.split(" - ")[1].trim() : title,
                artist,
                thumbnail: videoItem.snippet.thumbnails.high?.url || videoItem.snippet.thumbnails.default?.url,
                duration,
              };
            })
          );
          
          return {
            id: playlistId,
            title: item.snippet.title,
            description: item.snippet.description,
            videos,
            thumbnail: item.snippet.thumbnails.high?.url || item.snippet.thumbnails.default?.url,
          };
        })
      );
      
      setPlaylists(foundPlaylists);
      
      // Se encontrou playlists, selecionar a primeira
      if (foundPlaylists.length > 0 && !currentPlaylist) {
        setCurrentPlaylist(foundPlaylists[0]);
        setCurrentVideoIndex(0);
      }
      
      toast.success(`Encontradas ${foundPlaylists.length} playlists`);
    } catch (err: any) {
      console.error("Erro ao buscar playlists:", err);
      setError(err.message || "Erro ao buscar playlists do YouTube");
      toast.error("Erro ao buscar playlists");
      
      // Carregar playlists de exemplo em caso de erro
      loadExamplePlaylists();
    } finally {
      setLoading(false);
      setIsSearching(false);
    }
  }, [currentPlaylist]);

  // Converter duração ISO 8601 para segundos
  const convertISO8601ToSeconds = (duration: string): number => {
    const match = duration.match(/PT(\d+H)?(\d+M)?(\d+S)?/);
    
    const hours = (match?.[1] ? parseInt(match[1]) : 0);
    const minutes = (match?.[2] ? parseInt(match[2]) : 0);
    const seconds = (match?.[3] ? parseInt(match[3]) : 0);
    
    return hours * 3600 + minutes * 60 + seconds;
  };

  // Carregar playlists reais do restaurante
  const loadRealPlaylists = useCallback(async () => {
    if (!restaurantId) {
      toast.error("ID do restaurante não encontrado");
      return;
    }

    setLoading(true);
    setError(null);
    
    try {
      console.log(`🔄 Carregando playlists para restaurante: ${restaurantId}`);
      
      const response = await apiService.getPlaylists(restaurantId);
      const foundPlaylists = response.playlists || [];
      
      console.log(`✅ Encontradas ${foundPlaylists.length} playlists`);
      
      // Converter playlists para o formato esperado pelo player
      const formattedPlaylists: PlaylistData[] = foundPlaylists
        .filter((p: any) => p.isActive && p.tracks && p.tracks.length > 0)
        .map((p: any) => ({
          id: p.id,
          name: p.name,
          description: p.description || "",
          thumbnail: p.thumbnail || "",
          tracks: p.tracks.map((track: any) => ({
            youtubeVideoId: track.youtubeVideoId,
            title: track.title,
            artist: track.artist,
            duration: track.duration,
            thumbnailUrl: track.thumbnailUrl || "",
            addedAt: track.addedAt,
            position: track.position,
          })),
          totalDuration: p.totalDuration || 0,
          videoCount: p.videoCount || p.tracks.length,
          isActive: p.isActive,
        }));

      setPlaylists(formattedPlaylists);
      
      if (formattedPlaylists.length > 0 && !currentPlaylist) {
        setCurrentPlaylist(formattedPlaylists[0]);
        setCurrentVideoIndex(0);
      }
      
      toast.success(`Carregadas ${formattedPlaylists.length} playlists`);
    } catch (err: any) {
      console.error("❌ Erro ao carregar playlists:", err);
      setError(err.message || "Erro ao carregar playlists");
      toast.error("Erro ao carregar playlists do restaurante");
    } finally {
      setLoading(false);
    }
  }, [restaurantId, currentPlaylist]);

  // Carregar playlist específica com suas tracks
  const loadPlaylistTracks = useCallback(async (playlistId: string) => {
    setLoading(true);
    
    try {
      console.log(`🔄 Carregando tracks da playlist: ${playlistId}`);
      
      const playlistData = await apiService.getPlaylist(playlistId);
      
      if (playlistData.tracks && playlistData.tracks.length > 0) {
        const formattedPlaylist: PlaylistData = {
          id: playlistData.id,
          name: playlistData.name,
          description: playlistData.description || "",
          thumbnail: playlistData.thumbnail || "",
          tracks: playlistData.tracks.map((track: any) => ({
            youtubeVideoId: track.youtubeVideoId,
            title: track.title,
            artist: track.artist,
            duration: track.duration,
            thumbnailUrl: track.thumbnailUrl || "",
            addedAt: track.addedAt,
            position: track.position,
          })),
          totalDuration: playlistData.totalDuration || 0,
          videoCount: playlistData.videoCount || playlistData.tracks.length,
          isActive: playlistData.isActive,
        };

        setCurrentPlaylist(formattedPlaylist);
        setCurrentVideoIndex(0);
        
        toast.success(`Playlist "${playlistData.name}" carregada`);
      } else {
        toast.error("Playlist não possui músicas");
      }
    } catch (err: any) {
      console.error("❌ Erro ao carregar playlist:", err);
      toast.error("Erro ao carregar playlist");
    } finally {
      setLoading(false);
    }
  }, []);

  // Inicializar com playlists reais
  useEffect(() => {
    if (restaurantId) {
      loadRealPlaylists();
    }
  }, [restaurantId, loadRealPlaylists]);

  // Gerenciar eventos do player do YouTube
  const onPlayerReady = (event: any) => {
    setPlayer(event.target);
    // Definir volume inicial
    event.target.setVolume(volume * 100);
  };

  const onPlayerStateChange = (event: any) => {
    // -1 (não iniciado), 0 (terminado), 1 (reproduzindo), 2 (pausado), 3 (buffer), 5 (vídeo sugerido)
    switch (event.data) {
      case 0: // Terminado
        if (repeatMode === "one") {
          event.target.playVideo();
        } else if (repeatMode === "all" || currentVideoIndex < (currentPlaylist?.tracks.length || 0) - 1) {
          handleNextSong();
        } else {
          setIsPlaying(false);
          setCurrentTime(0);
        }
        break;
      case 1: // Reproduzindo
        setIsPlaying(true);
        break;
      case 2: // Pausado
        setIsPlaying(false);
        break;
    }
  };

  // Atualizar tempo atual
  useEffect(() => {
    if (isPlaying && player) {
      const interval = setInterval(() => {
        const currentTime = player.getCurrentTime();
        setCurrentTime(currentTime);
      }, 1000);
      return () => clearInterval(interval);
    }
  }, [isPlaying, player]);

  // Controles do player
  const togglePlay = () => {
    if (player) {
      if (isPlaying) {
        player.pauseVideo();
      } else {
        player.playVideo();
      }
      toast.success(isPlaying ? "Pausado" : "Reproduzindo");
    }
  };

  const stopPlayback = () => {
    if (player) {
      player.stopVideo();
      setIsPlaying(false);
      setCurrentTime(0);
      toast.success("Parado");
    }
  };

  const toggleMute = () => {
    if (player) {
      if (isMuted) {
        player.unMute();
        player.setVolume(volume * 100);
      } else {
        player.mute();
      }
      setIsMuted(!isMuted);
    }
  };

  const handleVolumeChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newVolume = parseFloat(e.target.value);
    setVolume(newVolume);
    
    if (player) {
      player.setVolume(newVolume * 100);
      if (newVolume === 0) {
        player.mute();
        setIsMuted(true);
      } else if (isMuted) {
        player.unMute();
        setIsMuted(false);
      }
    }
  };

  const formatTime = (time: number) => {
    const minutes = Math.floor(time / 60);
    const seconds = Math.floor(time % 60);
    return `${minutes}:${seconds.toString().padStart(2, "0")}`;
  };

  const handleSeek = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (player && currentVideo.duration > 0) {
      const seekTime = parseFloat(e.target.value) * currentVideo.duration;
      player.seekTo(seekTime);
      setCurrentTime(seekTime);
    }
  };

  const skipBack = () => {
    if (player) {
      const newTime = Math.max(0, currentTime - 10);
      player.seekTo(newTime);
      setCurrentTime(newTime);
      toast.success("Voltou 10 segundos");
    }
  };

  const skipForward = () => {
    if (player) {
      const newTime = Math.min(currentVideo.duration, currentTime + 10);
      player.seekTo(newTime);
      setCurrentTime(newTime);
      toast.success("Avançou 10 segundos");
    }
  };

  const toggleShuffle = () => {
    setIsShuffled(!isShuffled);
    toast.success(isShuffled ? "Aleatório desativado" : "Aleatório ativado");
  };

  const toggleRepeat = () => {
    const modes = ["none", "one", "all"] as const;
    const currentIndex = modes.indexOf(repeatMode);
    const nextMode = modes[(currentIndex + 1) % modes.length];
    setRepeatMode(nextMode);

    const messages = {
      none: "Repetição desativada",
      one: "Repetir uma música",
      all: "Repetir todas",
    };
    toast.success(messages[nextMode]);
  };

  const toggleLike = () => {
    setIsLiked(!isLiked);
    toast.success(isLiked ? "Removido dos favoritos" : "Adicionado aos favoritos");
  };

  const toggleFullscreen = () => {
    setIsFullscreen(!isFullscreen);
  };

  // Navegação entre músicas
  const handlePreviousSong = () => {
    if (!currentPlaylist) return;
    
    if (isShuffled) {
      const randomIndex = Math.floor(Math.random() * currentPlaylist.tracks.length);
      setCurrentVideoIndex(randomIndex);
    } else {
      setCurrentVideoIndex((prev) => (prev > 0 ? prev - 1 : currentPlaylist.tracks.length - 1));
    }
    
    // Reiniciar o player com a nova música
    setCurrentTime(0);
    if (isPlaying && player) {
      // Pequeno timeout para garantir que o estado foi atualizado
      setTimeout(() => player.playVideo(), 100);
    }
    
    toast.success("Música anterior");
  };

  const handleNextSong = () => {
    if (!currentPlaylist) return;
    
    if (isShuffled) {
      const randomIndex = Math.floor(Math.random() * currentPlaylist.tracks.length);
      setCurrentVideoIndex(randomIndex);
    } else {
      setCurrentVideoIndex((prev) => (prev < currentPlaylist.tracks.length - 1 ? prev + 1 : 0));
    }
    
    // Reiniciar o player com a nova música
    setCurrentTime(0);
    if (isPlaying && player) {
      // Pequeno timeout para garantir que o estado foi atualizado
      setTimeout(() => player.playVideo(), 100);
    }
    
    toast.success("Próxima música");
  };

  // Selecionar playlist
  const handleSelectPlaylist = (playlist: Playlist) => {
    setCurrentPlaylist(playlist);
    setCurrentVideoIndex(0);
    setCurrentTime(0);
    setShowPlaylist(false);
    
    // Iniciar reprodução da nova playlist
    if (player && isPlaying) {
      // Pequeno timeout para garantir que o estado foi atualizado
      setTimeout(() => player.playVideo(), 100);
    }
    
    toast.success(`Playlist "${playlist.title}" selecionada`);
  };

  // Selecionar música específica
  const handleSelectSong = (index: number) => {
    setCurrentVideoIndex(index);
    setCurrentTime(0);
    
    // Iniciar reprodução da nova música
    if (player) {
      if (isPlaying) {
        // Pequeno timeout para garantir que o estado foi atualizado
        setTimeout(() => player.playVideo(), 100);
      }
    }
    
    toast.success(`Música "${currentPlaylist?.tracks[index].title}" selecionada`);
  };

  // Componente não visível (simulação de erro)
  if (!isVisible) {
    return (
      <div className="text-center py-8">
        <AlertCircle className="w-12 h-12 text-red-500 mx-auto mb-4" />
        <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-4">Player não disponível</h3>
        <button
          onClick={() => setIsVisible(true)}
          className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
        >
          Recarregar Player
        </button>
      </div>
    );
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className={`space-y-6 ${
        isFullscreen ? "fixed inset-0 z-50 bg-black bg-opacity-95 p-8" : ""
      }`}
    >
      <div
        className={`bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 overflow-hidden ${
          isFullscreen ? "h-full flex flex-col" : ""
        }`}
      >
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700">
          <h2 className="text-2xl font-bold text-gray-900 dark:text-white flex items-center gap-2">
            <Music className="w-6 h-6 text-purple-600" />
            Player de Música
          </h2>
          <div className="flex items-center gap-2">
            <button
              onClick={() => setShowPlaylist(!showPlaylist)}
              className="p-2 text-gray-600 hover:text-gray-800 dark:text-gray-400 dark:hover:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-full transition-colors"
              aria-label="Mostrar playlist"
              title="Mostrar playlist"
            >
              <List className="w-5 h-5" />
            </button>
            <button
              onClick={toggleFullscreen}
              className="p-2 text-gray-600 hover:text-gray-800 dark:text-gray-400 dark:hover:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-full transition-colors"
              aria-label={isFullscreen ? "Sair da tela cheia" : "Tela cheia"}
              title={isFullscreen ? "Sair da tela cheia" : "Tela cheia"}
            >
              {isFullscreen ? (
                <Minimize2 className="w-5 h-5" />
              ) : (
                <Maximize2 className="w-5 h-5" />
              )}
            </button>
            <button
              onClick={() => setIsVisible(false)}
              className="text-red-600 hover:text-red-700 text-sm px-3 py-1 rounded-md border border-red-300 hover:bg-red-50 dark:hover:bg-red-900/20 transition-colors"
              aria-label="Simular erro"
            >
              Simular Erro
            </button>
          </div>
        </div>

        {/* Layout com dois painéis */}
        <div className={`flex ${isFullscreen ? "flex-1" : ""}`}>
          {/* Painel principal do player */}
          <div className={`${showPlaylist ? "w-2/3" : "w-full"} ${isFullscreen ? "flex flex-col" : ""}`}>
            {/* Main Player Area */}
            <div className={`${isFullscreen ? "flex-1 flex flex-col" : "p-6"}`}>
              {/* YouTube Player (invisível) */}
              <div className="hidden">
                <YouTube
                  videoId={currentVideo.id}
                  opts={youtubeOpts}
                  onReady={onPlayerReady}
                  onStateChange={onPlayerStateChange}
                  ref={youtubePlayerRef}
                />
              </div>

              {/* Album Art & Song Info */}
              <div
                className={`bg-gradient-to-br from-purple-600 via-blue-600 to-indigo-700 rounded-xl p-8 text-white mb-6 ${
                  isFullscreen ? "flex-1 flex items-center justify-center" : ""
                }`}
              >
                <div
                  className={`${
                    isFullscreen
                      ? "text-center max-w-2xl"
                      : "flex items-center space-x-6"
                  }`}
                >
                  <div
                    className={`${
                      isFullscreen ? "w-80 h-80 mx-auto mb-8" : "w-24 h-24"
                    } bg-white/20 rounded-xl flex items-center justify-center backdrop-blur-sm border border-white/30 shadow-2xl overflow-hidden`}
                  >
                    <img
                      src={currentVideo.thumbnail}
                      alt={currentVideo.title}
                      className="w-full h-full object-cover"
                      onError={(e) => {
                        e.currentTarget.style.display = "none";
                        e.currentTarget.nextElementSibling?.classList.remove(
                          "hidden"
                        );
                      }}
                    />
                    <Music
                      className={`${
                        isFullscreen ? "w-32 h-32" : "w-12 h-12"
                      } hidden text-white/80`}
                    />
                  </div>
                  <div className={`flex-1 ${isFullscreen ? "text-center" : ""}`}>
                    <h3
                      className={`${
                        isFullscreen ? "text-4xl mb-2" : "text-xl"
                      } font-bold`}
                    >
                      {currentVideo.title}
                    </h3>
                    <p
                      className={`${
                        isFullscreen
                          ? "text-2xl text-purple-200 mb-1"
                          : "text-lg text-blue-200"
                      }`}
                    >
                      {currentVideo.artist}
                    </p>
                    <p
                      className={`${
                        isFullscreen
                          ? "text-lg text-purple-300"
                          : "text-sm text-blue-300"
                      }`}
                    >
                      {currentPlaylist?.title || "Nenhuma playlist selecionada"}
                    </p>
                    
                    {/* YouTube badge */}
                    <div className={`flex items-center justify-center gap-1 mt-2 ${isFullscreen ? "mt-4" : ""}`}>
                      <Youtube className="w-4 h-4 text-red-400" />
                      <span className="text-xs text-white/70">YouTube</span>
                    </div>
                  </div>
                </div>
              </div>

              {/* Progress Bar */}
              <div className="mb-6 px-2">
                <div className="flex items-center justify-between text-sm text-gray-600 dark:text-gray-400 mb-2">
                  <span>{formatTime(currentTime)}</span>
                  <span>{formatTime(currentVideo.duration)}</span>
                </div>
                <input
                  type="range"
                  min="0"
                  max="1"
                  step="0.001"
                  value={currentVideo.duration > 0 ? currentTime / currentVideo.duration : 0}
                  onChange={handleSeek}
                  className="w-full h-2 bg-gray-200 dark:bg-gray-700 rounded-full appearance-none cursor-pointer"
                  style={{
                    background: `linear-gradient(to right, #8B5CF6 0%, #3B82F6 ${
                      (currentTime / Math.max(1, currentVideo.duration)) * 100
                    }%, #E5E7EB ${
                      (currentTime / Math.max(1, currentVideo.duration)) * 100
                    }%, #E5E7EB 100%)`,
                  }}
                />
              </div>

              {/* Main Controls */}
              <div className="flex items-center justify-center space-x-6 mb-6">
                <button
                  onClick={toggleShuffle}
                  className={`p-3 rounded-full transition-all ${
                    isShuffled
                      ? "bg-purple-600 text-white shadow-lg"
                      : "bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-400 hover:bg-gray-200 dark:hover:bg-gray-600"
                  }`}
                  title={isShuffled ? "Desativar modo aleatório" : "Ativar modo aleatório"}
                >
                  <Shuffle className="w-5 h-5" />
                </button>

                <button
                  onClick={handlePreviousSong}
                  className="p-3 bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-400 hover:bg-gray-200 dark:hover:bg-gray-600 rounded-full transition-colors"
                  title="Música anterior"
                >
                  <SkipBack className="w-6 h-6" />
                </button>

                <button
                  onClick={togglePlay}
                  className="bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700 text-white rounded-full p-4 transition-all transform hover:scale-105 shadow-lg"
                  title={isPlaying ? "Pausar" : "Reproduzir"}
                >
                  {isPlaying ? (
                    <Pause className="w-8 h-8" />
                  ) : (
                    <Play className="w-8 h-8 ml-1" />
                  )}
                </button>

                <button
                  onClick={handleNextSong}
                  className="p-3 bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-400 hover:bg-gray-200 dark:hover:bg-gray-600 rounded-full transition-colors"
                  title="Próxima música"
                >
                  <SkipForward className="w-6 h-6" />
                </button>

                <button
                  onClick={toggleRepeat}
                  className={`p-3 rounded-full transition-all relative ${
                    repeatMode !== "none"
                      ? "bg-purple-600 text-white shadow-lg"
                      : "bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-400 hover:bg-gray-200 dark:hover:bg-gray-600"
                  }`}
                  title={
                    repeatMode === "none" 
                      ? "Ativar repetição" 
                      : repeatMode === "one" 
                      ? "Repetir todas as músicas" 
                      : "Desativar repetição"
                  }
                >
                  <Repeat className="w-5 h-5" />
                  {repeatMode === "one" && (
                    <span className="absolute -top-1 -right-1 w-4 h-4 bg-purple-400 rounded-full text-xs flex items-center justify-center text-white">
                      1
                    </span>
                  )}
                </button>
              </div>

              {/* Secondary Controls */}
              <div className="flex items-center justify-between px-2 mb-6">
                <div className="flex items-center space-x-3">
                  <button
                    onClick={toggleLike}
                    className={`p-2 rounded-full transition-all ${
                      isLiked
                        ? "text-red-500 bg-red-50 dark:bg-red-900/20"
                        : "text-gray-400 hover:text-red-400 hover:bg-gray-100 dark:hover:bg-gray-700"
                    }`}
                    title={isLiked ? "Remover dos favoritos" : "Adicionar aos favoritos"}
                  >
                    <Heart className={`w-5 h-5 ${isLiked ? "fill-current" : ""}`} />
                  </button>
                  <button 
                    className="p-2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-full transition-colors"
                    title="Compartilhar"
                  >
                    <Share2 className="w-5 h-5" />
                  </button>
                  <a 
                    href={`https://www.youtube.com/watch?v=${currentVideo.id}`}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="p-2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-full transition-colors"
                    title="Abrir no YouTube"
                  >
                    <ExternalLink className="w-5 h-5" />
                  </a>
                </div>

                {/* Volume Control */}
                <div className="flex items-center space-x-3">
                  <button
                    onClick={toggleMute}
                    className="p-2 text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-full transition-colors"
                    title={isMuted ? "Ativar som" : "Desativar som"}
                  >
                    {isMuted || volume === 0 ? (
                      <VolumeX className="w-5 h-5" />
                    ) : (
                      <Volume2 className="w-5 h-5" />
                    )}
                  </button>
                  <input
                    type="range"
                    min="0"
                    max="1"
                    step="0.01"
                    value={isMuted ? 0 : volume}
                    onChange={handleVolumeChange}
                    className="w-24 h-2 bg-gray-200 dark:bg-gray-700 rounded-lg appearance-none cursor-pointer"
                    style={{
                      background: `linear-gradient(to right, #8B5CF6 0%, #3B82F6 ${
                        (isMuted ? 0 : volume) * 100
                      }%, #E5E7EB ${(isMuted ? 0 : volume) * 100}%, #E5E7EB 100%)`,
                    }}
                    title={`Volume: ${Math.round((isMuted ? 0 : volume) * 100)}%`}
                  />
                  <span className="text-sm text-gray-600 dark:text-gray-400 w-8 text-right">
                    {Math.round((isMuted ? 0 : volume) * 100)}%
                  </span>
                </div>
              </div>

              {/* Status */}
              <div className="bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg p-4 mx-2">
                <h3 className="text-sm font-medium text-green-800 dark:text-green-200 mb-2">
                  Status do Player
                </h3>
                <div className="text-sm text-green-700 dark:text-green-300 space-y-1">
                  <p>Estado: {isPlaying ? "▶️ Reproduzindo" : "⏸️ Pausado"}</p>
                  <p>
                    Volume: {Math.round(volume * 100)}% {isMuted && "(Mudo)"}
                  </p>
                  <p>Tempo: {formatTime(currentTime)} / {formatTime(currentVideo.duration)}</p>
                  <p>
                    Modo: {isShuffled ? "🔀 Aleatório" : "📋 Sequencial"} |{" "}
                    {repeatMode === "none"
                      ? "🔄 Sem repetição"
                      : repeatMode === "one"
                      ? "🔂 Repetir uma"
                      : "🔁 Repetir todas"}
                  </p>
                  <p>Playlist: {currentPlaylist?.title || "Nenhuma selecionada"} ({currentVideoIndex + 1}/{currentPlaylist?.videos.length || 0})</p>
                </div>
              </div>

              {/* Search Bar - Always visible in small screen, only when not showing playlist in larger screens */}
              <div className={`mt-6 p-2 ${showPlaylist && !isFullscreen ? "hidden md:block" : ""}`}>
                <div className="relative">
                  <input
                    type="text"
                    placeholder="Buscar playlist no YouTube..."
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    onKeyDown={(e) => {
                      if (e.key === "Enter") {
                        setIsSearching(true);
                        searchYouTubePlaylists(searchQuery);
                      }
                    }}
                    className="w-full px-4 py-2 pl-10 pr-12 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
                  />
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" />
                  <button
                    onClick={() => {
                      setIsSearching(true);
                      searchYouTubePlaylists(searchQuery);
                    }}
                    disabled={isSearching || !searchQuery.trim()}
                    className="absolute right-2 top-1/2 transform -translate-y-1/2 px-2 py-1 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    {isSearching ? (
                      <RefreshCw className="w-4 h-4 animate-spin" />
                    ) : (
                      "Buscar"
                    )}
                  </button>
                </div>
                
                {error && (
                  <div className="mt-2 text-sm text-red-600 dark:text-red-400 bg-red-50 dark:bg-red-900/20 p-2 rounded">
                    <div className="flex items-center gap-1">
                      <AlertCircle className="w-4 h-4" />
                      <span>{error}</span>
                    </div>
                    <button 
                      onClick={loadExamplePlaylists}
                      className="mt-1 text-blue-600 dark:text-blue-400 hover:underline"
                    >
                      Carregar playlists de exemplo
                    </button>
                  </div>
                )}
              </div>
            </div>
          </div>

          {/* Painel da playlist */}
          <AnimatePresence>
            {showPlaylist && (
              <motion.div 
                initial={{ width: 0, opacity: 0 }}
                animate={{ width: "33.333333%", opacity: 1 }}
                exit={{ width: 0, opacity: 0 }}
                transition={{ duration: 0.3 }}
                className="border-l border-gray-200 dark:border-gray-700 h-full overflow-hidden"
              >
                <div className="p-4 border-b border-gray-200 dark:border-gray-700 flex items-center justify-between">
                  <h3 className="font-medium text-gray-900 dark:text-white">Playlists</h3>
                  <button
                    onClick={() => setShowPlaylist(false)}
                    className="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
                  >
                    <X className="w-5 h-5" />
                  </button>
                </div>
                
                <div className="h-full overflow-y-auto" style={{ maxHeight: 'calc(100vh - 250px)' }}>
                  {/* Lista de playlists */}
                  <div className="space-y-2 p-2">
                    {playlists.map((playlist) => (
                      <div 
                        key={playlist.id}
                        onClick={() => handleSelectPlaylist(playlist)}
                        className={`flex items-center p-2 rounded-lg cursor-pointer ${
                          currentPlaylist?.id === playlist.id 
                            ? "bg-purple-100 dark:bg-purple-900/30" 
                            : "hover:bg-gray-100 dark:hover:bg-gray-700"
                        }`}
                      >
                        <div className="w-12 h-12 rounded overflow-hidden mr-3 flex-shrink-0">
                          <img 
                            src={playlist.thumbnail} 
                            alt={playlist.title}
                            className="w-full h-full object-cover"
                            onError={(e) => {
                              e.currentTarget.src = "https://via.placeholder.com/300x300/8B5CF6/FFFFFF?text=♪";
                            }}
                          />
                        </div>
                        <div className="flex-1 min-w-0">
                          <h4 className={`text-sm font-medium truncate ${
                            currentPlaylist?.id === playlist.id 
                              ? "text-purple-800 dark:text-purple-300" 
                              : "text-gray-900 dark:text-white"
                          }`}>
                            {playlist.title}
                          </h4>
                          <p className="text-xs text-gray-500 dark:text-gray-400 truncate">
                            {playlist.videos.length} músicas
                          </p>
                        </div>
                      </div>
                    ))}
                    
                    {playlists.length === 0 && !loading && (
                      <div className="text-center py-8 text-gray-500 dark:text-gray-400">
                        <Music className="w-12 h-12 mx-auto mb-2 opacity-50" />
                        <p>Nenhuma playlist encontrada</p>
                        <button 
                          onClick={loadExamplePlaylists}
                          className="mt-2 text-blue-600 dark:text-blue-400 hover:underline text-sm"
                        >
                          Carregar playlists de exemplo
                        </button>
                      </div>
                    )}
                    
                    {loading && (
                      <div className="text-center py-8">
                        <RefreshCw className="w-8 h-8 animate-spin mx-auto mb-2 text-purple-600" />
                        <p className="text-gray-600 dark:text-gray-400">Buscando playlists...</p>
                      </div>
                    )}
                  </div>
                  
                  {/* Músicas da playlist atual */}
                  {currentPlaylist && (
                    <div className="mt-4 p-2">
                      <h3 className="font-medium text-gray-900 dark:text-white mb-2 px-2">
                        Músicas de {currentPlaylist.title}
                      </h3>
                      <div className="space-y-1">
                        {currentPlaylist.videos.map((video, index) => (
                          <div 
                            key={video.id}
                            onClick={() => handleSelectSong(index)}
                            className={`flex items-center p-2 rounded-lg cursor-pointer ${
                              currentVideoIndex === index 
                                ? "bg-blue-100 dark:bg-blue-900/30" 
                                : "hover:bg-gray-100 dark:hover:bg-gray-700"
                            }`}
                          >
                            <div className="mr-3 text-xs text-gray-500 dark:text-gray-400 w-5 text-center">
                              {index + 1}
                            </div>
                            <div className="w-8 h-8 rounded overflow-hidden mr-2 flex-shrink-0">
                              <img 
                                src={video.thumbnail} 
                                alt={video.title}
                                className="w-full h-full object-cover"
                                onError={(e) => {
                                  e.currentTarget.src = "https://via.placeholder.com/300x300/8B5CF6/FFFFFF?text=♪";
                                }}
                              />
                            </div>
                            <div className="flex-1 min-w-0">
                              <h4 className={`text-xs font-medium truncate ${
                                currentVideoIndex === index 
                                  ? "text-blue-800 dark:text-blue-300" 
                                  : "text-gray-900 dark:text-white"
                              }`}>
                                {video.title}
                              </h4>
                              <p className="text-xs text-gray-500 dark:text-gray-400 truncate">
                                {video.artist}
                              </p>
                            </div>
                            <div className="text-xs text-gray-500 dark:text-gray-400">
                              {formatTime(video.duration)}
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}
                </div>
              </motion.div>
            )}
          </AnimatePresence>
        </div>
      </div>
    </motion.div>
  );
};

export default MusicPlayer;