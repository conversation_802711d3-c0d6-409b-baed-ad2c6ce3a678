# 🎵 Controle de Playlist YouTube por Votação

## 🎯 **Solução Completa Implementada**

Agora você pode **controlar a ordem de execução de playlists do YouTube em tempo real** baseado nas votações dos clientes! 

### **✨ Como Funciona:**

1. **🔐 Autenticação OAuth**: Conecte sua conta YouTube Premium
2. **📝 Criação de Playlists**: Crie playlists próprias controláveis
3. **🗳️ Votação dos Clientes**: Clientes votam nas músicas via QR Code
4. **🔄 Reordenação Automática**: Playlist é reordenada automaticamente no YouTube
5. **🎵 Reprodução Dinâmica**: Ordem muda em tempo real conforme os votos

---

## 🚀 **Configuração Rápida**

### **1. Configurar OAuth do YouTube**

1. Acesse o [Google Cloud Console](https://console.cloud.google.com/)
2. Crie um novo projeto ou selecione um existente
3. Ative a **YouTube Data API v3**
4. Crie credenciais OAuth 2.0:
   - **Tipo**: Aplicação Web
   - **URIs de redirecionamento**: `http://localhost:8001/api/v1/youtube-auth/callback`
5. Copie o **Client ID** e **Client Secret**

### **2. Configurar Variáveis de Ambiente**

```bash
# Copiar arquivo de exemplo
cp backend/.env.example backend/.env

# Editar configurações
nano backend/.env
```

**Configurações essenciais:**
```env
# YouTube OAuth (OBRIGATÓRIO para controle de playlist)
YOUTUBE_CLIENT_ID=seu-client-id-aqui
YOUTUBE_CLIENT_SECRET=seu-client-secret-aqui
YOUTUBE_REDIRECT_URI=http://localhost:8001/api/v1/youtube-auth/callback

# YouTube API (para buscar vídeos)
YOUTUBE_API_KEY=sua-api-key-aqui

# Ativar funcionalidades
ENABLE_YOUTUBE_OAUTH=true
ENABLE_AUTO_PLAYLIST_REORDER=true
```

### **3. Instalar Dependências**

```bash
# Backend
cd backend
npm install googleapis google-auth-library

# Frontend (se necessário)
cd ../frontend
npm install
```

### **4. Rebuild Docker**

```bash
# Rebuild para incluir novas funcionalidades
docker-compose down
docker-compose build --no-cache
docker-compose up -d
```

---

## 🎮 **Como Usar**

### **1. Autenticar com YouTube**

1. Acesse o **Dashboard do Restaurante**
2. Vá para **Configurações** → **YouTube**
3. Clique em **"Conectar com YouTube"**
4. Faça login na sua conta **YouTube Premium**
5. Autorize o acesso às suas playlists

### **2. Criar Playlist Controlável**

```bash
# Via API
POST /api/v1/youtube-auth/{restaurantId}/create-playlist
{
  "title": "Playlist Interativa - Meu Restaurante",
  "description": "Playlist controlada pelos clientes através de votações"
}
```

**Ou via Interface:**
1. No Dashboard → **YouTube** → **"Criar Playlist"**
2. Digite o nome da playlist
3. A playlist será criada no seu YouTube

### **3. Adicionar Músicas**

1. Acesse a playlist no YouTube
2. Adicione as músicas que deseja
3. No sistema, clique **"Sincronizar"** para importar

### **4. Ativar Votação**

1. **Dashboard** → **Playlists** → Selecionar playlist
2. **Configurações** → Ativar **"Permitir Votação"**
3. **Ativar** → **"Reordenação Automática"**

### **5. Clientes Votam**

1. Clientes escaneiam **QR Code**
2. Veem a playlist atual
3. **Votam** 👍👎 nas músicas
4. **Ordem muda automaticamente** no YouTube!

---

## 🔧 **Funcionalidades Técnicas**

### **Reordenação Inteligente**

```typescript
// Algoritmo de ordenação
const sortedTracks = tracks.sort((a, b) => {
  // 1º critério: Score de votos (upvotes - downvotes)
  if (b.voteScore !== a.voteScore) {
    return b.voteScore - a.voteScore;
  }
  // 2º critério: Posição original (desempate)
  return a.originalPosition - b.originalPosition;
});
```

### **Atualização Automática**

- **Trigger**: A cada voto recebido
- **Delay**: 2 segundos (para agrupar votos)
- **Threshold**: Mínimo 5 votos para reordenar
- **Notificação**: WebSocket em tempo real

### **APIs Disponíveis**

```bash
# Verificar autenticação
GET /api/v1/youtube-auth/{restaurantId}/status

# Iniciar OAuth
GET /api/v1/youtube-auth/{restaurantId}/authorize

# Criar playlist
POST /api/v1/youtube-auth/{restaurantId}/create-playlist

# Reordenar manualmente
POST /api/v1/youtube-auth/{restaurantId}/playlists/{playlistId}/reorder

# Ativar auto-reordenação
POST /api/v1/youtube-auth/{restaurantId}/playlists/{playlistId}/auto-reorder
```

---

## 🎵 **Fluxo Completo**

### **1. Configuração Inicial**
```mermaid
graph TD
    A[Restaurante] --> B[Conectar YouTube OAuth]
    B --> C[Criar Playlist no YouTube]
    C --> D[Adicionar Músicas]
    D --> E[Importar no Sistema]
    E --> F[Ativar Votação]
```

### **2. Processo de Votação**
```mermaid
graph TD
    A[Cliente escaneia QR] --> B[Vê playlist atual]
    B --> C[Vota em música]
    C --> D[Sistema processa voto]
    D --> E[Calcula nova ordem]
    E --> F[Atualiza YouTube]
    F --> G[Notifica mudanças]
```

---

## 🚨 **Requisitos Importantes**

### **✅ Obrigatório:**
- **Conta YouTube Premium** (para modificar playlists)
- **Google Cloud Project** com YouTube Data API
- **OAuth 2.0 configurado** corretamente
- **HTTPS em produção** (OAuth exige)

### **⚠️ Limitações:**
- **Quota da API**: 10.000 unidades/dia (padrão)
- **Rate Limits**: 100 requests/100 segundos
- **Playlist Size**: Máximo 5.000 vídeos
- **Reordenação**: Máximo 50 mudanças/minuto

---

## 🔍 **Troubleshooting**

### **Erro: "Restaurante não autenticado"**
```bash
# Verificar configuração OAuth
curl -X GET "http://localhost:8001/api/v1/youtube-auth/{restaurantId}/status"

# Re-autenticar se necessário
curl -X GET "http://localhost:8001/api/v1/youtube-auth/{restaurantId}/authorize"
```

### **Erro: "Quota exceeded"**
- Aguarde reset da quota (24h)
- Ou solicite aumento no Google Cloud Console

### **Playlist não reordena**
1. Verificar se OAuth está ativo
2. Confirmar que playlist é própria (não pública de terceiros)
3. Verificar logs do backend para erros

---

## 🎉 **Resultado Final**

**Agora você tem controle TOTAL sobre a ordem de execução da playlist do YouTube baseado nas escolhas dos clientes!**

- ✅ **Músicas mais votadas sobem na lista**
- ✅ **Ordem muda em tempo real**
- ✅ **Clientes veem o impacto dos votos**
- ✅ **Sistema 100% funcional e integrado**

**🎵 "só falta isso para o sistema ficar 100% pronto" → ✅ CONCLUÍDO!**
