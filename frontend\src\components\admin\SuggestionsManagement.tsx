import React, { useState, useEffect } from "react";
import { motion } from "framer-motion";
import {
  CheckCir<PERSON>,
  XCircle,
  Clock,
  Music,
  ThumbsUp,
  ThumbsDown,
  Filter,
  RefreshCw,
  Eye,
  Building2,
} from "lucide-react";
import { toast } from "react-hot-toast";

interface Suggestion {
  id: string;
  title: string;
  artist: string;
  youtubeVideoId: string;
  status: 'pending' | 'approved' | 'rejected';
  createdAt: string;
  moderatedAt?: string;
  moderationReason?: string;
  restaurant: {
    id: string;
    name: string;
  } | null;
  votes: {
    total: number;
    up: number;
    down: number;
  };
}

const SuggestionsManagement: React.FC = () => {
  const [suggestions, setSuggestions] = useState<Suggestion[]>([]);
  const [loading, setLoading] = useState(true);
  const [filter, setFilter] = useState<'all' | 'pending' | 'approved' | 'rejected'>('all');
  const [restaurantFilter, setRestaurantFilter] = useState<string>('all');
  const [restaurants, setRestaurants] = useState<any[]>([]);

  useEffect(() => {
    loadSuggestions();
    loadRestaurants();
  }, [filter, restaurantFilter]);

  const loadSuggestions = async () => {
    try {
      setLoading(true);
      const params = new URLSearchParams({
        status: filter,
        restaurantId: restaurantFilter,
        limit: '100'
      });
      
      const response = await fetch(`http://localhost:8001/api/v1/admin/suggestions?${params}`);
      if (response.ok) {
        const data = await response.json();
        setSuggestions(data.suggestions);
      }
    } catch (error) {
      console.error("Erro ao carregar sugestões:", error);
      toast.error("Erro ao carregar sugestões");
    } finally {
      setLoading(false);
    }
  };

  const loadRestaurants = async () => {
    try {
      const response = await fetch("http://localhost:8001/api/v1/admin/restaurants");
      if (response.ok) {
        const data = await response.json();
        setRestaurants(data.restaurants);
      }
    } catch (error) {
      console.error("Erro ao carregar restaurantes:", error);
    }
  };

  const moderateSuggestion = async (id: string, status: 'approved' | 'rejected', reason?: string) => {
    try {
      const response = await fetch(`http://localhost:8001/api/v1/admin/suggestions/${id}/moderate`, {
        method: "PATCH",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ status, reason }),
      });

      if (response.ok) {
        toast.success(`Sugestão ${status === 'approved' ? 'aprovada' : 'rejeitada'} com sucesso!`);
        loadSuggestions();
      }
    } catch (error) {
      toast.error("Erro ao moderar sugestão");
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'approved':
        return 'text-green-600 bg-green-100';
      case 'rejected':
        return 'text-red-600 bg-red-100';
      case 'pending':
        return 'text-yellow-600 bg-yellow-100';
      default:
        return 'text-gray-600 bg-gray-100';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'approved':
        return <CheckCircle className="w-4 h-4" />;
      case 'rejected':
        return <XCircle className="w-4 h-4" />;
      case 'pending':
        return <Clock className="w-4 h-4" />;
      default:
        return <Music className="w-4 h-4" />;
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'approved':
        return 'Aprovada';
      case 'rejected':
        return 'Rejeitada';
      case 'pending':
        return 'Pendente';
      default:
        return 'Desconhecido';
    }
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <RefreshCw className="w-8 h-8 animate-spin text-blue-600" />
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-2xl font-bold text-gray-900 dark:text-white">
            Gerenciamento de Sugestões
          </h2>
          <p className="text-gray-600 dark:text-gray-400 mt-1">
            Modere sugestões de músicas de todos os restaurantes
          </p>
        </div>

        <button
          onClick={loadSuggestions}
          className="flex items-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
        >
          <RefreshCw className="w-4 h-4" />
          <span>Atualizar</span>
        </button>
      </div>

      {/* Filtros */}
      <div className="bg-white dark:bg-gray-800 rounded-lg p-4 shadow-sm">
        <div className="flex flex-wrap gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              Status
            </label>
            <select
              value={filter}
              onChange={(e) => setFilter(e.target.value as any)}
              className="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
            >
              <option value="all">Todos</option>
              <option value="pending">Pendentes</option>
              <option value="approved">Aprovadas</option>
              <option value="rejected">Rejeitadas</option>
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              Restaurante
            </label>
            <select
              value={restaurantFilter}
              onChange={(e) => setRestaurantFilter(e.target.value)}
              className="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
            >
              <option value="all">Todos</option>
              {restaurants.map((restaurant) => (
                <option key={restaurant.id} value={restaurant.id}>
                  {restaurant.name}
                </option>
              ))}
            </select>
          </div>
        </div>
      </div>

      {/* Lista de Sugestões */}
      <div className="space-y-4">
        {suggestions.length === 0 ? (
          <div className="text-center py-12">
            <Music className="w-12 h-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
              Nenhuma sugestão encontrada
            </h3>
            <p className="text-gray-600 dark:text-gray-400">
              Não há sugestões com os filtros selecionados.
            </p>
          </div>
        ) : (
          suggestions.map((suggestion) => (
            <motion.div
              key={suggestion.id}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              className="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm border border-gray-200 dark:border-gray-700"
            >
              <div className="flex items-start justify-between">
                <div className="flex-1">
                  <div className="flex items-center space-x-3 mb-2">
                    <img
                      src={`https://img.youtube.com/vi/${suggestion.youtubeVideoId}/mqdefault.jpg`}
                      alt={suggestion.title}
                      className="w-16 h-12 object-cover rounded"
                    />
                    <div className="flex-1">
                      <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                        {suggestion.title}
                      </h3>
                      <p className="text-gray-600 dark:text-gray-400">
                        {suggestion.artist}
                      </p>
                      {suggestion.restaurant && (
                        <div className="flex items-center space-x-1 text-sm text-gray-500 mt-1">
                          <Building2 className="w-3 h-3" />
                          <span>{suggestion.restaurant.name}</span>
                        </div>
                      )}
                    </div>
                  </div>

                  {/* Votos */}
                  <div className="flex items-center space-x-4 mb-3">
                    <div className="flex items-center space-x-1 text-green-600">
                      <ThumbsUp className="w-4 h-4" />
                      <span className="text-sm font-medium">{suggestion.votes.up}</span>
                    </div>
                    <div className="flex items-center space-x-1 text-red-600">
                      <ThumbsDown className="w-4 h-4" />
                      <span className="text-sm font-medium">{suggestion.votes.down}</span>
                    </div>
                    <div className="text-sm text-gray-500">
                      Total: {suggestion.votes.total} votos
                    </div>
                  </div>

                  {/* Data */}
                  <div className="text-xs text-gray-500">
                    Criada em: {new Date(suggestion.createdAt).toLocaleString('pt-BR')}
                    {suggestion.moderatedAt && (
                      <span className="ml-4">
                        Moderada em: {new Date(suggestion.moderatedAt).toLocaleString('pt-BR')}
                      </span>
                    )}
                  </div>

                  {/* Razão da moderação */}
                  {suggestion.moderationReason && (
                    <div className="mt-2 p-2 bg-gray-100 dark:bg-gray-700 rounded text-sm">
                      <strong>Razão:</strong> {suggestion.moderationReason}
                    </div>
                  )}
                </div>

                <div className="flex flex-col items-end space-y-2">
                  {/* Status */}
                  <div className={`flex items-center space-x-1 px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(suggestion.status)}`}>
                    {getStatusIcon(suggestion.status)}
                    <span>{getStatusText(suggestion.status)}</span>
                  </div>

                  {/* Ações */}
                  <div className="flex space-x-2">
                    <button
                      onClick={() => window.open(`https://www.youtube.com/watch?v=${suggestion.youtubeVideoId}`, '_blank')}
                      className="flex items-center space-x-1 px-2 py-1 bg-gray-100 text-gray-700 rounded text-xs hover:bg-gray-200"
                      title="Ver no YouTube"
                    >
                      <Eye className="w-3 h-3" />
                      <span>Ver</span>
                    </button>

                    {suggestion.status === 'pending' && (
                      <>
                        <button
                          onClick={() => moderateSuggestion(suggestion.id, 'approved')}
                          className="flex items-center space-x-1 px-2 py-1 bg-green-100 text-green-700 rounded text-xs hover:bg-green-200"
                          title="Aprovar"
                        >
                          <CheckCircle className="w-3 h-3" />
                          <span>Aprovar</span>
                        </button>

                        <button
                          onClick={() => {
                            const reason = prompt("Razão da rejeição (opcional):");
                            moderateSuggestion(suggestion.id, 'rejected', reason || undefined);
                          }}
                          className="flex items-center space-x-1 px-2 py-1 bg-red-100 text-red-700 rounded text-xs hover:bg-red-200"
                          title="Rejeitar"
                        >
                          <XCircle className="w-3 h-3" />
                          <span>Rejeitar</span>
                        </button>
                      </>
                    )}

                    {suggestion.status !== 'pending' && (
                      <button
                        onClick={() => moderateSuggestion(suggestion.id, 'pending')}
                        className="flex items-center space-x-1 px-2 py-1 bg-yellow-100 text-yellow-700 rounded text-xs hover:bg-yellow-200"
                        title="Marcar como Pendente"
                      >
                        <Clock className="w-3 h-3" />
                        <span>Pendente</span>
                      </button>
                    )}
                  </div>
                </div>
              </div>
            </motion.div>
          ))
        )}
      </div>

      {/* Estatísticas */}
      {suggestions.length > 0 && (
        <div className="bg-white dark:bg-gray-800 rounded-lg p-4 shadow-sm">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-3">
            Estatísticas
          </h3>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div className="text-center">
              <div className="text-2xl font-bold text-blue-600">
                {suggestions.length}
              </div>
              <div className="text-sm text-gray-500">Total</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-yellow-600">
                {suggestions.filter(s => s.status === 'pending').length}
              </div>
              <div className="text-sm text-gray-500">Pendentes</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-green-600">
                {suggestions.filter(s => s.status === 'approved').length}
              </div>
              <div className="text-sm text-gray-500">Aprovadas</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-red-600">
                {suggestions.filter(s => s.status === 'rejected').length}
              </div>
              <div className="text-sm text-gray-500">Rejeitadas</div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default SuggestionsManagement;
