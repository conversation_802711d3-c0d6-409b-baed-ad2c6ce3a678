# Database Configuration
DATABASE_URL=postgresql://username:password@localhost:5432/restaurant_playlist
DATABASE_HOST=localhost
DATABASE_PORT=5432
DATABASE_USERNAME=postgres
DATABASE_PASSWORD=password
DATABASE_NAME=restaurant_playlist

# Redis Configuration
REDIS_URL=redis://localhost:6379
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=

# Server Configuration
PORT=8001
NODE_ENV=development
FRONTEND_URL=http://localhost:3000

# JWT Configuration
JWT_SECRET=your-super-secret-jwt-key-here
JWT_EXPIRES_IN=7d

# YouTube API Configuration
YOUTUBE_API_KEY=your-youtube-api-key-here

# YouTube OAuth Configuration (NOVO - Para controle de playlists)
YOUTUBE_CLIENT_ID=your-youtube-oauth-client-id
YOUTUBE_CLIENT_SECRET=your-youtube-oauth-client-secret
YOUTUBE_REDIRECT_URI=http://localhost:8001/api/v1/youtube-auth/callback

# Spotify API Configuration (Opcional)
SPOTIFY_CLIENT_ID=your-spotify-client-id
SPOTIFY_CLIENT_SECRET=your-spotify-client-secret

# Payment Configuration (PIX)
PIX_KEY=your-pix-key-here
PIX_MERCHANT_NAME=Restaurant Name
PIX_MERCHANT_CITY=City Name

# Email Configuration (Opcional)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your-app-password

# Webhook Configuration (Opcional)
WEBHOOK_SECRET=your-webhook-secret

# Analytics Configuration (Opcional)
GOOGLE_ANALYTICS_ID=GA-XXXXXXXXX

# Security Configuration
CORS_ORIGIN=http://localhost:3000
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# Feature Flags
ENABLE_REAL_APIS=true
FALLBACK_TO_MOCK=true
ENABLE_YOUTUBE_OAUTH=true
ENABLE_AUTO_PLAYLIST_REORDER=true

# Logging Configuration
LOG_LEVEL=info
LOG_FILE=logs/app.log

# Session Configuration
SESSION_SECRET=your-session-secret-here
SESSION_MAX_AGE=86400000

# File Upload Configuration
MAX_FILE_SIZE=10485760
UPLOAD_PATH=uploads/

# Notification Configuration
ENABLE_WEBSOCKETS=true
ENABLE_PUSH_NOTIFICATIONS=false

# Moderation Configuration
AUTO_APPROVE_SUGGESTIONS=false
MAX_SUGGESTIONS_PER_USER=5
MAX_SUGGESTIONS_PER_HOUR=10

# Playlist Configuration
MAX_QUEUE_SIZE=50
DEFAULT_VOLUME=80
CROSSFADE_DURATION=3

# YouTube Premium Features
YOUTUBE_PREMIUM_ENABLED=true
AUTO_REORDER_THRESHOLD=5
REORDER_DELAY_SECONDS=2
