import React, { useState, useEffect } from "react";
import { motion, AnimatePresence } from "framer-motion";
import {
  Play,
  Clock,
  Users,
  CreditCard,
  Music,
  ChevronDown,
  ChevronUp,
  Star,
  Volume2,
} from "lucide-react";

interface QueueItem {
  id: string;
  suggestionId: string;
  title: string;
  artist: string;
  duration: number;
  thumbnailUrl?: string;
  isPaid: boolean;
  paymentAmount?: number;
  clientName?: string;
  tableName?: string;
  position: number;
  estimatedPlayTime?: string;
}

interface QueueStats {
  totalItems: number;
  paidItems: number;
  freeItems: number;
  totalDuration: number;
  estimatedWaitTime: number;
  currentlyPlaying?: QueueItem;
  nextUp?: QueueItem;
}

interface PlaybackQueueProps {
  restaurantId: string;
  sessionId?: string;
  isCollapsed?: boolean;
  onToggleCollapse?: () => void;
}

const PlaybackQueue: React.FC<PlaybackQueueProps> = ({
  restaurantId,
  sessionId,
  isCollapsed = false,
  onToggleCollapse,
}) => {
  const [queue, setQueue] = useState<QueueItem[]>([]);
  const [stats, setStats] = useState<QueueStats | null>(null);
  const [loading, setLoading] = useState(false);
  const [currentlyPlaying, setCurrentlyPlaying] = useState<QueueItem | null>(
    null
  );

  // Carregar fila de reprodução
  useEffect(() => {
    loadQueue();
    const interval = setInterval(loadQueue, 10000); // Atualizar a cada 10s
    return () => clearInterval(interval);
  }, [restaurantId]);

  const loadQueue = async () => {
    try {
      setLoading(true);

      const response = await fetch(
        `http://localhost:8001/api/v1/playback-queue/${restaurantId}`
      );

      if (response.ok) {
        const data = await response.json();
        setQueue(data.queue || []);
        setStats(data.stats);
        setCurrentlyPlaying(data.currentlyPlaying || null);
      }
    } catch (error) {
      console.error("Erro ao carregar fila:", error);
    } finally {
      setLoading(false);
    }
  };

  const formatDuration = (seconds: number): string => {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}:${remainingSeconds.toString().padStart(2, "0")}`;
  };

  const formatWaitTime = (seconds: number): string => {
    const minutes = Math.floor(seconds / 60);
    if (minutes > 60) {
      const hours = Math.floor(minutes / 60);
      const remainingMinutes = minutes % 60;
      return `${hours}h ${remainingMinutes}m`;
    }
    return `${minutes}m`;
  };

  const isMyMusic = (item: QueueItem): boolean => {
    return sessionId ? item.sessionId === sessionId : false;
  };

  if (loading && queue.length === 0) {
    return (
      <div className="bg-white/5 rounded-lg p-4 border border-white/10">
        <div className="flex items-center justify-center space-x-2">
          <div className="w-4 h-4 border-2 border-purple-400 border-t-transparent rounded-full animate-spin" />
          <span className="text-white">Carregando fila...</span>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white/5 rounded-lg border border-white/10 overflow-hidden">
      {/* Header */}
      <div
        className="p-4 bg-gradient-to-r from-purple-600/20 to-blue-600/20 cursor-pointer"
        onClick={onToggleCollapse}
      >
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <Music className="w-5 h-5 text-purple-400" />
            <div>
              <h3 className="text-white font-semibold">Fila de Reprodução</h3>
              {stats && (
                <p className="text-purple-200 text-sm">
                  {stats.totalItems} músicas • {stats.paidItems} pagas •{" "}
                  {formatWaitTime(stats.estimatedWaitTime)} total
                </p>
              )}
            </div>
          </div>

          <div className="flex items-center space-x-2">
            {stats && stats.paidItems > 0 && (
              <div className="flex items-center space-x-1 text-yellow-400">
                <CreditCard className="w-4 h-4" />
                <span className="text-sm">{stats.paidItems}</span>
              </div>
            )}

            {onToggleCollapse && (
              <button className="p-1 text-purple-300 hover:text-white transition-colors">
                {isCollapsed ? (
                  <ChevronDown className="w-4 h-4" />
                ) : (
                  <ChevronUp className="w-4 h-4" />
                )}
              </button>
            )}
          </div>
        </div>
      </div>

      {/* Currently Playing */}
      {currentlyPlaying && !isCollapsed && (
        <div className="p-4 bg-green-600/10 border-b border-white/10">
          <div className="flex items-center space-x-3">
            <div className="relative">
              <img
                src={
                  currentlyPlaying.thumbnailUrl ||
                  `https://img.youtube.com/vi/${currentlyPlaying.suggestionId}/mqdefault.jpg`
                }
                alt={currentlyPlaying.title}
                className="w-12 h-12 rounded-lg object-cover"
              />
              <div className="absolute inset-0 flex items-center justify-center bg-black/50 rounded-lg">
                <Volume2 className="w-4 h-4 text-green-400" />
              </div>
            </div>

            <div className="flex-1 min-w-0">
              <div className="flex items-center space-x-2">
                <Play className="w-4 h-4 text-green-400" />
                <span className="text-green-400 text-sm font-medium">
                  TOCANDO AGORA
                </span>
                {currentlyPlaying.isPaid && (
                  <CreditCard className="w-3 h-3 text-yellow-400" />
                )}
              </div>
              <h4 className="text-white font-medium truncate">
                {currentlyPlaying.title}
              </h4>
              <p className="text-gray-300 text-sm truncate">
                {currentlyPlaying.artist}
              </p>
              {currentlyPlaying.tableName && (
                <p className="text-purple-300 text-xs">
                  Por {currentlyPlaying.tableName}
                </p>
              )}
            </div>

            <div className="text-right">
              <div className="text-white text-sm">
                {formatDuration(currentlyPlaying.duration)}
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Queue List */}
      <AnimatePresence>
        {!isCollapsed && (
          <motion.div
            initial={{ height: 0 }}
            animate={{ height: "auto" }}
            exit={{ height: 0 }}
            className="overflow-hidden"
          >
            {queue.length === 0 ? (
              <div className="p-6 text-center">
                <Music className="w-12 h-12 text-gray-500 mx-auto mb-3" />
                <p className="text-gray-400">Nenhuma música na fila</p>
                <p className="text-gray-500 text-sm">
                  Seja o primeiro a sugerir!
                </p>
              </div>
            ) : (
              <div className="max-h-96 overflow-y-auto">
                {queue.map((item, index) => (
                  <motion.div
                    key={item.id}
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ delay: index * 0.05 }}
                    className={`p-3 border-b border-white/5 hover:bg-white/5 transition-colors ${
                      isMyMusic(item) ? "bg-blue-600/10 border-blue-500/20" : ""
                    }`}
                  >
                    <div className="flex items-center space-x-3">
                      {/* Position */}
                      <div className="flex-shrink-0 w-8 text-center">
                        <div
                          className={`text-sm font-medium ${
                            item.isPaid ? "text-yellow-400" : "text-purple-300"
                          }`}
                        >
                          #{item.position}
                        </div>
                      </div>

                      {/* Thumbnail */}
                      <div className="relative">
                        <img
                          src={
                            item.thumbnailUrl ||
                            `https://img.youtube.com/vi/${item.suggestionId}/mqdefault.jpg`
                          }
                          alt={item.title}
                          className="w-10 h-10 rounded object-cover"
                        />
                        {item.isPaid && (
                          <div className="absolute -top-1 -right-1 w-4 h-4 bg-yellow-500 rounded-full flex items-center justify-center">
                            <CreditCard className="w-2 h-2 text-white" />
                          </div>
                        )}
                      </div>

                      {/* Info */}
                      <div className="flex-1 min-w-0">
                        <h4
                          className={`font-medium truncate ${
                            isMyMusic(item) ? "text-blue-300" : "text-white"
                          }`}
                        >
                          {item.title}
                        </h4>
                        <p className="text-gray-400 text-sm truncate">
                          {item.artist}
                        </p>

                        <div className="flex items-center space-x-3 mt-1">
                          {item.tableName && (
                            <div className="flex items-center space-x-1">
                              <Users className="w-3 h-3 text-purple-400" />
                              <span className="text-purple-300 text-xs">
                                {item.tableName}
                              </span>
                            </div>
                          )}

                          {item.isPaid && item.paymentAmount && (
                            <div className="flex items-center space-x-1">
                              <CreditCard className="w-3 h-3 text-yellow-400" />
                              <span className="text-yellow-300 text-xs">
                                R$ {item.paymentAmount.toFixed(2)}
                              </span>
                            </div>
                          )}

                          {isMyMusic(item) && (
                            <div className="flex items-center space-x-1">
                              <Star className="w-3 h-3 text-blue-400" />
                              <span className="text-blue-300 text-xs">
                                Sua música
                              </span>
                            </div>
                          )}
                        </div>
                      </div>

                      {/* Duration & Wait Time */}
                      <div className="text-right flex-shrink-0">
                        <div className="text-white text-sm">
                          {formatDuration(item.duration)}
                        </div>
                        {item.estimatedPlayTime && (
                          <div className="flex items-center space-x-1 text-gray-400 text-xs">
                            <Clock className="w-3 h-3" />
                            <span>
                              {new Date(
                                item.estimatedPlayTime
                              ).toLocaleTimeString("pt-BR", {
                                hour: "2-digit",
                                minute: "2-digit",
                              })}
                            </span>
                          </div>
                        )}
                      </div>
                    </div>
                  </motion.div>
                ))}
              </div>
            )}
          </motion.div>
        )}
      </AnimatePresence>

      {/* Footer Stats */}
      {!isCollapsed && stats && stats.totalItems > 0 && (
        <div className="p-3 bg-white/5 border-t border-white/10">
          <div className="flex items-center justify-between text-sm">
            <div className="flex items-center space-x-4">
              <div className="flex items-center space-x-1 text-purple-300">
                <Music className="w-3 h-3" />
                <span>{stats.totalItems} total</span>
              </div>

              {stats.paidItems > 0 && (
                <div className="flex items-center space-x-1 text-yellow-400">
                  <CreditCard className="w-3 h-3" />
                  <span>{stats.paidItems} pagas</span>
                </div>
              )}
            </div>

            <div className="flex items-center space-x-1 text-gray-400">
              <Clock className="w-3 h-3" />
              <span>{formatWaitTime(stats.totalDuration)} total</span>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default PlaybackQueue;
