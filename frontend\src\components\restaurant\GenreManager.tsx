import React, { useState, useEffect } from "react";
import { motion, AnimatePresence } from "framer-motion";
import {
  Music,
  Plus,
  Edit3,
  Trash2,
  Save,
  X,
  Search,
  Filter,
  Palette,
  Tag,
  BarChart3,
  <PERSON><PERSON><PERSON>,
  RefreshCw,
  CheckCircle,
  AlertCircle,
} from "lucide-react";
import { toast } from "react-hot-toast";

interface Genre {
  id: string;
  name: string;
  displayName: string;
  description?: string;
  category: "music" | "mood" | "energy" | "time" | "custom";
  color: string;
  icon?: string;
  priority: number;
  isActive: boolean;
  isDefault: boolean;
  usageCount: number;
  lastUsedAt?: string;
}

interface GenreManagerProps {
  onGenreSelect?: (genre: Genre) => void;
  selectedGenres?: string[];
  mode?: "select" | "manage";
}

const GenreManager: React.FC<GenreManagerProps> = ({
  onGenreSelect,
  selectedGenres = [],
  mode = "manage",
}) => {
  const [genres, setGenres] = useState<Record<string, Genre[]>>({});
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedCategory, setSelectedCategory] = useState<string>("all");
  const [showCreateForm, setShowCreateForm] = useState(false);
  const [editingGenre, setEditingGenre] = useState<Genre | null>(null);
  const [formData, setFormData] = useState({
    name: "",
    displayName: "",
    description: "",
    category: "music" as const,
    color: "#3B82F6",
    icon: "",
    priority: 0,
  });

  const categories = {
    all: "Todos",
    music: "Gêneros Musicais",
    mood: "Humor",
    energy: "Energia",
    time: "Horário",
    custom: "Personalizado",
  };

  const iconOptions = [
    "Music", "Music2", "Music3", "Music4", "Mic", "Heart", "Zap", "Volume2",
    "Headphones", "Sun", "Moon", "Coffee", "Smile", "CloudRain", "Waves",
    "Activity", "Star", "Disc", "Radio", "Speaker",
  ];

  useEffect(() => {
    loadGenres();
  }, []);

  const loadGenres = async () => {
    try {
      setLoading(true);
      const response = await fetch("http://localhost:8001/api/v1/genres");
      if (response.ok) {
        const data = await response.json();
        setGenres(data.genres);
      } else {
        toast.error("Erro ao carregar gêneros");
      }
    } catch (error) {
      console.error("Erro ao carregar gêneros:", error);
      toast.error("Erro ao carregar gêneros");
    } finally {
      setLoading(false);
    }
  };

  const seedDefaultGenres = async () => {
    try {
      const response = await fetch("http://localhost:8001/api/v1/genres/seed", {
        method: "POST",
      });
      if (response.ok) {
        const data = await response.json();
        toast.success(data.message);
        loadGenres();
      } else {
        toast.error("Erro ao criar gêneros padrão");
      }
    } catch (error) {
      console.error("Erro ao criar gêneros padrão:", error);
      toast.error("Erro ao criar gêneros padrão");
    }
  };

  const handleCreateGenre = async () => {
    try {
      const response = await fetch("http://localhost:8001/api/v1/genres", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify(formData),
      });

      if (response.ok) {
        toast.success("Gênero criado com sucesso!");
        setShowCreateForm(false);
        resetForm();
        loadGenres();
      } else {
        const error = await response.json();
        toast.error(error.message || "Erro ao criar gênero");
      }
    } catch (error) {
      console.error("Erro ao criar gênero:", error);
      toast.error("Erro ao criar gênero");
    }
  };

  const handleUpdateGenre = async () => {
    if (!editingGenre) return;

    try {
      const response = await fetch(`http://localhost:8001/api/v1/genres/${editingGenre.id}`, {
        method: "PUT",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify(formData),
      });

      if (response.ok) {
        toast.success("Gênero atualizado com sucesso!");
        setEditingGenre(null);
        resetForm();
        loadGenres();
      } else {
        const error = await response.json();
        toast.error(error.message || "Erro ao atualizar gênero");
      }
    } catch (error) {
      console.error("Erro ao atualizar gênero:", error);
      toast.error("Erro ao atualizar gênero");
    }
  };

  const handleDeleteGenre = async (genre: Genre) => {
    if (genre.isDefault) {
      toast.error("Não é possível deletar gêneros padrão do sistema");
      return;
    }

    if (!confirm(`Tem certeza que deseja deletar o gênero "${genre.displayName}"?`)) {
      return;
    }

    try {
      const response = await fetch(`http://localhost:8001/api/v1/genres/${genre.id}`, {
        method: "DELETE",
      });

      if (response.ok) {
        toast.success("Gênero deletado com sucesso!");
        loadGenres();
      } else {
        const error = await response.json();
        toast.error(error.message || "Erro ao deletar gênero");
      }
    } catch (error) {
      console.error("Erro ao deletar gênero:", error);
      toast.error("Erro ao deletar gênero");
    }
  };

  const startEdit = (genre: Genre) => {
    setEditingGenre(genre);
    setFormData({
      name: genre.name,
      displayName: genre.displayName,
      description: genre.description || "",
      category: genre.category,
      color: genre.color,
      icon: genre.icon || "",
      priority: genre.priority,
    });
    setShowCreateForm(true);
  };

  const resetForm = () => {
    setFormData({
      name: "",
      displayName: "",
      description: "",
      category: "music",
      color: "#3B82F6",
      icon: "",
      priority: 0,
    });
    setEditingGenre(null);
  };

  const filteredGenres = Object.entries(genres).reduce((acc, [category, categoryGenres]) => {
    if (selectedCategory !== "all" && category !== selectedCategory) {
      return acc;
    }

    const filtered = categoryGenres.filter(genre =>
      genre.displayName.toLowerCase().includes(searchQuery.toLowerCase()) ||
      genre.name.toLowerCase().includes(searchQuery.toLowerCase())
    );

    if (filtered.length > 0) {
      acc[category] = filtered;
    }

    return acc;
  }, {} as Record<string, Genre[]>);

  const totalGenres = Object.values(genres).flat().length;
  const activeGenres = Object.values(genres).flat().filter(g => g.isActive).length;

  if (loading) {
    return (
      <div className="flex items-center justify-center p-8">
        <RefreshCw className="w-6 h-6 animate-spin text-blue-600" />
        <span className="ml-2 text-gray-600">Carregando gêneros...</span>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-3">
          <div className="p-2 bg-purple-100 dark:bg-purple-900/20 rounded-lg">
            <Tag className="w-6 h-6 text-purple-600 dark:text-purple-400" />
          </div>
          <div>
            <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
              Gerenciamento de Gêneros
            </h2>
            <p className="text-sm text-gray-600 dark:text-gray-400">
              {totalGenres} gêneros • {activeGenres} ativos
            </p>
          </div>
        </div>

        <div className="flex items-center space-x-2">
          <button
            onClick={seedDefaultGenres}
            className="flex items-center space-x-2 px-3 py-2 bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors"
          >
            <RefreshCw className="w-4 h-4" />
            <span>Gêneros Padrão</span>
          </button>

          <button
            onClick={() => setShowCreateForm(true)}
            className="flex items-center space-x-2 px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors"
          >
            <Plus className="w-4 h-4" />
            <span>Novo Gênero</span>
          </button>
        </div>
      </div>

      {/* Filters */}
      <div className="flex flex-col sm:flex-row gap-4">
        <div className="flex-1">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
            <input
              type="text"
              placeholder="Buscar gêneros..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="w-full pl-10 pr-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-purple-500 focus:border-transparent"
            />
          </div>
        </div>

        <select
          value={selectedCategory}
          onChange={(e) => setSelectedCategory(e.target.value)}
          className="px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-purple-500 focus:border-transparent"
        >
          {Object.entries(categories).map(([key, label]) => (
            <option key={key} value={key}>
              {label}
            </option>
          ))}
        </select>
      </div>

      {/* Genres Grid */}
      <div className="space-y-6">
        {Object.entries(filteredGenres).map(([category, categoryGenres]) => (
          <div key={category} className="bg-white dark:bg-gray-800 rounded-lg p-6 border border-gray-200 dark:border-gray-700">
            <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4 flex items-center space-x-2">
              <span>{categories[category as keyof typeof categories]}</span>
              <span className="text-sm text-gray-500 bg-gray-100 dark:bg-gray-700 px-2 py-1 rounded-full">
                {categoryGenres.length}
              </span>
            </h3>

            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
              {categoryGenres.map((genre) => (
                <motion.div
                  key={genre.id}
                  initial={{ opacity: 0, y: 10 }}
                  animate={{ opacity: 1, y: 0 }}
                  className={`p-4 rounded-lg border-2 transition-all cursor-pointer ${
                    selectedGenres.includes(genre.id)
                      ? "border-purple-500 bg-purple-50 dark:bg-purple-900/20"
                      : "border-gray-200 dark:border-gray-600 hover:border-gray-300 dark:hover:border-gray-500"
                  }`}
                  onClick={() => mode === "select" && onGenreSelect?.(genre)}
                >
                  <div className="flex items-center justify-between mb-2">
                    <div
                      className="w-8 h-8 rounded-lg flex items-center justify-center text-white text-sm font-medium"
                      style={{ backgroundColor: genre.color }}
                    >
                      {genre.icon ? (
                        <Music className="w-4 h-4" />
                      ) : (
                        genre.displayName.charAt(0).toUpperCase()
                      )}
                    </div>

                    {mode === "manage" && (
                      <div className="flex items-center space-x-1">
                        <button
                          onClick={(e) => {
                            e.stopPropagation();
                            startEdit(genre);
                          }}
                          className="p-1 text-gray-400 hover:text-blue-600 transition-colors"
                        >
                          <Edit3 className="w-3 h-3" />
                        </button>
                        
                        {!genre.isDefault && (
                          <button
                            onClick={(e) => {
                              e.stopPropagation();
                              handleDeleteGenre(genre);
                            }}
                            className="p-1 text-gray-400 hover:text-red-600 transition-colors"
                          >
                            <Trash2 className="w-3 h-3" />
                          </button>
                        )}
                      </div>
                    )}
                  </div>

                  <h4 className="font-medium text-gray-900 dark:text-white text-sm mb-1">
                    {genre.displayName}
                  </h4>
                  
                  {genre.description && (
                    <p className="text-xs text-gray-600 dark:text-gray-400 mb-2">
                      {genre.description}
                    </p>
                  )}

                  <div className="flex items-center justify-between text-xs text-gray-500">
                    <span className="flex items-center space-x-1">
                      <BarChart3 className="w-3 h-3" />
                      <span>{genre.usageCount}</span>
                    </span>
                    
                    <div className="flex items-center space-x-1">
                      {genre.isDefault && (
                        <CheckCircle className="w-3 h-3 text-green-500" title="Gênero padrão" />
                      )}
                      {!genre.isActive && (
                        <AlertCircle className="w-3 h-3 text-red-500" title="Inativo" />
                      )}
                    </div>
                  </div>
                </motion.div>
              ))}
            </div>
          </div>
        ))}
      </div>

      {Object.keys(filteredGenres).length === 0 && (
        <div className="text-center py-12">
          <Tag className="w-12 h-12 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
            Nenhum gênero encontrado
          </h3>
          <p className="text-gray-600 dark:text-gray-400 mb-4">
            {searchQuery ? "Tente uma busca diferente" : "Crie seu primeiro gênero"}
          </p>
          {!searchQuery && (
            <button
              onClick={() => setShowCreateForm(true)}
              className="inline-flex items-center space-x-2 px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors"
            >
              <Plus className="w-4 h-4" />
              <span>Criar Gênero</span>
            </button>
          )}
        </div>
      )}

      {/* Create/Edit Form Modal */}
      <AnimatePresence>
        {showCreateForm && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50"
            onClick={() => {
              setShowCreateForm(false);
              resetForm();
            }}
          >
            <motion.div
              initial={{ scale: 0.95, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              exit={{ scale: 0.95, opacity: 0 }}
              className="bg-white dark:bg-gray-800 rounded-lg p-6 w-full max-w-md"
              onClick={(e) => e.stopPropagation()}
            >
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                  {editingGenre ? "Editar Gênero" : "Novo Gênero"}
                </h3>
                <button
                  onClick={() => {
                    setShowCreateForm(false);
                    resetForm();
                  }}
                  className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
                >
                  <X className="w-5 h-5" />
                </button>
              </div>

              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                    Nome (ID)
                  </label>
                  <input
                    type="text"
                    value={formData.name}
                    onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                    disabled={!!editingGenre}
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-purple-500 focus:border-transparent disabled:opacity-50"
                    placeholder="rock, pop, sertanejo..."
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                    Nome de Exibição
                  </label>
                  <input
                    type="text"
                    value={formData.displayName}
                    onChange={(e) => setFormData({ ...formData, displayName: e.target.value })}
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                    placeholder="Rock, Pop, Sertanejo..."
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                    Descrição
                  </label>
                  <textarea
                    value={formData.description}
                    onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                    rows={2}
                    placeholder="Descrição do gênero..."
                  />
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                      Categoria
                    </label>
                    <select
                      value={formData.category}
                      onChange={(e) => setFormData({ ...formData, category: e.target.value as any })}
                      className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                    >
                      <option value="music">Gênero Musical</option>
                      <option value="mood">Humor</option>
                      <option value="energy">Energia</option>
                      <option value="time">Horário</option>
                      <option value="custom">Personalizado</option>
                    </select>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                      Cor
                    </label>
                    <div className="flex items-center space-x-2">
                      <input
                        type="color"
                        value={formData.color}
                        onChange={(e) => setFormData({ ...formData, color: e.target.value })}
                        className="w-10 h-10 border border-gray-300 dark:border-gray-600 rounded-lg cursor-pointer"
                      />
                      <input
                        type="text"
                        value={formData.color}
                        onChange={(e) => setFormData({ ...formData, color: e.target.value })}
                        className="flex-1 px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                        placeholder="#3B82F6"
                      />
                    </div>
                  </div>
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                      Ícone
                    </label>
                    <select
                      value={formData.icon}
                      onChange={(e) => setFormData({ ...formData, icon: e.target.value })}
                      className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                    >
                      <option value="">Sem ícone</option>
                      {iconOptions.map(icon => (
                        <option key={icon} value={icon}>{icon}</option>
                      ))}
                    </select>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                      Prioridade
                    </label>
                    <input
                      type="number"
                      value={formData.priority}
                      onChange={(e) => setFormData({ ...formData, priority: parseInt(e.target.value) || 0 })}
                      className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                      min="0"
                    />
                  </div>
                </div>
              </div>

              <div className="flex items-center justify-end space-x-3 mt-6">
                <button
                  onClick={() => {
                    setShowCreateForm(false);
                    resetForm();
                  }}
                  className="px-4 py-2 text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors"
                >
                  Cancelar
                </button>
                <button
                  onClick={editingGenre ? handleUpdateGenre : handleCreateGenre}
                  className="flex items-center space-x-2 px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors"
                >
                  <Save className="w-4 h-4" />
                  <span>{editingGenre ? "Atualizar" : "Criar"}</span>
                </button>
              </div>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
};

export default GenreManager;
