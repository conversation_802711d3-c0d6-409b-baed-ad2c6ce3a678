import { create } from "zustand";
import { devtools, persist } from "zustand/middleware";
import {
  Restaurant,
  User,
  Suggestion,
  PlayQueue,
  ConnectionStatus,
  DeviceType,
  Theme,
  Notification,
} from "@/types";

// Interface do estado global
interface AppState {
  // Autenticação
  user: User | null;
  isAuthenticated: boolean;
  authToken: string | null;

  // Restaurante atual
  currentRestaurant: Restaurant | null;

  // Sugestões e fila
  suggestions: Suggestion[];
  playQueue: PlayQueue | null;
  currentlyPlaying: Suggestion | null;

  // UI State
  theme: "light" | "dark" | "auto";
  deviceType: DeviceType;
  isOnline: boolean;
  connectionStatus: ConnectionStatus;

  // Notificações
  notifications: Notification[];

  // Configurações
  settings: {
    autoRefresh: boolean;
    soundEnabled: boolean;
    showNotifications: boolean;
    language: string;
    maxSuggestionsPerSession: number;
  };

  // Loading states
  loading: {
    suggestions: boolean;
    queue: boolean;
    search: boolean;
    voting: boolean;
  };

  // Ações
  setUser: (user: User | null) => void;
  setAuthToken: (token: string | null) => void;
  setCurrentRestaurant: (restaurant: Restaurant | null) => void;
  setSuggestions: (suggestions: Suggestion[]) => void;
  addSuggestion: (suggestion: Suggestion) => void;
  updateSuggestion: (id: string, updates: Partial<Suggestion>) => void;
  removeSuggestion: (id: string) => void;
  setPlayQueue: (queue: PlayQueue) => void;
  setCurrentlyPlaying: (suggestion: Suggestion | null) => void;
  setTheme: (theme: "light" | "dark" | "auto") => void;
  setDeviceType: (type: DeviceType) => void;
  setOnlineStatus: (isOnline: boolean) => void;
  setConnectionStatus: (status: ConnectionStatus) => void;
  addNotification: (
    notification: Omit<Notification, "id" | "createdAt">
  ) => void;
  removeNotification: (id: string) => void;
  clearNotifications: () => void;
  updateSettings: (settings: Partial<AppState["settings"]>) => void;
  setLoading: (key: keyof AppState["loading"], value: boolean) => void;
  reset: () => void;
}

// Estado inicial
const initialState = {
  user: null,
  isAuthenticated: false,
  authToken: null,
  currentRestaurant: null,
  suggestions: [],
  playQueue: null,
  currentlyPlaying: null,
  theme: "auto" as const,
  deviceType: "desktop" as DeviceType,
  isOnline: navigator.onLine,
  connectionStatus: "disconnected" as ConnectionStatus,
  notifications: [],
  settings: {
    autoRefresh: true,
    soundEnabled: true,
    showNotifications: true,
    language: "pt-BR",
    maxSuggestionsPerSession: 3,
  },
  loading: {
    suggestions: false,
    queue: false,
    search: false,
    voting: false,
  },
};

// Store principal
export const useAppStore = create<AppState>()(
  devtools(
    persist(
      (set, get) => ({
        ...initialState,

        // Ações de autenticação
        setUser: (user) => set({ user, isAuthenticated: !!user }),

        setAuthToken: (token) => {
          set({ authToken: token, isAuthenticated: !!token });
          if (token) {
            localStorage.setItem("authToken", token);
          } else {
            localStorage.removeItem("authToken");
          }
        },

        // Ações do restaurante
        setCurrentRestaurant: (restaurant) =>
          set({ currentRestaurant: restaurant }),

        // Ações das sugestões
        setSuggestions: (suggestions) => set({ suggestions }),

        addSuggestion: (suggestion) =>
          set((state) => ({
            suggestions: [suggestion, ...state.suggestions],
          })),

        updateSuggestion: (id, updates) =>
          set((state) => ({
            suggestions: state.suggestions.map((s) =>
              s.id === id ? { ...s, ...updates } : s
            ),
          })),

        removeSuggestion: (id) =>
          set((state) => ({
            suggestions: state.suggestions.filter((s) => s.id !== id),
          })),

        // Ações da fila de reprodução
        setPlayQueue: (queue) => set({ playQueue: queue }),

        setCurrentlyPlaying: (suggestion) =>
          set({ currentlyPlaying: suggestion }),

        // Ações da UI
        setTheme: (theme) => {
          set({ theme });
          // Aplicar tema no documento
          const root = document.documentElement;
          if (theme === "dark") {
            root.classList.add("dark");
          } else if (theme === "light") {
            root.classList.remove("dark");
          } else {
            // Auto - usar preferência do sistema
            const prefersDark = window.matchMedia(
              "(prefers-color-scheme: dark)"
            ).matches;
            if (prefersDark) {
              root.classList.add("dark");
            } else {
              root.classList.remove("dark");
            }
          }
        },

        setDeviceType: (deviceType) => set({ deviceType }),

        setOnlineStatus: (isOnline) => set({ isOnline }),

        setConnectionStatus: (connectionStatus) => set({ connectionStatus }),

        // Ações das notificações
        addNotification: (notification) => {
          const id = Date.now().toString();
          const newNotification: Notification = {
            ...notification,
            id,
            createdAt: new Date().toISOString(),
          };

          set((state) => ({
            notifications: [newNotification, ...state.notifications],
          }));

          // Auto-remover após duração especificada
          if (notification.duration && notification.duration > 0) {
            setTimeout(() => {
              get().removeNotification(id);
            }, notification.duration);
          }
        },

        removeNotification: (id) =>
          set((state) => ({
            notifications: state.notifications.filter((n) => n.id !== id),
          })),

        clearNotifications: () => set({ notifications: [] }),

        // Ações das configurações
        updateSettings: (newSettings) =>
          set((state) => ({
            settings: { ...state.settings, ...newSettings },
          })),

        // Ações de loading
        setLoading: (key, value) =>
          set((state) => ({
            loading: { ...state.loading, [key]: value },
          })),

        // Reset do estado
        reset: () => set(initialState),
      }),
      {
        name: "restaurant-playlist-store",
        partialize: (state) => ({
          // Persistir dados importantes para manter sessão
          user: state.user,
          isAuthenticated: state.isAuthenticated,
          authToken: state.authToken,
          theme: state.theme,
          settings: state.settings,
        }),
      }
    ),
    {
      name: "restaurant-playlist-store",
    }
  )
);

// Hooks específicos para partes do estado
export const useAuth = () => {
  const { user, isAuthenticated, authToken, setUser, setAuthToken } =
    useAppStore();
  return { user, isAuthenticated, authToken, setUser, setAuthToken };
};

export const useRestaurant = () => {
  const { currentRestaurant, setCurrentRestaurant } = useAppStore();
  return { currentRestaurant, setCurrentRestaurant };
};

export const useSuggestions = () => {
  const {
    suggestions,
    setSuggestions,
    addSuggestion,
    updateSuggestion,
    removeSuggestion,
  } = useAppStore();
  return {
    suggestions,
    setSuggestions,
    addSuggestion,
    updateSuggestion,
    removeSuggestion,
  };
};

export const usePlayQueue = () => {
  const { playQueue, currentlyPlaying, setPlayQueue, setCurrentlyPlaying } =
    useAppStore();
  return {
    playQueue,
    currentlyPlaying,
    setPlayQueue,
    setCurrentlyPlaying,
  };
};

export const useUI = () => {
  const {
    theme,
    deviceType,
    isOnline,
    connectionStatus,
    setTheme,
    setDeviceType,
    setOnlineStatus,
    setConnectionStatus,
  } = useAppStore();
  return {
    theme,
    deviceType,
    isOnline,
    connectionStatus,
    setTheme,
    setDeviceType,
    setOnlineStatus,
    setConnectionStatus,
  };
};

export const useNotifications = () => {
  const {
    notifications,
    addNotification,
    removeNotification,
    clearNotifications,
  } = useAppStore();
  return {
    notifications,
    addNotification,
    removeNotification,
    clearNotifications,
  };
};

export const useSettings = () => {
  const { settings, updateSettings } = useAppStore();
  return { settings, updateSettings };
};

export const useLoading = () => {
  const { loading, setLoading } = useAppStore();
  return { loading, setLoading };
};

// Inicialização do store
export const initializeStore = () => {
  const store = useAppStore.getState();

  // Detectar tipo de dispositivo
  const detectDeviceType = (): DeviceType => {
    const width = window.innerWidth;
    if (width < 768) return "mobile";
    if (width < 1024) return "tablet";
    return "desktop";
  };

  store.setDeviceType(detectDeviceType());

  // Aplicar tema inicial
  store.setTheme(store.theme);

  // Monitorar status online/offline
  window.addEventListener("online", () => store.setOnlineStatus(true));
  window.addEventListener("offline", () => store.setOnlineStatus(false));

  // Monitorar mudanças no tamanho da tela
  window.addEventListener("resize", () => {
    store.setDeviceType(detectDeviceType());
  });

  // Monitorar mudanças na preferência de tema do sistema
  const mediaQuery = window.matchMedia("(prefers-color-scheme: dark)");
  mediaQuery.addEventListener("change", () => {
    if (store.theme === "auto") {
      store.setTheme("auto"); // Reaplica o tema automático
    }
  });
};

export default useAppStore;
