import React, { useState, useEffect } from "react";
import { useParams } from "react-router-dom";
import { motion } from "framer-motion";
import {
  Play,
  Pause,
  SkipForward,
  Volume2,
  VolumeX,
  Music,
  Clock,
  Users,
  TrendingUp,
  Al<PERSON><PERSON>riangle,
  RefreshCw,
  Settings,
} from "lucide-react";
import { toast } from "react-hot-toast";

interface Track {
  id: string;
  title: string;
  artist: string;
  youtubeVideoId: string;
  duration: number;
  thumbnailUrl: string;
  upvotes: number;
  downvotes: number;
  score: number;
  suggestedBy?: string;
  createdAt: Date;
}

interface PlaybackState {
  currentTrack: Track | null;
  isPlaying: boolean;
  currentTime: number;
  volume: number;
  queue: Track[];
  history: Track[];
}

const PlaybackController: React.FC = () => {
  const { restaurantId } = useParams<{ restaurantId: string }>();
  const [playbackState, setPlaybackState] = useState<PlaybackState | null>(
    null
  );
  const [loading, setLoading] = useState(true);
  const [volume, setVolume] = useState(70);
  const [isMuted, setIsMuted] = useState(false);

  const finalRestaurantId = restaurantId || "demo-restaurant";

  useEffect(() => {
    loadPlaybackState();

    // Atualizar estado a cada 5 segundos
    const interval = setInterval(loadPlaybackState, 5000);
    return () => clearInterval(interval);
  }, []);

  const loadPlaybackState = async () => {
    try {
      const response = await fetch(
        `http://localhost:8001/api/v1/playback/${finalRestaurantId}/state`
      );

      if (response.ok) {
        const data = await response.json();
        setPlaybackState(data.state);
        setVolume(data.state.volume);
      }
    } catch (error) {
      console.error("Erro ao carregar estado de reprodução:", error);
    } finally {
      setLoading(false);
    }
  };

  const handlePlayPause = async () => {
    if (!playbackState) return;

    try {
      const endpoint = playbackState.isPlaying ? "pause" : "resume";
      const response = await fetch(
        `http://localhost:8001/api/v1/playback/${finalRestaurantId}/${endpoint}`,
        { method: "POST" }
      );

      if (response.ok) {
        setPlaybackState((prev) =>
          prev
            ? {
                ...prev,
                isPlaying: !prev.isPlaying,
              }
            : null
        );

        toast.success(
          playbackState.isPlaying ? "Reprodução pausada" : "Reprodução retomada"
        );
      }
    } catch (error) {
      toast.error("Erro ao controlar reprodução");
    }
  };

  const handleSkip = async () => {
    try {
      const response = await fetch(
        `http://localhost:8001/api/v1/playback/${finalRestaurantId}/skip`,
        { method: "POST" }
      );

      if (response.ok) {
        toast.success("Música pulada");
        loadPlaybackState(); // Recarregar estado
      }
    } catch (error) {
      toast.error("Erro ao pular música");
    }
  };

  const handleStartNext = async () => {
    try {
      const response = await fetch(
        `http://localhost:8001/api/v1/playback/${finalRestaurantId}/start`,
        { method: "POST" }
      );

      if (response.ok) {
        toast.success("Próxima música iniciada");
        loadPlaybackState();
      }
    } catch (error) {
      toast.error("Erro ao iniciar próxima música");
    }
  };

  const handleVolumeChange = async (newVolume: number) => {
    try {
      const response = await fetch(
        `http://localhost:8001/api/v1/playback/${finalRestaurantId}/volume`,
        {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify({ volume: newVolume }),
        }
      );

      if (response.ok) {
        setVolume(newVolume);
        setPlaybackState((prev) =>
          prev ? { ...prev, volume: newVolume } : null
        );
      }
    } catch (error) {
      toast.error("Erro ao ajustar volume");
    }
  };

  const toggleMute = () => {
    if (isMuted) {
      handleVolumeChange(volume);
      setIsMuted(false);
    } else {
      handleVolumeChange(0);
      setIsMuted(true);
    }
  };

  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs.toString().padStart(2, "0")}`;
  };

  const getProgressPercentage = () => {
    if (!playbackState?.currentTrack) return 0;
    return (
      (playbackState.currentTime / playbackState.currentTrack.duration) * 100
    );
  };

  if (loading) {
    return (
      <div className="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm">
        <div className="flex justify-center items-center h-32">
          <RefreshCw className="w-8 h-8 animate-spin text-blue-600" />
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-2xl font-bold text-gray-900 dark:text-white">
            Controle de Reprodução
          </h2>
          <p className="text-gray-600 dark:text-gray-400 mt-1">
            Controle inteligente da playlist do restaurante
          </p>
        </div>

        <button
          onClick={loadPlaybackState}
          className="flex items-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
        >
          <RefreshCw className="w-4 h-4" />
          <span>Atualizar</span>
        </button>
      </div>

      {/* Player Principal */}
      <div className="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm">
        {playbackState?.currentTrack ? (
          <div className="space-y-4">
            {/* Informações da música atual */}
            <div className="flex items-center space-x-4">
              <img
                src={playbackState.currentTrack.thumbnailUrl}
                alt={playbackState.currentTrack.title}
                className="w-16 h-12 object-cover rounded-lg"
              />
              <div className="flex-1">
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                  {playbackState.currentTrack.title}
                </h3>
                <p className="text-gray-600 dark:text-gray-400">
                  {playbackState.currentTrack.artist}
                </p>
                <div className="flex items-center space-x-4 mt-1">
                  <div className="flex items-center space-x-1 text-green-600">
                    <TrendingUp className="w-4 h-4" />
                    <span className="text-sm">
                      {playbackState.currentTrack.upvotes}
                    </span>
                  </div>
                  <div className="flex items-center space-x-1 text-red-600">
                    <AlertTriangle className="w-4 h-4" />
                    <span className="text-sm">
                      {playbackState.currentTrack.downvotes}
                    </span>
                  </div>
                  <div className="text-sm text-gray-500">
                    Score: {playbackState.currentTrack.score}
                  </div>
                </div>
              </div>
            </div>

            {/* Barra de progresso */}
            <div className="space-y-2">
              <div className="flex justify-between text-sm text-gray-500">
                <span>{formatTime(playbackState.currentTime)}</span>
                <span>{formatTime(playbackState.currentTrack.duration)}</span>
              </div>
              <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                <div
                  className="bg-blue-600 h-2 rounded-full transition-all duration-1000"
                  style={{ width: `${getProgressPercentage()}%` }}
                />
              </div>
            </div>

            {/* Controles */}
            <div className="flex items-center justify-center space-x-4">
              <button
                onClick={handlePlayPause}
                className="flex items-center justify-center w-12 h-12 bg-blue-600 text-white rounded-full hover:bg-blue-700 transition-colors"
              >
                {playbackState.isPlaying ? (
                  <Pause className="w-6 h-6" />
                ) : (
                  <Play className="w-6 h-6 ml-1" />
                )}
              </button>

              <button
                onClick={handleSkip}
                className="flex items-center justify-center w-10 h-10 bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded-full hover:bg-gray-300 dark:hover:bg-gray-600 transition-colors"
              >
                <SkipForward className="w-5 h-5" />
              </button>

              {/* Controle de volume */}
              <div className="flex items-center space-x-2">
                <button onClick={toggleMute}>
                  {isMuted || volume === 0 ? (
                    <VolumeX className="w-5 h-5 text-gray-600" />
                  ) : (
                    <Volume2 className="w-5 h-5 text-gray-600" />
                  )}
                </button>
                <input
                  type="range"
                  min="0"
                  max="100"
                  value={isMuted ? 0 : volume}
                  onChange={(e) => handleVolumeChange(parseInt(e.target.value))}
                  className="w-20 h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer"
                />
                <span className="text-sm text-gray-500 w-8">
                  {isMuted ? 0 : volume}
                </span>
              </div>
            </div>
          </div>
        ) : (
          <div className="text-center py-8">
            <Music className="w-12 h-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
              Nenhuma música tocando
            </h3>
            <p className="text-gray-600 dark:text-gray-400 mb-4">
              Inicie a reprodução da próxima música na fila
            </p>
            <button
              onClick={handleStartNext}
              className="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
            >
              Iniciar Próxima Música
            </button>
          </div>
        )}
      </div>

      {/* Fila de reprodução */}
      {playbackState?.queue && playbackState.queue.length > 0 && (
        <div className="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
            Próximas na Fila ({playbackState.queue.length})
          </h3>
          <div className="space-y-3">
            {playbackState.queue.slice(0, 5).map((track, index) => (
              <div
                key={track.id}
                className="flex items-center space-x-3 p-3 bg-gray-50 dark:bg-gray-700 rounded-lg"
              >
                <div className="w-6 h-6 bg-gray-200 dark:bg-gray-600 rounded text-xs flex items-center justify-center font-medium">
                  {index + 1}
                </div>
                <img
                  src={track.thumbnailUrl}
                  alt={track.title}
                  className="w-10 h-8 object-cover rounded"
                />
                <div className="flex-1 min-w-0">
                  <p className="text-sm font-medium text-gray-900 dark:text-white truncate">
                    {track.title}
                  </p>
                  <p className="text-xs text-gray-600 dark:text-gray-400 truncate">
                    {track.artist} • Score: {track.score}
                  </p>
                </div>
                <div className="text-xs text-gray-500">
                  {formatTime(track.duration)}
                </div>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};

export default PlaybackController;
