import React, { useState, useEffect } from "react";
import { toast } from "react-hot-toast";
import {
  Clock,
  Plus,
  Edit,
  Trash2,
  Calendar,
  Play,
  Pause,
  Save,
  X,
} from "lucide-react";

interface TimeSlot {
  playlistId: string;
  playlistName: string;
  startTime: string;
  endTime: string;
  days: number[];
  priority: number;
  isActive: boolean;
  description?: string;
}

interface PlaylistSchedule {
  id: string;
  name: string;
  description?: string;
  timeSlots: TimeSlot[];
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

interface Playlist {
  id: string;
  name: string;
  isActive: boolean;
}

const DAYS_OF_WEEK = [
  { value: 0, label: "Dom", fullName: "Domingo" },
  { value: 1, label: "Seg", fullName: "Segunda" },
  { value: 2, label: "Ter", fullName: "Terça" },
  { value: 3, label: "Qua", fullName: "Quarta" },
  { value: 4, label: "Qui", fullName: "Quinta" },
  { value: 5, label: "Sex", fullName: "Sex<PERSON>" },
  { value: 6, label: "Sáb", fullName: "Sábado" },
];

export default function PlaylistScheduler() {
  const [schedules, setSchedules] = useState<PlaylistSchedule[]>([]);
  const [playlists, setPlaylists] = useState<Playlist[]>([]);
  const [loading, setLoading] = useState(false);
  const [showForm, setShowForm] = useState(false);
  const [editingSchedule, setEditingSchedule] = useState<PlaylistSchedule | null>(null);

  // Form state
  const [formData, setFormData] = useState({
    name: "",
    description: "",
    isActive: true,
    timeSlots: [] as TimeSlot[],
  });

  const [newTimeSlot, setNewTimeSlot] = useState<Partial<TimeSlot>>({
    playlistId: "",
    playlistName: "",
    startTime: "09:00",
    endTime: "17:00",
    days: [],
    priority: 1,
    isActive: true,
    description: "",
  });

  useEffect(() => {
    loadSchedules();
    loadPlaylists();
  }, []);

  const loadSchedules = async () => {
    try {
      setLoading(true);
      const response = await fetch(
        "/api/v1/playlist-schedules/demo-restaurant"
      );
      if (response.ok) {
        const data = await response.json();
        setSchedules(data.schedules || []);
      }
    } catch (error) {
      console.error("Erro ao carregar agendamentos:", error);
      toast.error("Erro ao carregar agendamentos");
    } finally {
      setLoading(false);
    }
  };

  const loadPlaylists = async () => {
    try {
      const response = await fetch("/api/v1/playlists/demo-restaurant");
      if (response.ok) {
        const data = await response.json();
        setPlaylists(data.playlists || []);
      }
    } catch (error) {
      console.error("Erro ao carregar playlists:", error);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (formData.timeSlots.length === 0) {
      toast.error("Adicione pelo menos um horário");
      return;
    }

    try {
      setLoading(true);
      const url = editingSchedule
        ? `/api/v1/playlist-schedules/${editingSchedule.id}`
        : "/api/v1/playlist-schedules";
      
      const method = editingSchedule ? "PUT" : "POST";
      
      const response = await fetch(url, {
        method,
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          ...formData,
          restaurantId: "demo-restaurant",
        }),
      });

      if (response.ok) {
        toast.success(
          editingSchedule
            ? "Agendamento atualizado com sucesso!"
            : "Agendamento criado com sucesso!"
        );
        resetForm();
        loadSchedules();
      } else {
        const error = await response.json();
        toast.error(error.message || "Erro ao salvar agendamento");
      }
    } catch (error) {
      console.error("Erro ao salvar agendamento:", error);
      toast.error("Erro ao salvar agendamento");
    } finally {
      setLoading(false);
    }
  };

  const addTimeSlot = () => {
    if (!newTimeSlot.playlistId || !newTimeSlot.days?.length) {
      toast.error("Selecione uma playlist e pelo menos um dia");
      return;
    }

    const playlist = playlists.find(p => p.id === newTimeSlot.playlistId);
    if (!playlist) return;

    const timeSlot: TimeSlot = {
      playlistId: newTimeSlot.playlistId,
      playlistName: playlist.name,
      startTime: newTimeSlot.startTime || "09:00",
      endTime: newTimeSlot.endTime || "17:00",
      days: newTimeSlot.days || [],
      priority: newTimeSlot.priority || 1,
      isActive: newTimeSlot.isActive ?? true,
      description: newTimeSlot.description || "",
    };

    setFormData(prev => ({
      ...prev,
      timeSlots: [...prev.timeSlots, timeSlot],
    }));

    // Reset new time slot
    setNewTimeSlot({
      playlistId: "",
      playlistName: "",
      startTime: "09:00",
      endTime: "17:00",
      days: [],
      priority: 1,
      isActive: true,
      description: "",
    });
  };

  const removeTimeSlot = (index: number) => {
    setFormData(prev => ({
      ...prev,
      timeSlots: prev.timeSlots.filter((_, i) => i !== index),
    }));
  };

  const editSchedule = (schedule: PlaylistSchedule) => {
    setEditingSchedule(schedule);
    setFormData({
      name: schedule.name,
      description: schedule.description || "",
      isActive: schedule.isActive,
      timeSlots: schedule.timeSlots,
    });
    setShowForm(true);
  };

  const deleteSchedule = async (id: string) => {
    if (!confirm("Tem certeza que deseja deletar este agendamento?")) return;

    try {
      const response = await fetch(`/api/v1/playlist-schedules/${id}`, {
        method: "DELETE",
      });

      if (response.ok) {
        toast.success("Agendamento deletado com sucesso!");
        loadSchedules();
      } else {
        toast.error("Erro ao deletar agendamento");
      }
    } catch (error) {
      console.error("Erro ao deletar agendamento:", error);
      toast.error("Erro ao deletar agendamento");
    }
  };

  const resetForm = () => {
    setFormData({
      name: "",
      description: "",
      isActive: true,
      timeSlots: [],
    });
    setNewTimeSlot({
      playlistId: "",
      playlistName: "",
      startTime: "09:00",
      endTime: "17:00",
      days: [],
      priority: 1,
      isActive: true,
      description: "",
    });
    setEditingSchedule(null);
    setShowForm(false);
  };

  const formatDays = (days: number[]) => {
    return days
      .sort()
      .map(day => DAYS_OF_WEEK.find(d => d.value === day)?.label)
      .join(", ");
  };

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-2xl font-bold text-gray-900 dark:text-white">
            Agendamento de Playlists
          </h2>
          <p className="text-gray-600 dark:text-gray-400 mt-1">
            Configure horários específicos para suas playlists
          </p>
        </div>

        <button
          onClick={() => setShowForm(true)}
          className="bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 flex items-center space-x-2"
        >
          <Plus className="w-4 h-4" />
          <span>Novo Agendamento</span>
        </button>
      </div>

      {/* Lista de Agendamentos */}
      <div className="grid gap-4">
        {schedules.map((schedule) => (
          <div
            key={schedule.id}
            className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-6"
          >
            <div className="flex justify-between items-start mb-4">
              <div>
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                  {schedule.name}
                </h3>
                {schedule.description && (
                  <p className="text-gray-600 dark:text-gray-400 text-sm mt-1">
                    {schedule.description}
                  </p>
                )}
              </div>
              
              <div className="flex items-center space-x-2">
                <span
                  className={`px-2 py-1 rounded-full text-xs font-medium ${
                    schedule.isActive
                      ? "bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400"
                      : "bg-gray-100 text-gray-800 dark:bg-gray-900/30 dark:text-gray-400"
                  }`}
                >
                  {schedule.isActive ? "Ativo" : "Inativo"}
                </span>
                
                <button
                  onClick={() => editSchedule(schedule)}
                  className="p-2 text-blue-600 hover:bg-blue-100 dark:hover:bg-blue-900/20 rounded-lg"
                  title="Editar"
                >
                  <Edit className="w-4 h-4" />
                </button>
                
                <button
                  onClick={() => deleteSchedule(schedule.id)}
                  className="p-2 text-red-600 hover:bg-red-100 dark:hover:bg-red-900/20 rounded-lg"
                  title="Deletar"
                >
                  <Trash2 className="w-4 h-4" />
                </button>
              </div>
            </div>

            <div className="space-y-3">
              {schedule.timeSlots.map((slot, index) => (
                <div
                  key={index}
                  className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4"
                >
                  <div className="flex justify-between items-start">
                    <div>
                      <h4 className="font-medium text-gray-900 dark:text-white">
                        {slot.playlistName}
                      </h4>
                      <div className="flex items-center space-x-4 mt-2 text-sm text-gray-600 dark:text-gray-400">
                        <div className="flex items-center space-x-1">
                          <Clock className="w-4 h-4" />
                          <span>{slot.startTime} - {slot.endTime}</span>
                        </div>
                        <div className="flex items-center space-x-1">
                          <Calendar className="w-4 h-4" />
                          <span>{formatDays(slot.days)}</span>
                        </div>
                        <span>Prioridade: {slot.priority}</span>
                      </div>
                      {slot.description && (
                        <p className="text-sm text-gray-500 dark:text-gray-400 mt-1">
                          {slot.description}
                        </p>
                      )}
                    </div>
                    
                    <span
                      className={`px-2 py-1 rounded-full text-xs font-medium ${
                        slot.isActive
                          ? "bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400"
                          : "bg-gray-100 text-gray-800 dark:bg-gray-900/30 dark:text-gray-400"
                      }`}
                    >
                      {slot.isActive ? "Ativo" : "Inativo"}
                    </span>
                  </div>
                </div>
              ))}
            </div>
          </div>
        ))}
      </div>

      {/* Modal de Formulário */}
      {showForm && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white dark:bg-gray-800 rounded-lg p-6 w-full max-w-4xl max-h-[90vh] overflow-y-auto">
            <div className="flex justify-between items-center mb-6">
              <h3 className="text-xl font-semibold text-gray-900 dark:text-white">
                {editingSchedule ? "Editar Agendamento" : "Novo Agendamento"}
              </h3>
              <button
                onClick={resetForm}
                className="p-2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
              >
                <X className="w-5 h-5" />
              </button>
            </div>

            <form onSubmit={handleSubmit} className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Nome do Agendamento
                  </label>
                  <input
                    type="text"
                    value={formData.name}
                    onChange={(e) =>
                      setFormData(prev => ({ ...prev, name: e.target.value }))
                    }
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white"
                    placeholder="Ex: Horário Comercial"
                    required
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Status
                  </label>
                  <select
                    value={formData.isActive ? "true" : "false"}
                    onChange={(e) =>
                      setFormData(prev => ({ ...prev, isActive: e.target.value === "true" }))
                    }
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white"
                  >
                    <option value="true">Ativo</option>
                    <option value="false">Inativo</option>
                  </select>
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Descrição (opcional)
                </label>
                <textarea
                  value={formData.description}
                  onChange={(e) =>
                    setFormData(prev => ({ ...prev, description: e.target.value }))
                  }
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white"
                  rows={3}
                  placeholder="Descreva quando este agendamento deve ser usado..."
                />
              </div>

              {/* Adicionar Novo Time Slot */}
              <div className="border border-gray-200 dark:border-gray-600 rounded-lg p-4">
                <h4 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
                  Adicionar Horário
                </h4>
                
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      Playlist
                    </label>
                    <select
                      value={newTimeSlot.playlistId || ""}
                      onChange={(e) =>
                        setNewTimeSlot(prev => ({ ...prev, playlistId: e.target.value }))
                      }
                      className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white"
                    >
                      <option value="">Selecione uma playlist</option>
                      {playlists.filter(p => p.isActive).map((playlist) => (
                        <option key={playlist.id} value={playlist.id}>
                          {playlist.name}
                        </option>
                      ))}
                    </select>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      Horário Início
                    </label>
                    <input
                      type="time"
                      value={newTimeSlot.startTime || "09:00"}
                      onChange={(e) =>
                        setNewTimeSlot(prev => ({ ...prev, startTime: e.target.value }))
                      }
                      className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      Horário Fim
                    </label>
                    <input
                      type="time"
                      value={newTimeSlot.endTime || "17:00"}
                      onChange={(e) =>
                        setNewTimeSlot(prev => ({ ...prev, endTime: e.target.value }))
                      }
                      className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white"
                    />
                  </div>
                </div>

                <div className="mb-4">
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Dias da Semana
                  </label>
                  <div className="flex flex-wrap gap-2">
                    {DAYS_OF_WEEK.map((day) => (
                      <label
                        key={day.value}
                        className="flex items-center space-x-2 cursor-pointer"
                      >
                        <input
                          type="checkbox"
                          checked={newTimeSlot.days?.includes(day.value) || false}
                          onChange={(e) => {
                            const days = newTimeSlot.days || [];
                            if (e.target.checked) {
                              setNewTimeSlot(prev => ({
                                ...prev,
                                days: [...days, day.value],
                              }));
                            } else {
                              setNewTimeSlot(prev => ({
                                ...prev,
                                days: days.filter(d => d !== day.value),
                              }));
                            }
                          }}
                          className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                        />
                        <span className="text-sm text-gray-700 dark:text-gray-300">
                          {day.fullName}
                        </span>
                      </label>
                    ))}
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      Prioridade (1-10)
                    </label>
                    <input
                      type="number"
                      min="1"
                      max="10"
                      value={newTimeSlot.priority || 1}
                      onChange={(e) =>
                        setNewTimeSlot(prev => ({ ...prev, priority: parseInt(e.target.value) }))
                      }
                      className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      Status
                    </label>
                    <select
                      value={newTimeSlot.isActive ? "true" : "false"}
                      onChange={(e) =>
                        setNewTimeSlot(prev => ({ ...prev, isActive: e.target.value === "true" }))
                      }
                      className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white"
                    >
                      <option value="true">Ativo</option>
                      <option value="false">Inativo</option>
                    </select>
                  </div>
                </div>

                <div className="mb-4">
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Descrição (opcional)
                  </label>
                  <input
                    type="text"
                    value={newTimeSlot.description || ""}
                    onChange={(e) =>
                      setNewTimeSlot(prev => ({ ...prev, description: e.target.value }))
                    }
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white"
                    placeholder="Ex: Música ambiente para almoço"
                  />
                </div>

                <button
                  type="button"
                  onClick={addTimeSlot}
                  className="bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 flex items-center space-x-2"
                >
                  <Plus className="w-4 h-4" />
                  <span>Adicionar Horário</span>
                </button>
              </div>

              {/* Lista de Time Slots Adicionados */}
              {formData.timeSlots.length > 0 && (
                <div>
                  <h4 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
                    Horários Configurados ({formData.timeSlots.length})
                  </h4>
                  <div className="space-y-3">
                    {formData.timeSlots.map((slot, index) => (
                      <div
                        key={index}
                        className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4 flex justify-between items-center"
                      >
                        <div>
                          <h5 className="font-medium text-gray-900 dark:text-white">
                            {slot.playlistName}
                          </h5>
                          <div className="flex items-center space-x-4 mt-1 text-sm text-gray-600 dark:text-gray-400">
                            <span>{slot.startTime} - {slot.endTime}</span>
                            <span>{formatDays(slot.days)}</span>
                            <span>Prioridade: {slot.priority}</span>
                            <span className={slot.isActive ? "text-green-600" : "text-gray-400"}>
                              {slot.isActive ? "Ativo" : "Inativo"}
                            </span>
                          </div>
                          {slot.description && (
                            <p className="text-sm text-gray-500 dark:text-gray-400 mt-1">
                              {slot.description}
                            </p>
                          )}
                        </div>
                        <button
                          type="button"
                          onClick={() => removeTimeSlot(index)}
                          className="p-2 text-red-600 hover:bg-red-100 dark:hover:bg-red-900/20 rounded-lg"
                          title="Remover"
                        >
                          <Trash2 className="w-4 h-4" />
                        </button>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              <div className="flex justify-end space-x-4 pt-6 border-t border-gray-200 dark:border-gray-600">
                <button
                  type="button"
                  onClick={resetForm}
                  className="px-6 py-2 border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700"
                >
                  Cancelar
                </button>
                <button
                  type="submit"
                  disabled={loading}
                  className="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 flex items-center space-x-2"
                >
                  {loading ? (
                    <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
                  ) : (
                    <Save className="w-4 h-4" />
                  )}
                  <span>{editingSchedule ? "Atualizar" : "Criar"} Agendamento</span>
                </button>
              </div>
            </form>
          </div>
        </div>
      )}
    </div>
  );
}
