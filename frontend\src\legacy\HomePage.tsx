import React, { useState } from "react";
import { motion } from "framer-motion";
import { useNavigate } from "react-router-dom";
import {
  Music,
  Search,
  QrCode,
  Smartphone,
  Users,
  Vote,
  ArrowRight,
  Play,
  Heart,
  Star,
  BarChart3,
  CheckCircle,
  TrendingUp,
  Shield,
  Zap,
  Globe,
  Award,
  Clock,
  Volume2,
  Headphones,
  Settings,
  Eye,
  Target,
  Sparkles,
} from "lucide-react";
import Button from "@/components/ui/Button";

const HomePage: React.FC = () => {
  const navigate = useNavigate();
  const [restaurantCode, setRestaurantCode] = useState("");
  const [email, setEmail] = useState("");

  const handleJoinRestaurant = () => {
    if (restaurantCode.trim()) {
      navigate(`/restaurant/${restaurantCode.trim()}`);
    }
  };

  const handleDemoAccess = () => {
    navigate("/restaurant/demo-restaurant");
  };

  const handleAdminAccess = () => {
    navigate("/admin");
  };

  const handleNewsletterSignup = () => {
    if (email.trim()) {
      // Aqui seria integrado com um serviço de newsletter
      alert("Obrigado! Você será notificado quando lançarmos!");
      setEmail("");
    }
  };

  const mainFeatures = [
    {
      icon: QrCode,
      title: "QR Code Inteligente",
      description:
        "Clientes escaneiam QR codes únicos por mesa e acessam instantaneamente o sistema de sugestões musicais",
      color: "from-blue-500 to-cyan-500",
    },
    {
      icon: Music,
      title: "Integração YouTube Premium",
      description:
        "Acesso completo ao catálogo do YouTube sem anúncios, com suas próprias playlists personalizadas",
      color: "from-red-500 to-pink-500",
    },
    {
      icon: Users,
      title: "Experiência Colaborativa",
      description:
        "Clientes sugerem e votam em músicas em tempo real, criando uma atmosfera única e envolvente",
      color: "from-green-500 to-emerald-500",
    },
    {
      icon: BarChart3,
      title: "Analytics Avançados",
      description:
        "Dashboard completo com métricas, relatórios e insights sobre preferências musicais dos clientes",
      color: "from-purple-500 to-violet-500",
    },
  ];

  const benefits = [
    {
      icon: TrendingUp,
      title: "Aumente o Engajamento",
      description:
        "Clientes ficam mais tempo no restaurante quando participam da experiência musical",
      stats: "+35% tempo de permanência",
    },
    {
      icon: Heart,
      title: "Melhore a Satisfação",
      description:
        "Música personalizada pelos próprios clientes resulta em experiências mais memoráveis",
      stats: "94% aprovação dos clientes",
    },
    {
      icon: Target,
      title: "Reduza Reclamações",
      description:
        "Elimine reclamações sobre música inadequada - os clientes escolhem o que querem ouvir",
      stats: "-80% reclamações sobre música",
    },
    {
      icon: Sparkles,
      title: "Diferencial Competitivo",
      description:
        "Seja o primeiro restaurante da região com tecnologia interativa de música",
      stats: "Inovação garantida",
    },
  ];

  const howItWorks = [
    {
      step: "1",
      title: "Cliente Escaneia QR Code",
      description:
        "Cada mesa tem um QR code único que direciona para a interface de sugestões",
      icon: QrCode,
    },
    {
      step: "2",
      title: "Sugere Músicas",
      description:
        "Busca no YouTube ou nas playlists do restaurante e sugere suas favoritas",
      icon: Search,
    },
    {
      step: "3",
      title: "Comunidade Vota",
      description:
        "Outros clientes votam nas sugestões, criando uma fila democrática",
      icon: Vote,
    },
    {
      step: "4",
      title: "Música Toca Automaticamente",
      description:
        "Sistema reproduz as músicas mais votadas sem intervenção manual",
      icon: Play,
    },
  ];

  const testimonials = [
    {
      name: "Carlos Silva",
      role: "Proprietário - Restaurante Sabor & Arte",
      content:
        "Nossos clientes adoram! O tempo de permanência aumentou 40% e as avaliações melhoraram significativamente.",
      rating: 5,
    },
    {
      name: "Ana Costa",
      role: "Gerente - Bistrô Harmonia",
      content:
        "Revolucionou nosso ambiente. Os clientes se sentem parte da experiência e sempre comentam sobre a tecnologia.",
      rating: 5,
    },
    {
      name: "Roberto Mendes",
      role: "Chef - Casa do Sabor",
      content:
        "Eliminou completamente as reclamações sobre música. Agora os próprios clientes criam a atmosfera perfeita.",
      rating: 5,
    },
  ];

  const pricingPlans = [
    {
      name: "Starter",
      price: "R$ 97",
      period: "/mês",
      description: "Perfeito para restaurantes pequenos",
      features: [
        "Até 10 mesas",
        "QR codes ilimitados",
        "Integração YouTube",
        "Dashboard básico",
        "Suporte por email",
      ],
      popular: false,
    },
    {
      name: "Professional",
      price: "R$ 197",
      period: "/mês",
      description: "Ideal para restaurantes médios",
      features: [
        "Até 30 mesas",
        "QR codes ilimitados",
        "Integração YouTube Premium",
        "Analytics avançados",
        "Moderação inteligente",
        "Suporte prioritário",
      ],
      popular: true,
    },
    {
      name: "Enterprise",
      price: "R$ 397",
      period: "/mês",
      description: "Para redes e grandes restaurantes",
      features: [
        "Mesas ilimitadas",
        "Multi-restaurantes",
        "API personalizada",
        "Relatórios customizados",
        "Suporte 24/7",
        "Treinamento incluso",
      ],
      popular: false,
    },
  ];

  const stats = [
    { number: "1000+", label: "Músicas Sugeridas" },
    { number: "50+", label: "Restaurantes Ativos" },
    { number: "5000+", label: "Votos Realizados" },
    { number: "98%", label: "Satisfação" },
  ];

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900">
      {/* Header */}
      <header className="relative overflow-hidden">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="text-center"
          >
            {/* Logo */}
            <div className="flex justify-center mb-8">
              <div className="w-20 h-20 bg-gradient-to-br from-primary-500 to-primary-600 rounded-full flex items-center justify-center shadow-xl">
                <Music className="w-10 h-10 text-white" />
              </div>
            </div>

            {/* Título principal */}
            <h1 className="text-4xl md:text-6xl font-bold text-gray-900 dark:text-white mb-6">
              Playlist
              <span className="text-transparent bg-clip-text bg-gradient-to-r from-primary-600 to-purple-600">
                {" "}
                Interativa
              </span>
            </h1>

            <p className="text-xl text-gray-600 dark:text-gray-300 mb-8 max-w-3xl mx-auto">
              Transforme a experiência musical do seu restaurante. Clientes
              sugerem músicas, votam em tempo real e criam uma playlist
              colaborativa única.
            </p>

            {/* CTA Principal */}
            <div className="flex flex-col sm:flex-row gap-4 justify-center items-center mb-12">
              <div className="flex space-x-3">
                <input
                  type="text"
                  placeholder="Digite o código do restaurante"
                  value={restaurantCode}
                  onChange={(e) => setRestaurantCode(e.target.value)}
                  onKeyPress={(e) =>
                    e.key === "Enter" && handleJoinRestaurant()
                  }
                  className="input w-64"
                />
                <Button
                  onClick={handleJoinRestaurant}
                  disabled={!restaurantCode.trim()}
                  size="lg"
                  icon={<ArrowRight className="w-5 h-5" />}
                >
                  Entrar
                </Button>
              </div>
            </div>

            {/* QR Code Info */}
            <div className="inline-flex items-center space-x-2 text-sm text-gray-500 dark:text-gray-400 bg-white/50 dark:bg-gray-800/50 backdrop-blur-sm rounded-full px-4 py-2">
              <QrCode className="w-4 h-4" />
              <span>Ou escaneie o QR Code na mesa do restaurante</span>
            </div>
          </motion.div>
        </div>

        {/* Background decorativo */}
        <div className="absolute inset-0 -z-10">
          <div className="absolute top-0 left-1/4 w-72 h-72 bg-purple-300 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-blob"></div>
          <div className="absolute top-0 right-1/4 w-72 h-72 bg-yellow-300 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-blob animation-delay-2000"></div>
          <div className="absolute -bottom-8 left-1/3 w-72 h-72 bg-pink-300 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-blob animation-delay-4000"></div>
        </div>
      </header>

      {/* Features */}
      <section className="py-20 bg-white dark:bg-gray-800">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            className="text-center mb-16"
          >
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 dark:text-white mb-4">
              Como Funciona
            </h2>
            <p className="text-xl text-gray-600 dark:text-gray-300 max-w-2xl mx-auto">
              Uma experiência musical colaborativa em quatro passos simples
            </p>
          </motion.div>

          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
            {howItWorks.map((feature, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ delay: index * 0.1 }}
                className="text-center group"
              >
                <div className="w-16 h-16 bg-gradient-to-br from-primary-500 to-primary-600 rounded-full flex items-center justify-center mx-auto mb-4 group-hover:scale-110 transition-transform">
                  <feature.icon className="w-8 h-8 text-white" />
                </div>
                <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-2">
                  {feature.title}
                </h3>
                <p className="text-gray-600 dark:text-gray-300">
                  {feature.description}
                </p>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Stats */}
      <section className="py-20 bg-gradient-to-r from-primary-600 to-purple-600">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-2 md:grid-cols-4 gap-8">
            {stats.map((stat, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, scale: 0.5 }}
                whileInView={{ opacity: 1, scale: 1 }}
                viewport={{ once: true }}
                transition={{ delay: index * 0.1 }}
                className="text-center"
              >
                <div className="text-3xl md:text-4xl font-bold text-white mb-2">
                  {stat.number}
                </div>
                <div className="text-blue-100">{stat.label}</div>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Final */}
      <section className="py-20 bg-gray-50 dark:bg-gray-900">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
          >
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 dark:text-white mb-4">
              Pronto para Começar?
            </h2>
            <p className="text-xl text-gray-600 dark:text-gray-300 mb-8">
              Entre em um restaurante participante e comece a sugerir suas
              músicas favoritas
            </p>

            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button
                onClick={() => navigate("/admin")}
                variant="outline"
                size="lg"
              >
                Sou Restaurante
              </Button>

              <Button
                onClick={() => navigate("/analytics")}
                variant="outline"
                size="lg"
                icon={<BarChart3 className="w-5 h-5" />}
              >
                Ver Analytics
              </Button>

              <Button
                onClick={() => window.scrollTo({ top: 0, behavior: "smooth" })}
                size="lg"
                icon={<Smartphone className="w-5 h-5" />}
              >
                Sou Cliente
              </Button>
            </div>
          </motion.div>
        </div>
      </section>

      {/* Footer */}
      <footer className="bg-white dark:bg-gray-800 border-t border-gray-200 dark:border-gray-700">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="flex flex-col md:flex-row justify-between items-center">
            <div className="flex items-center space-x-2 mb-4 md:mb-0">
              <Music className="w-6 h-6 text-primary-600" />
              <span className="text-lg font-semibold text-gray-900 dark:text-white">
                Restaurant Playlist
              </span>
            </div>

            <div className="text-sm text-gray-500 dark:text-gray-400">
              © 2024 Restaurant Playlist System. Todos os direitos reservados.
            </div>
          </div>
        </div>
      </footer>
    </div>
  );
};

export default HomePage;
