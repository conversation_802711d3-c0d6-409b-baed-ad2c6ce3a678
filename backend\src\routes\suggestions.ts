import { Router, Request, Response } from "express";
import { body, query, param, validationResult } from "express-validator";
import { In } from "typeorm";
import { AppDataSource } from "../config/database";
import {
  Suggestion,
  SuggestionStatus,
  SuggestionSource,
} from "../models/Suggestion";
import { Vote, VoteType } from "../models/Vote";
import { Restaurant } from "../models/Restaurant";
import { ClientSession } from "../models/ClientSession";
import { ModerationRule } from "../models/ModerationRule";
import { YouTubeService } from "../services/YouTubeService";
import { redisClient } from "../config/redis";
import { notificationService } from "../services/NotificationService";
import { votingPlaylistService } from "../services/VotingPlaylistService";
import {
  authMiddleware,
  optionalAuth,
  sessionMiddleware,
  requireModerator,
} from "../middleware/auth";
import {
  asyncHandler,
  createValidationError,
  NotFound<PERSON>rror,
  ConflictError,
  ForbiddenError,
} from "../middleware/errorHandler";
import { logger, logModeration } from "../utils/logger";

const router = Router();
const youtubeService = new YouTubeService();

/**
 * @swagger
 * /api/v1/suggestions:
 *   post:
 *     summary: Criar nova sugestão de música
 *     tags: [Sugestões]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - youtubeVideoId
 *               - restaurantId
 *             properties:
 *               youtubeVideoId:
 *                 type: string
 *                 description: ID do vídeo no YouTube
 *               restaurantId:
 *                 type: string
 *                 format: uuid
 *                 description: ID do restaurante
 *               playlistId:
 *                 type: string
 *                 format: uuid
 *                 description: ID da playlist (opcional)
 *     headers:
 *       X-Session-ID:
 *         description: ID da sessão do cliente
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *     responses:
 *       201:
 *         description: Sugestão criada com sucesso
 *       400:
 *         description: Dados inválidos
 *       409:
 *         description: Sugestão duplicada
 *       429:
 *         description: Limite de sugestões excedido
 */
router.post(
  "/",
  [
    body("youtubeVideoId")
      .isString()
      .isLength({ min: 11, max: 11 })
      .withMessage("ID do vídeo deve ter 11 caracteres"),
    body("restaurantId")
      .notEmpty()
      .withMessage("ID do restaurante é obrigatório"),
    body("playlistId")
      .optional()
      .isUUID()
      .withMessage("ID da playlist deve ser um UUID válido"),
  ],
  sessionMiddleware,
  optionalAuth,
  asyncHandler(async (req: Request, res: Response) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      throw createValidationError(errors.array());
    }

    const { youtubeVideoId, restaurantId, playlistId } = req.body;
    const sessionId = req.sessionId!;
    const userId = req.user?.id;

    const suggestionRepository = AppDataSource.getRepository(Suggestion);
    const restaurantRepository = AppDataSource.getRepository(Restaurant);
    const sessionRepository = AppDataSource.getRepository(ClientSession);

    // Verificar se restaurante existe e está ativo
    const restaurant = await restaurantRepository.findOne({
      where: { id: restaurantId, isActive: true },
    });

    if (!restaurant) {
      throw new NotFoundError("Restaurante não encontrado ou inativo");
    }

    // Verificar se restaurante está aberto
    if (!restaurant.isOpen()) {
      throw new ForbiddenError("Restaurante fechado no momento");
    }

    // Obter ou criar sessão do cliente
    let session = await sessionRepository.findOne({
      where: { sessionToken: sessionId, restaurant: { id: restaurantId } },
    });

    if (!session) {
      session = sessionRepository.create({
        sessionToken: sessionId,
        restaurant,
        ipAddress: req.ip,
        userAgent: req.get("User-Agent"),
        lastActivity: new Date(),
        isActive: true,
      });
      await sessionRepository.save(session);
    }

    // Verificar limites de sugestões
    const maxSuggestionsPerUser = restaurant.getSetting(
      "moderation.maxSuggestionsPerUser",
      3
    );
    const maxSuggestionsPerHour = restaurant.getSetting(
      "moderation.maxSuggestionsPerHour",
      10
    );

    if (!session.canMakeSuggestion(maxSuggestionsPerHour)) {
      return res.status(429).json({
        error: "Limite de sugestões por hora excedido",
        code: "SUGGESTION_LIMIT_EXCEEDED",
        limit: maxSuggestionsPerHour,
      });
    }

    // Verificar se já existe sugestão pendente/aprovada para este vídeo
    const existingSuggestion = await suggestionRepository.findOne({
      where: {
        youtubeVideoId,
        restaurant: { id: restaurantId },
        status: In([SuggestionStatus.PENDING, SuggestionStatus.APPROVED]),
      },
    });

    if (existingSuggestion) {
      throw new ConflictError("Esta música já foi sugerida e está na fila");
    }

    // Obter informações do vídeo do YouTube
    const videoInfo = await youtubeService.getVideoInfo(youtubeVideoId);
    if (!videoInfo) {
      return res.status(400).json({
        error: "Vídeo não encontrado ou não disponível",
        code: "VIDEO_NOT_AVAILABLE",
      });
    }

    // Criar sugestão
    const suggestion = suggestionRepository.create({
      youtubeVideoId: videoInfo.youtubeVideoId,
      title: videoInfo.title,
      artist: videoInfo.artist,
      channelName: videoInfo.channelName,
      duration: videoInfo.duration,
      thumbnailUrl: videoInfo.thumbnailUrl,
      description: videoInfo.description,
      metadata: videoInfo.metadata,
      restaurant,
      clientSessionId: sessionId,
      clientIp: req.ip,
      clientUserAgent: req.get("User-Agent"),
      source: userId ? SuggestionSource.CLIENT : SuggestionSource.CLIENT,
      suggestedBy: req.user,
    });

    // Aplicar regras de moderação
    const moderationResult = await applyModerationRules(suggestion, restaurant);
    suggestion.status = moderationResult.status;
    suggestion.moderationFlags = moderationResult.flags;

    if (moderationResult.autoRejected) {
      suggestion.moderationReason = moderationResult.reason;
    }

    await suggestionRepository.save(suggestion);

    // Atualizar sessão
    session.incrementSuggestions();
    await sessionRepository.save(session);

    // Atualizar estatísticas em tempo real
    await redisClient.incrementDailyStats(restaurantId, "suggestions");

    // Se aprovada automaticamente, atualizar fila
    if (suggestion.status === SuggestionStatus.APPROVED) {
      await updatePlayQueue(restaurantId);
    }

    // Notificar via WebSocket
    const io = req.app.get("io");
    io.to(`restaurant-${restaurantId}`).emit(
      "new-suggestion",
      suggestion.toPublicJSON()
    );

    // Enviar notificação em tempo real
    await notificationService.notifyNewSuggestion(restaurantId, {
      suggestionId: suggestion.id,
      songTitle: suggestion.title,
      artist: suggestion.artist,
      clientName: session.clientName || "Cliente",
      status: suggestion.status,
      autoApproved: suggestion.status === SuggestionStatus.APPROVED,
      autoRejected: moderationResult.autoRejected,
    });

    logger.info("Sugestão criada", {
      suggestionId: suggestion.id,
      youtubeVideoId,
      restaurantId,
      sessionId,
      status: suggestion.status,
    });

    res.status(201).json({
      suggestion: suggestion.toPublicJSON(),
      moderationResult: {
        autoApproved: suggestion.status === SuggestionStatus.APPROVED,
        autoRejected: moderationResult.autoRejected,
        reason: moderationResult.reason,
      },
    });
  })
);

// Função auxiliar para aplicar regras de moderação
async function applyModerationRules(
  suggestion: Suggestion,
  restaurant: Restaurant
) {
  const ruleRepository = AppDataSource.getRepository(ModerationRule);

  const rules = await ruleRepository.find({
    where: {
      restaurant: { id: restaurant.id },
      isActive: true,
    },
    order: { priority: "ASC" },
  });

  const flags: any = {};
  let autoRejected = false;
  let reason = "";

  for (const rule of rules) {
    if (rule.matches(suggestion)) {
      rule.recordMatch();
      await ruleRepository.save(rule);

      switch (rule.action) {
        case "auto_reject":
          autoRejected = true;
          reason = `Rejeitada automaticamente: ${rule.getDescription()}`;
          break;
        case "flag_for_review":
          flags[rule.ruleType] = rule.getDescription();
          break;
      }
    }
  }

  return {
    status: autoRejected
      ? SuggestionStatus.REJECTED
      : restaurant.getSetting("moderation.autoApprove", false)
      ? SuggestionStatus.APPROVED
      : SuggestionStatus.PENDING,
    flags,
    autoRejected,
    reason,
  };
}

// Função auxiliar para atualizar fila de reprodução
async function updatePlayQueue(restaurantId: string) {
  const suggestionRepository = AppDataSource.getRepository(Suggestion);

  const approvedSuggestions = await suggestionRepository.find({
    where: {
      restaurant: { id: restaurantId },
      status: SuggestionStatus.APPROVED,
    },
    order: { createdAt: "ASC" },
  });

  // Calcular posições na fila baseado em votos e tempo
  const sortedSuggestions = approvedSuggestions.sort(
    (a, b) => b.calculateScore() - a.calculateScore()
  );

  // Atualizar posições
  for (let i = 0; i < sortedSuggestions.length; i++) {
    const suggestion = sortedSuggestions[i];
    suggestion.queuePosition = i + 1;
    await suggestionRepository.save(suggestion);
  }

  // Atualizar cache da fila
  const queueData = sortedSuggestions.map((s) => s.toPublicJSON());
  await redisClient.updatePlayQueue(restaurantId, queueData);

  return queueData;
}

/**
 * @swagger
 * /api/v1/suggestions/{restaurantId}:
 *   get:
 *     summary: Listar sugestões de um restaurante
 *     tags: [Sugestões]
 *     parameters:
 *       - in: path
 *         name: restaurantId
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *       - in: query
 *         name: status
 *         schema:
 *           type: string
 *           enum: [pending, approved, rejected, playing, played, skipped]
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           minimum: 1
 *           default: 1
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           minimum: 1
 *           maximum: 100
 *           default: 20
 *     responses:
 *       200:
 *         description: Lista de sugestões
 */
router.get(
  "/:restaurantId",
  [
    param("restaurantId")
      .notEmpty()
      .withMessage("ID do restaurante é obrigatório"),
    query("status")
      .optional()
      .isIn(["pending", "approved", "rejected", "playing", "played", "skipped"])
      .withMessage("Status inválido"),
    query("page")
      .optional()
      .isInt({ min: 1 })
      .withMessage("Página deve ser um número positivo"),
    query("limit")
      .optional()
      .isInt({ min: 1, max: 100 })
      .withMessage("Limite deve ser entre 1 e 100"),
    query("genres")
      .optional()
      .isString()
      .withMessage("Gêneros devem ser uma string separada por vírgulas"),
    query("moods")
      .optional()
      .isString()
      .withMessage("Humores devem ser uma string separada por vírgulas"),
  ],
  optionalAuth,
  asyncHandler(async (req: Request, res: Response) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      throw createValidationError(errors.array());
    }

    const { restaurantId } = req.params;
    const { status, page = 1, limit = 20, genres, moods } = req.query as any;

    const suggestionRepository = AppDataSource.getRepository(Suggestion);

    // Construir filtros
    const where: any = { restaurant: { id: restaurantId } };
    if (status) {
      where.status = status;
    }

    // Filtros de gênero e humor (usando metadata JSON)
    const additionalFilters: any[] = [];

    if (genres) {
      const genreList = genres
        .split(",")
        .map((g: string) => g.trim().toLowerCase());
      // Buscar por gênero no campo metadata ou em um campo específico se existir
      additionalFilters.push(
        `(LOWER(metadata->>'genre') IN (${genreList
          .map((g: string) => `'${g}'`)
          .join(",")}) OR
          LOWER(artist) LIKE ANY(ARRAY[${genreList
            .map((g: string) => `'%${g}%'`)
            .join(",")}]))`
      );
    }

    if (moods) {
      const moodList = moods
        .split(",")
        .map((m: string) => m.trim().toLowerCase());
      additionalFilters.push(
        `LOWER(metadata->>'mood') IN (${moodList
          .map((m: string) => `'${m}'`)
          .join(",")})`
      );
    }

    // Buscar sugestões com paginação
    let queryBuilder = suggestionRepository
      .createQueryBuilder("suggestion")
      .leftJoinAndSelect("suggestion.votes", "votes")
      .where("suggestion.restaurant_id = :restaurantId", { restaurantId });

    if (status) {
      queryBuilder = queryBuilder.andWhere("suggestion.status = :status", {
        status,
      });
    }

    // Aplicar filtros adicionais
    additionalFilters.forEach((filter, index) => {
      queryBuilder = queryBuilder.andWhere(filter);
    });

    const [suggestions, total] = await queryBuilder
      .orderBy("suggestion.createdAt", "DESC")
      .skip((page - 1) * limit)
      .take(limit)
      .getManyAndCount();

    const totalPages = Math.ceil(total / limit);

    res.json({
      suggestions: suggestions.map((s) => s.toPublicJSON()),
      pagination: {
        page,
        limit,
        total,
        totalPages,
        hasNext: page < totalPages,
        hasPrev: page > 1,
      },
    });
  })
);

/**
 * @swagger
 * /api/v1/suggestions/{restaurantId}/queue:
 *   get:
 *     summary: Obter fila de reprodução atual
 *     tags: [Sugestões]
 *     parameters:
 *       - in: path
 *         name: restaurantId
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *     responses:
 *       200:
 *         description: Fila de reprodução atual
 */
router.get(
  "/:restaurantId/queue",
  [
    param("restaurantId")
      .notEmpty()
      .withMessage("ID do restaurante é obrigatório"),
  ],
  optionalAuth,
  asyncHandler(async (req: Request, res: Response) => {
    const { restaurantId } = req.params;

    // Tentar obter da cache primeiro
    let queue = await redisClient.getPlayQueue(restaurantId);

    if (!queue) {
      // Se não estiver em cache, recalcular
      queue = await updatePlayQueue(restaurantId);
    }

    res.json({
      queue,
      totalItems: queue.length,
      estimatedDuration: queue.reduce(
        (total, item) => total + (item.duration || 0),
        0
      ),
    });
  })
);

/**
 * @swagger
 * /api/v1/suggestions/{id}/vote:
 *   post:
 *     summary: Votar em uma sugestão
 *     tags: [Sugestões]
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - voteType
 *             properties:
 *               voteType:
 *                 type: string
 *                 enum: [up, down]
 *     headers:
 *       X-Session-ID:
 *         description: ID da sessão do cliente
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *     responses:
 *       200:
 *         description: Voto registrado com sucesso
 *       409:
 *         description: Usuário já votou nesta sugestão
 */
router.post(
  "/:id/vote",
  [
    param("id").isUUID().withMessage("ID da sugestão deve ser um UUID válido"),
    body("voteType")
      .isIn(["up", "down"])
      .withMessage("Tipo de voto deve ser up ou down"),
  ],
  sessionMiddleware,
  optionalAuth,
  asyncHandler(async (req: Request, res: Response) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      throw createValidationError(errors.array());
    }

    const { id } = req.params;
    const { voteType } = req.body;
    const sessionId = req.sessionId!;
    const userId = req.user?.id;

    const suggestionRepository = AppDataSource.getRepository(Suggestion);
    const voteRepository = AppDataSource.getRepository(Vote);
    const sessionRepository = AppDataSource.getRepository(ClientSession);

    // Buscar sugestão
    const suggestion = await suggestionRepository.findOne({
      where: { id },
      relations: ["restaurant"],
    });

    if (!suggestion) {
      throw new NotFoundError("Sugestão não encontrada");
    }

    // Verificar se pode ser votada
    if (!suggestion.canBeVoted()) {
      throw new ForbiddenError("Esta sugestão não pode mais receber votos");
    }

    // Verificar se já votou
    const existingVote = await voteRepository.findOne({
      where: userId
        ? { suggestion: { id }, user: { id: userId } }
        : { suggestion: { id }, clientSessionId: sessionId },
    });

    if (existingVote) {
      // Se é o mesmo tipo de voto, remover
      if (existingVote.voteType === voteType) {
        await voteRepository.remove(existingVote);

        // Atualizar contadores
        if (voteType === VoteType.UP) {
          suggestion.upvotes--;
        } else {
          suggestion.downvotes--;
        }
        suggestion.voteCount = suggestion.upvotes - suggestion.downvotes;

        await suggestionRepository.save(suggestion);

        return res.json({
          message: "Voto removido",
          suggestion: suggestion.toPublicJSON(),
        });
      } else {
        // Trocar tipo de voto
        existingVote.voteType = voteType as VoteType;
        await voteRepository.save(existingVote);

        // Atualizar contadores
        if (voteType === VoteType.UP) {
          suggestion.upvotes++;
          suggestion.downvotes--;
        } else {
          suggestion.upvotes--;
          suggestion.downvotes++;
        }
        suggestion.voteCount = suggestion.upvotes - suggestion.downvotes;

        await suggestionRepository.save(suggestion);

        return res.json({
          message: "Voto alterado",
          suggestion: suggestion.toPublicJSON(),
        });
      }
    }

    // Criar novo voto usando query builder para evitar problemas de relação
    await voteRepository
      .createQueryBuilder()
      .insert()
      .into(Vote)
      .values({
        voteType: voteType as VoteType,
        suggestion: { id: suggestion.id },
        clientSessionId: sessionId,
        clientIp: req.ip,
        clientUserAgent: req.get("User-Agent"),
        user: req.user ? { id: req.user.id } : undefined,
      })
      .execute();

    // Atualizar contadores
    if (voteType === VoteType.UP) {
      suggestion.upvotes++;
    } else {
      suggestion.downvotes++;
    }
    suggestion.voteCount = suggestion.upvotes - suggestion.downvotes;

    await suggestionRepository.save(suggestion);

    // Atualizar sessão
    const session = await sessionRepository.findOne({
      where: { sessionToken: sessionId },
    });
    if (session) {
      session.incrementVotes();
      await sessionRepository.save(session);
    }

    // Atualizar estatísticas
    await redisClient.incrementDailyStats(suggestion.restaurant.id, "votes");

    // Atualizar fila se necessário
    if (suggestion.status === SuggestionStatus.APPROVED) {
      await updatePlayQueue(suggestion.restaurant.id);
    }

    // Notificar via WebSocket
    const io = req.app.get("io");
    io.to(`restaurant-${suggestion.restaurant.id}`).emit("vote-update", {
      suggestionId: suggestion.id,
      voteCount: suggestion.voteCount,
      upvotes: suggestion.upvotes,
      downvotes: suggestion.downvotes,
    });

    // Enviar notificação em tempo real
    await notificationService.notifyVote(suggestion.restaurant.id, {
      suggestionId: suggestion.id,
      songTitle: suggestion.title,
      artist: suggestion.artist,
      voteType: voteType,
      voteCount: suggestion.voteCount,
      upvotes: suggestion.upvotes,
      downvotes: suggestion.downvotes,
      positionChanged: true, // Seria calculado baseado na mudança de posição na fila
    });

    // 🎯 NOVA FUNCIONALIDADE: Processar voto e atualizar playlist automaticamente
    try {
      await votingPlaylistService.processVoteAndUpdatePlaylist(
        suggestion.restaurant.id,
        suggestion.id,
        voteType === VoteType.UP ? 'up' : 'down'
      );
    } catch (error) {
      console.error('Erro ao processar voto para reordenação:', error);
      // Não falhar a votação se a reordenação falhar
    }

    res.json({
      message: "Voto registrado com sucesso",
      suggestion: suggestion.toPublicJSON(),
    });
  })
);

export default router;
