import React, { useState, useEffect } from "react";
import { motion, AnimatePresence } from "framer-motion";
import {
  Gift,
  Trophy,
  Star,
  Clock,
  CreditCard,
  Music,
  Zap,
  Share2,
  CheckCircle,
  XCircle,
  Calendar,
  Award,
} from "lucide-react";

interface Reward {
  id: string;
  type: "discount" | "free_song" | "priority_queue" | "badge" | "custom";
  title: string;
  description: string;
  icon: string;
  formattedValue: string;
  awardedFor:
    | "daily_winner"
    | "weekly_winner"
    | "monthly_winner"
    | "best_performance"
    | "most_votes"
    | "participation";
  awardedDate: string;
  expiresAt?: string;
  status: "active" | "used" | "expired" | "cancelled";
  isActive: boolean;
  canBeUsed: boolean;
  usageCount: number;
  usageLimit?: number;
  performanceData?: {
    songTitle?: string;
    artist?: string;
    averageRating?: number;
    totalVotes?: number;
    rank?: number;
    competitionDate?: string;
  };
}

interface RewardsPanelProps {
  sessionId: string;
  restaurantId: string;
  isOpen: boolean;
  onClose: () => void;
}

const RewardsPanel: React.FC<RewardsPanelProps> = ({
  sessionId,
  restaurantId,
  isOpen,
  onClose,
}) => {
  const [rewards, setRewards] = useState<Reward[]>([]);
  const [loading, setLoading] = useState(false);
  const [activeRewards, setActiveRewards] = useState(0);

  useEffect(() => {
    if (isOpen && sessionId) {
      loadRewards();
    }
  }, [isOpen, sessionId]);

  const loadRewards = async () => {
    try {
      setLoading(true);

      const response = await fetch(
        `http://localhost:8001/api/v1/rewards/client/${sessionId}`
      );

      if (response.ok) {
        const data = await response.json();
        setRewards(data.rewards || []);
        setActiveRewards(data.active || 0);
      }
    } catch (error) {
      console.error("Erro ao carregar prêmios:", error);
    } finally {
      setLoading(false);
    }
  };

  const useReward = async (rewardId: string) => {
    try {
      const response = await fetch(
        `http://localhost:8001/api/v1/rewards/use/${rewardId}`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({ sessionId }),
        }
      );

      if (response.ok) {
        const data = await response.json();

        // Atualizar lista de prêmios
        await loadRewards();

        // Mostrar notificação de sucesso
        alert(data.message);
      } else {
        const errorData = await response.json();
        alert(errorData.message || "Erro ao usar prêmio");
      }
    } catch (error) {
      console.error("Erro ao usar prêmio:", error);
      alert("Erro ao usar prêmio");
    }
  };

  const shareReward = (reward: Reward) => {
    const shareText = `🏆 Ganhei um prêmio! ${reward.title} - ${reward.formattedValue}`;

    if (navigator.share) {
      navigator.share({
        title: "Meu Prêmio!",
        text: shareText,
        url: window.location.href,
      });
    } else {
      // Fallback para copiar para clipboard
      navigator.clipboard.writeText(shareText).then(() => {
        alert("Texto copiado para a área de transferência!");
      });
    }
  };

  const getRewardIcon = (type: string) => {
    const icons = {
      discount: CreditCard,
      free_song: Music,
      priority_queue: Zap,
      badge: Award,
      custom: Gift,
    };
    return icons[type as keyof typeof icons] || Gift;
  };

  const getRewardColor = (type: string) => {
    const colors = {
      discount: "text-green-600 bg-green-100",
      free_song: "text-blue-600 bg-blue-100",
      priority_queue: "text-yellow-600 bg-yellow-100",
      badge: "text-purple-600 bg-purple-100",
      custom: "text-pink-600 bg-pink-100",
    };
    return colors[type as keyof typeof colors] || "text-gray-600 bg-gray-100";
  };

  const getAwardedForLabel = (awardedFor: string) => {
    const labels = {
      daily_winner: "Campeão do Dia",
      weekly_winner: "Campeão da Semana",
      monthly_winner: "Campeão do Mês",
      best_performance: "Melhor Performance",
      most_votes: "Mais Votado",
      participation: "Participação",
    };
    return labels[awardedFor as keyof typeof labels] || awardedFor;
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString("pt-BR", {
      day: "2-digit",
      month: "2-digit",
      year: "numeric",
    });
  };

  const getDaysUntilExpiry = (expiresAt: string) => {
    const now = new Date();
    const expiry = new Date(expiresAt);
    const diffTime = expiry.getTime() - now.getTime();
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    return diffDays;
  };

  if (!isOpen) return null;

  return (
    <AnimatePresence>
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        exit={{ opacity: 0 }}
        className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4"
        onClick={onClose}
      >
        <motion.div
          initial={{ scale: 0.9, opacity: 0 }}
          animate={{ scale: 1, opacity: 1 }}
          exit={{ scale: 0.9, opacity: 0 }}
          className="bg-white rounded-2xl shadow-2xl max-w-2xl w-full max-h-[80vh] overflow-hidden"
          onClick={(e) => e.stopPropagation()}
        >
          {/* Header */}
          <div className="bg-gradient-to-r from-purple-600 to-blue-600 p-6 text-white">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-3">
                <Trophy className="w-8 h-8" />
                <div>
                  <h2 className="text-2xl font-bold">Meus Prêmios</h2>
                  <p className="text-purple-100">
                    {activeRewards} prêmios ativos • {rewards.length} total
                  </p>
                </div>
              </div>

              <button
                onClick={onClose}
                className="p-2 hover:bg-white/20 rounded-full transition-colors"
              >
                <XCircle className="w-6 h-6" />
              </button>
            </div>
          </div>

          {/* Content */}
          <div className="p-6 max-h-[60vh] overflow-y-auto">
            {loading ? (
              <div className="flex items-center justify-center py-12">
                <div className="w-8 h-8 border-2 border-purple-600 border-t-transparent rounded-full animate-spin" />
                <span className="ml-3 text-gray-600">
                  Carregando prêmios...
                </span>
              </div>
            ) : rewards.length === 0 ? (
              <div className="text-center py-12">
                <Gift className="w-16 h-16 text-gray-400 mx-auto mb-4" />
                <h3 className="text-xl font-semibold text-gray-900 mb-2">
                  Nenhum prêmio ainda
                </h3>
                <p className="text-gray-600">
                  Participe das competições para ganhar prêmios incríveis!
                </p>
              </div>
            ) : (
              <div className="space-y-4">
                {rewards.map((reward) => {
                  const IconComponent = getRewardIcon(reward.type);
                  const colorClass = getRewardColor(reward.type);
                  const daysUntilExpiry = reward.expiresAt
                    ? getDaysUntilExpiry(reward.expiresAt)
                    : null;

                  return (
                    <motion.div
                      key={reward.id}
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      className={`p-4 rounded-xl border-2 transition-all ${
                        reward.isActive
                          ? "border-green-200 bg-green-50"
                          : reward.status === "used"
                          ? "border-gray-200 bg-gray-50"
                          : "border-red-200 bg-red-50"
                      }`}
                    >
                      <div className="flex items-start justify-between">
                        <div className="flex items-start space-x-4">
                          <div className={`p-3 rounded-full ${colorClass}`}>
                            <IconComponent className="w-6 h-6" />
                          </div>

                          <div className="flex-1">
                            <div className="flex items-center space-x-2 mb-1">
                              <h3 className="font-bold text-gray-900">
                                {reward.title}
                              </h3>
                              {reward.performanceData?.rank &&
                                reward.performanceData.rank <= 3 && (
                                  <div className="flex items-center space-x-1">
                                    {reward.performanceData.rank === 1 && (
                                      <span>🥇</span>
                                    )}
                                    {reward.performanceData.rank === 2 && (
                                      <span>🥈</span>
                                    )}
                                    {reward.performanceData.rank === 3 && (
                                      <span>🥉</span>
                                    )}
                                  </div>
                                )}
                            </div>

                            <p className="text-gray-600 text-sm mb-2">
                              {reward.description}
                            </p>

                            <div className="flex items-center space-x-4 text-xs text-gray-500">
                              <div className="flex items-center space-x-1">
                                <Calendar className="w-3 h-3" />
                                <span>{formatDate(reward.awardedDate)}</span>
                              </div>

                              <div className="flex items-center space-x-1">
                                <Star className="w-3 h-3" />
                                <span>
                                  {getAwardedForLabel(reward.awardedFor)}
                                </span>
                              </div>

                              {reward.performanceData?.songTitle && (
                                <div className="flex items-center space-x-1">
                                  <Music className="w-3 h-3" />
                                  <span>
                                    {reward.performanceData.songTitle}
                                  </span>
                                </div>
                              )}
                            </div>

                            {reward.performanceData?.averageRating && (
                              <div className="mt-2 flex items-center space-x-2">
                                <div className="flex items-center space-x-1">
                                  <Star className="w-4 h-4 text-yellow-500" />
                                  <span className="text-sm font-medium">
                                    {reward.performanceData.averageRating.toFixed(
                                      1
                                    )}
                                  </span>
                                </div>
                                <span className="text-xs text-gray-500">
                                  ({reward.performanceData.totalVotes} votos)
                                </span>
                              </div>
                            )}

                            {daysUntilExpiry !== null && (
                              <div className="mt-2">
                                {daysUntilExpiry > 0 ? (
                                  <div className="flex items-center space-x-1 text-xs text-orange-600">
                                    <Clock className="w-3 h-3" />
                                    <span>
                                      Expira em {daysUntilExpiry} dias
                                    </span>
                                  </div>
                                ) : (
                                  <div className="flex items-center space-x-1 text-xs text-red-600">
                                    <XCircle className="w-3 h-3" />
                                    <span>Expirado</span>
                                  </div>
                                )}
                              </div>
                            )}
                          </div>
                        </div>

                        <div className="flex flex-col items-end space-y-2">
                          <div className="text-right">
                            <div className="font-bold text-lg text-purple-600">
                              {reward.formattedValue}
                            </div>
                            <div
                              className={`text-xs px-2 py-1 rounded-full ${
                                reward.isActive
                                  ? "bg-green-100 text-green-800"
                                  : reward.status === "used"
                                  ? "bg-gray-100 text-gray-800"
                                  : "bg-red-100 text-red-800"
                              }`}
                            >
                              {reward.status === "active"
                                ? "Ativo"
                                : reward.status === "used"
                                ? "Usado"
                                : reward.status === "expired"
                                ? "Expirado"
                                : "Cancelado"}
                            </div>
                          </div>

                          <div className="flex space-x-2">
                            <button
                              onClick={() => shareReward(reward)}
                              className="p-2 text-blue-600 hover:bg-blue-100 rounded-full transition-colors"
                              title="Compartilhar"
                            >
                              <Share2 className="w-4 h-4" />
                            </button>

                            {reward.canBeUsed && (
                              <button
                                onClick={() => useReward(reward.id)}
                                className="px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors text-sm font-medium"
                              >
                                Usar Prêmio
                              </button>
                            )}
                          </div>
                        </div>
                      </div>
                    </motion.div>
                  );
                })}
              </div>
            )}
          </div>
        </motion.div>
      </motion.div>
    </AnimatePresence>
  );
};

export default RewardsPanel;
